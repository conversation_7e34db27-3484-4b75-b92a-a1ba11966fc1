'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { Server, buildAuthHeaders } from '../services/api';

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';

interface PlayerManagerProps {
  server: Server;
  onPlayerUpdated: () => void;
}

interface Player {
  uuid: string;
  username: string;
  lastSeen?: string;
  isOnline: boolean;
  isOp: boolean;
  isBanned: boolean;
  isWhitelisted: boolean;
}

interface PlayerAction {
  type: 'kick' | 'ban' | 'unban' | 'op' | 'deop' | 'whitelist' | 'unwhitelist';
  reason?: string;
}

export default function PlayerManager({ server, onPlayerUpdated }: PlayerManagerProps) {
  const [players, setPlayers] = useState<Player[]>([]);
  const [onlinePlayers, setOnlinePlayers] = useState<Player[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [newPlayerUsername, setNewPlayerUsername] = useState('');
  const [activeTab, setActiveTab] = useState<'online' | 'whitelist' | 'banned' | 'ops'>('online');
  const { getToken } = useAuth();

  useEffect(() => {
    fetchPlayers();
    // Refresh online players every 30 seconds
    const interval = setInterval(fetchOnlinePlayers, 30000);
    return () => clearInterval(interval);
  }, [server.id]);

  const fetchPlayers = async () => {
    try {
      const token = await getToken();
      const headers = buildAuthHeaders(token);
      const response = await fetch(`${API_URL}/minecraft/servers/${server.id}/players`, {
        headers
      });
      
      if (response.ok) {
        const data = await response.json();
        setPlayers(data.players || []);
        setOnlinePlayers(data.onlinePlayers || []);
      }
    } catch (err) {
      console.error('Error fetching players:', err);
    }
  };

  const fetchOnlinePlayers = async () => {
    try {
      const token = await getToken();
      const headers = buildAuthHeaders(token);
      const response = await fetch(`${API_URL}/minecraft/servers/${server.id}/players`, {
        headers
      });
      
      if (response.ok) {
        const data = await response.json();
        setOnlinePlayers(data.players || []);
      }
    } catch (err) {
      console.error('Error fetching online players:', err);
    }
  };

  const executePlayerAction = async (username: string, action: PlayerAction) => {
    setIsLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const token = await getToken();
      const headers = buildAuthHeaders(token);
      const response = await fetch(`${API_URL}/minecraft/servers/${server.id}/players/${username}/action`, {
        method: 'POST',
        headers,
        body: JSON.stringify(action)
      });

      if (response.ok) {
        const data = await response.json();
        setSuccess(data.message || `Action ${action.type} executed successfully`);
        fetchPlayers();
        onPlayerUpdated();
        setTimeout(() => setSuccess(null), 3000);
      } else {
        const errorData = await response.json();
        setError(errorData.error || `Failed to execute ${action.type}`);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : `Failed to execute ${action.type}`);
    } finally {
      setIsLoading(false);
    }
  };

  const addToWhitelist = async () => {
    if (!newPlayerUsername.trim()) {
      setError('Please enter a username');
      return;
    }

    await executePlayerAction(newPlayerUsername.trim(), { type: 'whitelist' });
    setNewPlayerUsername('');
  };

  const getFilteredPlayers = () => {
    switch (activeTab) {
      case 'online':
        return onlinePlayers;
      case 'whitelist':
        return players.filter(p => p.isWhitelisted);
      case 'banned':
        return players.filter(p => p.isBanned);
      case 'ops':
        return players.filter(p => p.isOp);
      default:
        return [];
    }
  };

  const tabs = [
    { id: 'online', label: 'Online Players', icon: '🟢', count: onlinePlayers.length },
    { id: 'whitelist', label: 'Whitelist', icon: '✅', count: players.filter(p => p.isWhitelisted).length },
    { id: 'banned', label: 'Banned', icon: '🚫', count: players.filter(p => p.isBanned).length },
    { id: 'ops', label: 'Operators', icon: '👑', count: players.filter(p => p.isOp).length }
  ] as const;

  return (
    <div className="card overflow-hidden fade-in-fast">
      <div className="px-6 lg:px-8 py-6 border-b border-white/10 bg-gradient-to-r from-indigo-500/10 to-purple-500/10">
        <h2 className="text-xl lg:text-2xl font-bold flex items-center gradient-text">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 lg:h-6 lg:w-6 mr-3 text-indigo-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
          </svg>
          Player Management
        </h2>
        <p className="text-gray-300 mt-2 font-light text-sm lg:text-base">Manage players, whitelist, and permissions</p>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-white/10">
        <nav className="flex space-x-0 overflow-x-auto">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center px-4 lg:px-6 py-4 text-sm font-medium border-b-2 transition-colors whitespace-nowrap ${
                activeTab === tab.id
                  ? 'border-indigo-500 text-indigo-400 bg-indigo-500/10'
                  : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'
              }`}
            >
              <span className="mr-2">{tab.icon}</span>
              {tab.label}
              <span className="ml-2 bg-white/10 text-xs px-2 py-1 rounded-full">{tab.count}</span>
            </button>
          ))}
        </nav>
      </div>

      <div className="p-6 lg:p-8">
        {/* Status Messages */}
        {error && (
          <div className="mb-6 p-4 bg-red-500/20 border border-red-500/30 rounded-lg text-red-400 flex items-center">
            <svg className="h-5 w-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
            {error}
          </div>
        )}

        {success && (
          <div className="mb-6 p-4 bg-green-500/20 border border-green-500/30 rounded-lg text-green-400 flex items-center">
            <svg className="h-5 w-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
            {success}
          </div>
        )}

        {/* Add to Whitelist */}
        {activeTab === 'whitelist' && (
          <div className="mb-6 p-4 bg-white/5 rounded-lg">
            <h3 className="text-lg font-semibold text-white mb-4">Add Player to Whitelist</h3>
            <div className="flex gap-4">
              <input
                type="text"
                className="input-field flex-1"
                placeholder="Enter username"
                value={newPlayerUsername}
                onChange={(e) => setNewPlayerUsername(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && addToWhitelist()}
              />
              <button
                onClick={addToWhitelist}
                disabled={isLoading || !newPlayerUsername.trim()}
                className="bg-indigo-500 hover:bg-indigo-600 disabled:bg-gray-600 text-white px-6 py-2 rounded-lg font-semibold transition-all duration-300 disabled:cursor-not-allowed"
              >
                Add
              </button>
            </div>
          </div>
        )}

        {/* Player List */}
        <div className="space-y-3">
          {getFilteredPlayers().length === 0 ? (
            <div className="text-center py-8">
              <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br from-indigo-500/20 to-purple-500/20 flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-indigo-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                </svg>
              </div>
              <h4 className="text-lg font-semibold text-white mb-2">No players found</h4>
              <p className="text-gray-400 font-light">
                {activeTab === 'online' && 'No players are currently online.'}
                {activeTab === 'whitelist' && 'No players in whitelist.'}
                {activeTab === 'banned' && 'No banned players.'}
                {activeTab === 'ops' && 'No operators assigned.'}
              </p>
            </div>
          ) : (
            getFilteredPlayers().map((player) => (
              <div key={player.uuid} className="bg-white/5 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-500 rounded-lg flex items-center justify-center">
                      <span className="text-white font-bold text-lg">
                        {player.username.charAt(0).toUpperCase()}
                      </span>
                    </div>
                    <div>
                      <h4 className="text-white font-medium flex items-center">
                        {player.username}
                        {player.isOnline && <span className="ml-2 w-2 h-2 bg-green-400 rounded-full"></span>}
                        {player.isOp && <span className="ml-2 text-yellow-400">👑</span>}
                      </h4>
                      <p className="text-gray-400 text-sm">
                        {player.isOnline ? 'Online' : player.lastSeen ? `Last seen: ${new Date(player.lastSeen).toLocaleDateString()}` : 'Never seen'}
                      </p>
                    </div>
                  </div>

                  <div className="flex gap-2">
                    {activeTab === 'online' && (
                      <>
                        <button
                          onClick={() => executePlayerAction(player.username, { type: 'kick', reason: 'Kicked by admin' })}
                          disabled={isLoading}
                          className="bg-yellow-500/20 hover:bg-yellow-500/30 border border-yellow-500/30 hover:border-yellow-500/50 text-yellow-400 hover:text-yellow-300 px-3 py-2 rounded-lg text-sm font-semibold transition-all duration-300 hover:scale-105"
                        >
                          Kick
                        </button>
                        <button
                          onClick={() => executePlayerAction(player.username, { type: 'ban', reason: 'Banned by admin' })}
                          disabled={isLoading}
                          className="bg-red-500/20 hover:bg-red-500/30 border border-red-500/30 hover:border-red-500/50 text-red-400 hover:text-red-300 px-3 py-2 rounded-lg text-sm font-semibold transition-all duration-300 hover:scale-105"
                        >
                          Ban
                        </button>
                      </>
                    )}

                    {activeTab === 'whitelist' && (
                      <button
                        onClick={() => executePlayerAction(player.username, { type: 'unwhitelist' })}
                        disabled={isLoading}
                        className="bg-red-500/20 hover:bg-red-500/30 border border-red-500/30 hover:border-red-500/50 text-red-400 hover:text-red-300 px-3 py-2 rounded-lg text-sm font-semibold transition-all duration-300 hover:scale-105"
                      >
                        Remove
                      </button>
                    )}

                    {activeTab === 'banned' && (
                      <button
                        onClick={() => executePlayerAction(player.username, { type: 'unban' })}
                        disabled={isLoading}
                        className="bg-green-500/20 hover:bg-green-500/30 border border-green-500/30 hover:border-green-500/50 text-green-400 hover:text-green-300 px-3 py-2 rounded-lg text-sm font-semibold transition-all duration-300 hover:scale-105"
                      >
                        Unban
                      </button>
                    )}

                    {activeTab === 'ops' && (
                      <button
                        onClick={() => executePlayerAction(player.username, { type: 'deop' })}
                        disabled={isLoading}
                        className="bg-red-500/20 hover:bg-red-500/30 border border-red-500/30 hover:border-red-500/50 text-red-400 hover:text-red-300 px-3 py-2 rounded-lg text-sm font-semibold transition-all duration-300 hover:scale-105"
                      >
                        Remove OP
                      </button>
                    )}

                    {!player.isOp && activeTab !== 'ops' && (
                      <button
                        onClick={() => executePlayerAction(player.username, { type: 'op' })}
                        disabled={isLoading}
                        className="bg-yellow-500/20 hover:bg-yellow-500/30 border border-yellow-500/30 hover:border-yellow-500/50 text-yellow-400 hover:text-yellow-300 px-3 py-2 rounded-lg text-sm font-semibold transition-all duration-300 hover:scale-105"
                      >
                        Make OP
                      </button>
                    )}
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
}
