package com.blocksconnect;

import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;
import net.minecraftforge.fml.event.lifecycle.FMLClientSetupEvent;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * Client-side mod class for BlocksConnect integration
 * Handles client-specific initialization (minimal for this server-focused mod)
 */
@Mod.EventBusSubscriber(modid = BlocksConnectMod.MOD_ID, bus = Mod.EventBusSubscriber.Bus.MOD, value = Dist.CLIENT)
public class BlocksConnectModClient {
    private static final Logger LOGGER = LogManager.getLogger("blocksconnect-client");
    
    @SubscribeEvent
    public static void onClientSetup(FMLClientSetupEvent event) {
        LOGGER.info("BlocksConnect client-side setup complete");
        // Client-side initialization if needed
        // This mod is primarily server-side, so minimal client setup required
    }
}
