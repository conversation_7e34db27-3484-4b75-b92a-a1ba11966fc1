@echo off
echo Building BlocksConnect Forge Mod...
echo.

REM Check if Gradle wrapper exists
if not exist "gradle\wrapper\gradle-wrapper.jar" (
    echo ERROR: Gradle wrapper not found!
    echo Please run init-gradle-wrapper.bat first to initialize the Gradle wrapper.
    echo.
    pause
    exit /b 1
)

echo Cleaning previous builds...
call gradlew.bat clean

echo.
echo Building mod...
call gradlew.bat build

echo.
if exist "build\libs\blocksconnect-forge-mod-1.0.0.jar" (
    echo ✅ Build successful!
    echo Mod file: build\libs\blocksconnect-forge-mod-1.0.0.jar
    echo.
    echo 📦 Installation Instructions:
    echo 1. Copy build\libs\blocksconnect-forge-mod-1.0.0.jar to your server's mods folder
    echo 2. Start your server to generate the configuration file
    echo 3. Edit config\blocksconnect.json with your server details
    echo 4. Restart your server
) else (
    echo ❌ Build failed!
    echo Check the output above for errors.
    echo.
    echo Common issues:
    echo - Java 21 not installed or not in PATH
    echo - Internet connection required for dependencies
    echo - Gradle wrapper not properly initialized
    exit /b 1
)

echo.
pause
