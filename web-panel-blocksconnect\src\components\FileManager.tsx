'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { Server, buildAuthHeaders } from '../services/api';

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';

interface FileManagerProps {
  server: Server;
}

interface FileItem {
  name: string;
  type: 'file' | 'directory';
  size: number;
  modified: string;
  path: string;
  permissions: string;
}

interface BreadcrumbItem {
  name: string;
  path: string;
}

export default function FileManager({ server }: FileManagerProps) {
  const [files, setFiles] = useState<FileItem[]>([]);
  const [currentPath, setCurrentPath] = useState('/');
  const [breadcrumbs, setBreadcrumbs] = useState<BreadcrumbItem[]>([{ name: 'Root', path: '/' }]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [selectedFile, setSelectedFile] = useState<FileItem | null>(null);
  const [showUpload, setShowUpload] = useState(false);
  const [uploadFile, setUploadFile] = useState<File | null>(null);
  const [newFolderName, setNewFolderName] = useState('');
  const [showNewFolder, setShowNewFolder] = useState(false);
  const { getToken } = useAuth();

  useEffect(() => {
    fetchFiles(currentPath);
  }, [currentPath, server.id]);

  const fetchFiles = async (path: string) => {
    setIsLoading(true);
    setError(null);

    try {
      const token = await getToken();
      const headers = buildAuthHeaders(token);
      const response = await fetch(`${API_URL}/minecraft/servers/${server.id}/files?path=${encodeURIComponent(path)}`, {
        headers
      });
      
      if (response.ok) {
        const data = await response.json();
        setFiles(data.files || []);
        updateBreadcrumbs(path);
      } else {
        setError('Failed to load files');
      }
    } catch (err) {
      setError('Failed to load files');
    } finally {
      setIsLoading(false);
    }
  };

  const updateBreadcrumbs = (path: string) => {
    const parts = path.split('/').filter(part => part !== '');
    const breadcrumbs: BreadcrumbItem[] = [{ name: 'Root', path: '/' }];
    
    let currentPath = '';
    parts.forEach(part => {
      currentPath += '/' + part;
      breadcrumbs.push({ name: part, path: currentPath });
    });
    
    setBreadcrumbs(breadcrumbs);
  };

  const navigateToPath = (path: string) => {
    setCurrentPath(path);
    setSelectedFile(null);
  };

  const handleFileClick = (file: FileItem) => {
    if (file.type === 'directory') {
      navigateToPath(file.path);
    } else {
      setSelectedFile(file);
    }
  };

  const downloadFile = async (file: FileItem) => {
    try {
      const token = await getToken();
      const headers = buildAuthHeaders(token);
      const response = await fetch(`${API_URL}/minecraft/servers/${server.id}/files/download?path=${encodeURIComponent(file.path)}`, {
        headers
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = file.name;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
      } else {
        setError('Failed to download file');
      }
    } catch (err) {
      setError('Failed to download file');
    }
  };

  const deleteFile = async (file: FileItem) => {
    if (!confirm(`Are you sure you want to delete ${file.name}?`)) return;

    try {
      const token = await getToken();
      const headers = buildAuthHeaders(token);
      const response = await fetch(`${API_URL}/minecraft/servers/${server.id}/files?path=${encodeURIComponent(file.path)}`, {
        method: 'DELETE',
        headers
      });

      if (response.ok) {
        setSuccess(`${file.name} deleted successfully`);
        fetchFiles(currentPath);
        setTimeout(() => setSuccess(null), 3000);
      } else {
        setError('Failed to delete file');
      }
    } catch (err) {
      setError('Failed to delete file');
    }
  };

  const uploadFileToServer = async () => {
    if (!uploadFile) return;

    const formData = new FormData();
    formData.append('file', uploadFile);
    formData.append('path', currentPath);

    try {
      const token = await getToken();
      const response = await fetch(`${API_URL}/minecraft/servers/${server.id}/files/upload`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: formData
      });

      if (response.ok) {
        setSuccess('File uploaded successfully');
        setShowUpload(false);
        setUploadFile(null);
        fetchFiles(currentPath);
        setTimeout(() => setSuccess(null), 3000);
      } else {
        setError('Failed to upload file');
      }
    } catch (err) {
      setError('Failed to upload file');
    }
  };

  const createFolder = async () => {
    if (!newFolderName.trim()) return;

    try {
      const token = await getToken();
      const headers = buildAuthHeaders(token);
      const response = await fetch(`${API_URL}/minecraft/servers/${server.id}/files/folder`, {
        method: 'POST',
        headers,
        body: JSON.stringify({
          path: currentPath,
          name: newFolderName.trim()
        })
      });

      if (response.ok) {
        setSuccess('Folder created successfully');
        setShowNewFolder(false);
        setNewFolderName('');
        fetchFiles(currentPath);
        setTimeout(() => setSuccess(null), 3000);
      } else {
        setError('Failed to create folder');
      }
    } catch (err) {
      setError('Failed to create folder');
    }
  };

  const formatFileSize = (bytes: number) => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const getFileIcon = (file: FileItem) => {
    if (file.type === 'directory') {
      return (
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-5l-2-2H5a2 2 0 00-2 2z" />
        </svg>
      );
    }

    const extension = file.name.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'txt':
      case 'log':
      case 'properties':
      case 'yml':
      case 'yaml':
      case 'json':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
        );
      case 'jar':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-orange-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
          </svg>
        );
      default:
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
          </svg>
        );
    }
  };

  return (
    <div className="card overflow-hidden fade-in-fast">
      <div className="px-6 lg:px-8 py-6 border-b border-white/10 bg-gradient-to-r from-orange-500/10 to-red-500/10">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl lg:text-2xl font-bold flex items-center gradient-text">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 lg:h-6 lg:w-6 mr-3 text-orange-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-5l-2-2H5a2 2 0 00-2 2z" />
              </svg>
              File Manager
            </h2>
            <p className="text-gray-300 mt-2 font-light text-sm lg:text-base">Browse and manage server files</p>
          </div>
          <div className="flex gap-2">
            <button
              onClick={() => setShowNewFolder(true)}
              className="bg-blue-500/20 hover:bg-blue-500/30 border border-blue-500/30 hover:border-blue-500/50 text-blue-400 hover:text-blue-300 px-3 py-2 rounded-lg text-sm font-semibold transition-all duration-300 hover:scale-105"
            >
              New Folder
            </button>
            <button
              onClick={() => setShowUpload(true)}
              className="bg-green-500/20 hover:bg-green-500/30 border border-green-500/30 hover:border-green-500/50 text-green-400 hover:text-green-300 px-3 py-2 rounded-lg text-sm font-semibold transition-all duration-300 hover:scale-105"
            >
              Upload
            </button>
          </div>
        </div>
      </div>

      <div className="p-6 lg:p-8">
        {/* Status Messages */}
        {error && (
          <div className="mb-6 p-4 bg-red-500/20 border border-red-500/30 rounded-lg text-red-400 flex items-center">
            <svg className="h-5 w-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
            {error}
          </div>
        )}

        {success && (
          <div className="mb-6 p-4 bg-green-500/20 border border-green-500/30 rounded-lg text-green-400 flex items-center">
            <svg className="h-5 w-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
            {success}
          </div>
        )}

        {/* Breadcrumbs */}
        <div className="mb-6">
          <nav className="flex" aria-label="Breadcrumb">
            <ol className="flex items-center space-x-2">
              {breadcrumbs.map((crumb, index) => (
                <li key={index} className="flex items-center">
                  {index > 0 && (
                    <svg className="h-4 w-4 text-gray-400 mx-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                    </svg>
                  )}
                  <button
                    onClick={() => navigateToPath(crumb.path)}
                    className={`text-sm font-medium transition-colors ${
                      index === breadcrumbs.length - 1
                        ? 'text-white cursor-default'
                        : 'text-gray-400 hover:text-white'
                    }`}
                  >
                    {crumb.name}
                  </button>
                </li>
              ))}
            </ol>
          </nav>
        </div>

        {/* Upload Modal */}
        {showUpload && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <div className="bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4">
              <h3 className="text-lg font-semibold text-white mb-4">Upload File</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-gray-200 text-sm font-semibold mb-2">Select File</label>
                  <input
                    type="file"
                    onChange={(e) => setUploadFile(e.target.files?.[0] || null)}
                    className="w-full text-gray-300 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-semibold file:bg-blue-500 file:text-white hover:file:bg-blue-600"
                  />
                </div>
                <div className="flex gap-4">
                  <button
                    onClick={uploadFileToServer}
                    disabled={!uploadFile}
                    className="flex-1 bg-green-500 hover:bg-green-600 disabled:bg-gray-600 text-white px-4 py-2 rounded-lg font-semibold transition-all duration-300 disabled:cursor-not-allowed"
                  >
                    Upload
                  </button>
                  <button
                    onClick={() => {
                      setShowUpload(false);
                      setUploadFile(null);
                    }}
                    className="flex-1 bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-semibold transition-all duration-300"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* New Folder Modal */}
        {showNewFolder && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <div className="bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4">
              <h3 className="text-lg font-semibold text-white mb-4">Create New Folder</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-gray-200 text-sm font-semibold mb-2">Folder Name</label>
                  <input
                    type="text"
                    className="input-field w-full"
                    placeholder="Enter folder name"
                    value={newFolderName}
                    onChange={(e) => setNewFolderName(e.target.value)}
                    onKeyPress={(e) => e.key === 'Enter' && createFolder()}
                  />
                </div>
                <div className="flex gap-4">
                  <button
                    onClick={createFolder}
                    disabled={!newFolderName.trim()}
                    className="flex-1 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-600 text-white px-4 py-2 rounded-lg font-semibold transition-all duration-300 disabled:cursor-not-allowed"
                  >
                    Create
                  </button>
                  <button
                    onClick={() => {
                      setShowNewFolder(false);
                      setNewFolderName('');
                    }}
                    className="flex-1 bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-semibold transition-all duration-300"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* File List */}
        {isLoading ? (
          <div className="text-center py-8">
            <div className="animate-spin h-8 w-8 border-4 border-orange-500/30 border-t-orange-500 rounded-full mx-auto mb-4"></div>
            <p className="text-gray-400">Loading files...</p>
          </div>
        ) : files.length === 0 ? (
          <div className="text-center py-8">
            <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br from-orange-500/20 to-red-500/20 flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-orange-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-5l-2-2H5a2 2 0 00-2 2z" />
              </svg>
            </div>
            <h4 className="text-lg font-semibold text-white mb-2">Empty Directory</h4>
            <p className="text-gray-400 font-light">This directory contains no files or folders.</p>
          </div>
        ) : (
          <div className="space-y-2">
            {files.map((file, index) => (
              <div
                key={index}
                className="bg-white/5 hover:bg-white/10 rounded-lg p-4 transition-colors cursor-pointer"
                onClick={() => handleFileClick(file)}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    {getFileIcon(file)}
                    <div>
                      <h4 className="text-white font-medium">{file.name}</h4>
                      <p className="text-gray-400 text-sm">
                        {file.type === 'file' ? formatFileSize(file.size) : 'Directory'} • 
                        Modified {new Date(file.modified).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                  
                  {file.type === 'file' && (
                    <div className="flex gap-2" onClick={(e) => e.stopPropagation()}>
                      <button
                        onClick={() => downloadFile(file)}
                        className="bg-blue-500/20 hover:bg-blue-500/30 border border-blue-500/30 hover:border-blue-500/50 text-blue-400 hover:text-blue-300 px-3 py-2 rounded-lg text-sm font-semibold transition-all duration-300 hover:scale-105"
                      >
                        Download
                      </button>
                      <button
                        onClick={() => deleteFile(file)}
                        className="bg-red-500/20 hover:bg-red-500/30 border border-red-500/30 hover:border-red-500/50 text-red-400 hover:text-red-300 px-3 py-2 rounded-lg text-sm font-semibold transition-all duration-300 hover:scale-105"
                      >
                        Delete
                      </button>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
