Java Launcher: C:\Program Files\Java\jdk-21\bin\java.exe
Arguments: '--clean, C:\Users\<USER>\.gradle\caches\forge_gradle\mcp_repo\net\minecraft\joined\1.21.1-20240808.132146\joined-1.21.1-20240808.132146-srg.jar, --output, C:\Users\<USER>\.gradle\caches\forge_gradle\minecraft_user_repo\net\minecraftforge\forge\1.21.1-52.0.17\forge-1.21.1-52.0.17-binpatched.jar, --apply, C:\Users\<USER>\.gradle\caches\forge_gradle\minecraft_user_repo\net\minecraftforge\forge\1.21.1-52.0.17\forge-1.21.1-52.0.17-binpatches.lzma'
Classpath:
 - C:\Users\<USER>\.gradle\caches\forge_gradle\maven_downloader\net\minecraftforge\binarypatcher\1.2.0\binarypatcher-1.2.0-fatjar.jar
Working directory: C:\Users\<USER>\Documents\BlocksConnect\minecraft-forge-mod-blocksconnect\build\_applyBinpatches_2
Main class: net.minecraftforge.binarypatcher.ConsoleTool
====================================
Applying: 
  Clean:     C:\Users\<USER>\.gradle\caches\forge_gradle\mcp_repo\net\minecraft\joined\1.21.1-20240808.132146\joined-1.21.1-20240808.132146-srg.jar
  Output:    C:\Users\<USER>\.gradle\caches\forge_gradle\minecraft_user_repo\net\minecraftforge\forge\1.21.1-52.0.17\forge-1.21.1-52.0.17-binpatched.jar
  KeepData:  false
  Unpatched: false
  Pack200:   false
  Legacy:    false
Loading patches file: C:\Users\<USER>\.gradle\caches\forge_gradle\minecraft_user_repo\net\minecraftforge\forge\1.21.1-52.0.17\forge-1.21.1-52.0.17-binpatches.lzma
  Reading patch com.mojang.blaze3d.pipeline.RenderTarget.binpatch
    Checksum: d3630847 Exists: true
  Reading patch com.mojang.blaze3d.platform.GlStateManager$BlendState.binpatch
    Checksum: 48c0e9a8 Exists: true
  Reading patch com.mojang.blaze3d.platform.GlStateManager$BooleanState.binpatch
    Checksum: ccf3028c Exists: true
  Reading patch com.mojang.blaze3d.platform.GlStateManager$ColorLogicState.binpatch
    Checksum: a046e344 Exists: true
  Reading patch com.mojang.blaze3d.platform.GlStateManager$ColorMask.binpatch
    Checksum: 7291aff0 Exists: true
  Reading patch com.mojang.blaze3d.platform.GlStateManager$CullState.binpatch
    Checksum: c941db3a Exists: true
  Reading patch com.mojang.blaze3d.platform.GlStateManager$DepthState.binpatch
    Checksum: 16b0e153 Exists: true
  Reading patch com.mojang.blaze3d.platform.GlStateManager$DestFactor.binpatch
    Checksum: 2843a3a Exists: true
  Reading patch com.mojang.blaze3d.platform.GlStateManager$LogicOp.binpatch
    Checksum: 1b2b1138 Exists: true
  Reading patch com.mojang.blaze3d.platform.GlStateManager$PolygonOffsetState.binpatch
    Checksum: ba99ee6b Exists: true
  Reading patch com.mojang.blaze3d.platform.GlStateManager$ScissorState.binpatch
    Checksum: 72fcda9b Exists: true
  Reading patch com.mojang.blaze3d.platform.GlStateManager$SourceFactor.binpatch
    Checksum: c5d14dd2 Exists: true
  Reading patch com.mojang.blaze3d.platform.GlStateManager$StencilFunc.binpatch
    Checksum: defac20 Exists: true
  Reading patch com.mojang.blaze3d.platform.GlStateManager$StencilState.binpatch
    Checksum: a0d2ea51 Exists: true
  Reading patch com.mojang.blaze3d.platform.GlStateManager$TextureState.binpatch
    Checksum: 1f1ea554 Exists: true
  Reading patch com.mojang.blaze3d.platform.GlStateManager$Viewport.binpatch
    Checksum: 3fc4838e Exists: true
  Reading patch com.mojang.blaze3d.platform.GlStateManager.binpatch
    Checksum: b056e21 Exists: true
  Reading patch com.mojang.blaze3d.platform.Window$WindowInitFailed.binpatch
    Checksum: c8a1b858 Exists: true
  Reading patch com.mojang.blaze3d.platform.Window.binpatch
    Checksum: fec5c9db Exists: true
  Reading patch com.mojang.blaze3d.vertex.PoseStack$Pose.binpatch
    Checksum: 6197e0f0 Exists: true
  Reading patch com.mojang.blaze3d.vertex.PoseStack.binpatch
    Checksum: 9c3f6d1f Exists: true
  Reading patch com.mojang.blaze3d.vertex.SheetedDecalTextureGenerator.binpatch
    Checksum: 32b41e89 Exists: true
  Reading patch com.mojang.blaze3d.vertex.VertexConsumer.binpatch
    Checksum: b02f0f3a Exists: true
  Reading patch com.mojang.blaze3d.vertex.VertexFormat$Builder.binpatch
    Checksum: 159c4e7d Exists: true
  Reading patch com.mojang.blaze3d.vertex.VertexFormat$IndexType.binpatch
    Checksum: 6bb4a812 Exists: true
  Reading patch com.mojang.blaze3d.vertex.VertexFormat$Mode.binpatch
    Checksum: 996cfb69 Exists: true
  Reading patch com.mojang.blaze3d.vertex.VertexFormat.binpatch
    Checksum: 45ec36af Exists: true
  Reading patch com.mojang.math.Transformation.binpatch
    Checksum: f23cf67 Exists: true
  Reading patch com.mojang.realmsclient.gui.screens.RealmsGenericErrorScreen$ErrorMessage.binpatch
    Checksum: d36f2d61 Exists: true
  Reading patch com.mojang.realmsclient.gui.screens.RealmsGenericErrorScreen.binpatch
    Checksum: 7340a6b8 Exists: true
  Reading patch net.minecraft.CrashReport.binpatch
    Checksum: 14eadb61 Exists: true
  Reading patch net.minecraft.CrashReportCategory$Entry.binpatch
    Checksum: 76089b82 Exists: true
  Reading patch net.minecraft.CrashReportCategory.binpatch
    Checksum: 10fd6088 Exists: true
  Reading patch net.minecraft.SharedConstants.binpatch
    Checksum: 68820113 Exists: true
  Reading patch net.minecraft.Util$1.binpatch
    Checksum: 327492db Exists: true
  Reading patch net.minecraft.Util$10.binpatch
    Checksum: ba65da1e Exists: true
  Reading patch net.minecraft.Util$11.binpatch
    Checksum: 51241440 Exists: true
  Reading patch net.minecraft.Util$2.binpatch
    Checksum: 1da00543 Exists: true
  Reading patch net.minecraft.Util$3.binpatch
    Checksum: 0 Exists: false
  Reading patch net.minecraft.Util$4.binpatch
    Checksum: 0 Exists: false
  Reading patch net.minecraft.Util$5.binpatch
    Checksum: d6b0e0de Exists: true
  Reading patch net.minecraft.Util$6.binpatch
    Checksum: df28a480 Exists: true
  Reading patch net.minecraft.Util$7.binpatch
    Checksum: 36017a7a Exists: true
  Reading patch net.minecraft.Util$8.binpatch
    Checksum: 11e5744a Exists: true
  Reading patch net.minecraft.Util$9.binpatch
    Checksum: b425bf0f Exists: true
  Reading patch net.minecraft.Util$OS$1.binpatch
    Checksum: 96d9b0df Exists: true
  Reading patch net.minecraft.Util$OS$2.binpatch
    Checksum: b36ba3e3 Exists: true
  Reading patch net.minecraft.Util$OS.binpatch
    Checksum: 1ad29638 Exists: true
  Reading patch net.minecraft.Util.binpatch
    Checksum: 8d557258 Exists: true
  Reading patch net.minecraft.advancements.Advancement$Builder.binpatch
    Checksum: 2c4e218a Exists: true
  Reading patch net.minecraft.advancements.Advancement.binpatch
    Checksum: e42c8c9c Exists: true
  Reading patch net.minecraft.advancements.AdvancementRewards$Builder.binpatch
    Checksum: 44a32bb5 Exists: true
  Reading patch net.minecraft.advancements.AdvancementRewards.binpatch
    Checksum: 2baa6211 Exists: true
  Reading patch net.minecraft.client.Camera$NearPlane.binpatch
    Checksum: fb3061ac Exists: true
  Reading patch net.minecraft.client.Camera.binpatch
    Checksum: d75b8352 Exists: true
  Reading patch net.minecraft.client.ClientBrandRetriever.binpatch
    Checksum: 5d57b89e Exists: true
  Reading patch net.minecraft.client.ClientRecipeBook$1.binpatch
    Checksum: f5285c79 Exists: true
  Reading patch net.minecraft.client.ClientRecipeBook.binpatch
    Checksum: f5e039af Exists: true
  Reading patch net.minecraft.client.DeltaTracker$DefaultValue.binpatch
    Checksum: 9d0b719 Exists: true
  Reading patch net.minecraft.client.DeltaTracker$Timer.binpatch
    Checksum: a63cac39 Exists: true
  Reading patch net.minecraft.client.DeltaTracker.binpatch
    Checksum: 423fb50e Exists: true
  Reading patch net.minecraft.client.KeyMapping.binpatch
    Checksum: 30e91e7c Exists: true
  Reading patch net.minecraft.client.KeyboardHandler$1.binpatch
    Checksum: 1129e742 Exists: true
  Reading patch net.minecraft.client.KeyboardHandler.binpatch
    Checksum: b3b70948 Exists: true
  Reading patch net.minecraft.client.Minecraft$1.binpatch
    Checksum: 6bd2e80c Exists: true
  Reading patch net.minecraft.client.Minecraft$ChatStatus$1.binpatch
    Checksum: f04cdb7c Exists: true
  Reading patch net.minecraft.client.Minecraft$ChatStatus$2.binpatch
    Checksum: 23edb8c Exists: true
  Reading patch net.minecraft.client.Minecraft$ChatStatus$3.binpatch
    Checksum: 1d06dbb5 Exists: true
  Reading patch net.minecraft.client.Minecraft$ChatStatus$4.binpatch
    Checksum: 1ea3dbbd Exists: true
  Reading patch net.minecraft.client.Minecraft$ChatStatus.binpatch
    Checksum: 31c7065 Exists: true
  Reading patch net.minecraft.client.Minecraft$GameLoadCookie.binpatch
    Checksum: 74ec4a49 Exists: true
  Reading patch net.minecraft.client.Minecraft.binpatch
    Checksum: 73f18441 Exists: true
  Reading patch net.minecraft.client.MouseHandler.binpatch
    Checksum: 7c51ddfe Exists: true
  Reading patch net.minecraft.client.Options$1.binpatch
    Checksum: 2bbcae6e Exists: true
  Reading patch net.minecraft.client.Options$2.binpatch
    Checksum: 31486ea Exists: true
  Reading patch net.minecraft.client.Options$3.binpatch
    Checksum: 7484e15e Exists: true
  Reading patch net.minecraft.client.Options$4.binpatch
    Checksum: fd66a2bb Exists: true
  Reading patch net.minecraft.client.Options$5.binpatch
    Checksum: 79852baf Exists: true
  Reading patch net.minecraft.client.Options$FieldAccess.binpatch
    Checksum: 3ddb1c35 Exists: true
  Reading patch net.minecraft.client.Options$OptionAccess.binpatch
    Checksum: 49e5a4c5 Exists: true
  Reading patch net.minecraft.client.Options.binpatch
    Checksum: 53ab92d0 Exists: true
  Reading patch net.minecraft.client.RecipeBookCategories$1.binpatch
    Checksum: 7522f66f Exists: true
  Reading patch net.minecraft.client.RecipeBookCategories.binpatch
    Checksum: d81d714f Exists: true
  Reading patch net.minecraft.client.Screenshot.binpatch
    Checksum: 276e3c8e Exists: true
  Reading patch net.minecraft.client.ToggleKeyMapping.binpatch
    Checksum: f75b1d Exists: true
  Reading patch net.minecraft.client.color.block.BlockColors.binpatch
    Checksum: 7d6a30c6 Exists: true
  Reading patch net.minecraft.client.color.item.ItemColors.binpatch
    Checksum: 8f3b4611 Exists: true
  Reading patch net.minecraft.client.gui.Font$DisplayMode.binpatch
    Checksum: 755e563e Exists: true
  Reading patch net.minecraft.client.gui.Font$StringRenderOutput.binpatch
    Checksum: d8ee86fd Exists: true
  Reading patch net.minecraft.client.gui.Font.binpatch
    Checksum: eb6080d1 Exists: true
  Reading patch net.minecraft.client.gui.Gui$1DisplayEntry.binpatch
    Checksum: c55d171f Exists: true
  Reading patch net.minecraft.client.gui.Gui$HeartType.binpatch
    Checksum: e62acecf Exists: true
  Reading patch net.minecraft.client.gui.Gui.binpatch
    Checksum: 2d5259b5 Exists: true
  Reading patch net.minecraft.client.gui.GuiGraphics$ScissorStack.binpatch
    Checksum: 8ba6087d Exists: true
  Reading patch net.minecraft.client.gui.GuiGraphics.binpatch
    Checksum: f61e97ef Exists: true
  Reading patch net.minecraft.client.gui.components.AbstractButton.binpatch
    Checksum: 9fd96404 Exists: true
  Reading patch net.minecraft.client.gui.components.AbstractWidget.binpatch
    Checksum: 19355f19 Exists: true
  Reading patch net.minecraft.client.gui.components.BossHealthOverlay$1.binpatch
    Checksum: 72ab8cb0 Exists: true
  Reading patch net.minecraft.client.gui.components.BossHealthOverlay.binpatch
    Checksum: e2a25e1c Exists: true
  Reading patch net.minecraft.client.gui.components.Button$Builder.binpatch
    Checksum: 628cf52a Exists: true
  Reading patch net.minecraft.client.gui.components.Button$CreateNarration.binpatch
    Checksum: 2c89c9da Exists: true
  Reading patch net.minecraft.client.gui.components.Button$OnPress.binpatch
    Checksum: c42f8492 Exists: true
  Reading patch net.minecraft.client.gui.components.Button.binpatch
    Checksum: 4382b5d2 Exists: true
  Reading patch net.minecraft.client.gui.components.DebugScreenOverlay$1.binpatch
    Checksum: 941be7b8 Exists: true
  Reading patch net.minecraft.client.gui.components.DebugScreenOverlay$AllocationRateCalculator.binpatch
    Checksum: c3a01830 Exists: true
  Reading patch net.minecraft.client.gui.components.DebugScreenOverlay.binpatch
    Checksum: fcb2b732 Exists: true
  Reading patch net.minecraft.client.gui.components.toasts.ToastComponent$ToastInstance.binpatch
    Checksum: e2a496d3 Exists: true
  Reading patch net.minecraft.client.gui.components.toasts.ToastComponent.binpatch
    Checksum: 87117b4f Exists: true
  Reading patch net.minecraft.client.gui.screens.ChatScreen$1.binpatch
    Checksum: d40cf93 Exists: true
  Reading patch net.minecraft.client.gui.screens.ChatScreen.binpatch
    Checksum: c0fd5c1 Exists: true
  Reading patch net.minecraft.client.gui.screens.ConnectScreen$1.binpatch
    Checksum: fc56ab25 Exists: true
  Reading patch net.minecraft.client.gui.screens.ConnectScreen$2.binpatch
    Checksum: aa871992 Exists: true
  Reading patch net.minecraft.client.gui.screens.ConnectScreen.binpatch
    Checksum: 4543b06 Exists: true
  Reading patch net.minecraft.client.gui.screens.LoadingOverlay$LogoTexture.binpatch
    Checksum: 4edc8031 Exists: true
  Reading patch net.minecraft.client.gui.screens.LoadingOverlay.binpatch
    Checksum: 3982f778 Exists: true
  Reading patch net.minecraft.client.gui.screens.MenuScreens$ScreenConstructor.binpatch
    Checksum: 4db91a94 Exists: true
  Reading patch net.minecraft.client.gui.screens.MenuScreens.binpatch
    Checksum: e00e7b11 Exists: true
  Reading patch net.minecraft.client.gui.screens.PauseScreen$FeedbackSubScreen.binpatch
    Checksum: 5a64c76 Exists: true
  Reading patch net.minecraft.client.gui.screens.PauseScreen.binpatch
    Checksum: f91c8efe Exists: true
  Reading patch net.minecraft.client.gui.screens.Screen$DeferredTooltipRendering.binpatch
    Checksum: e2a4bc70 Exists: true
  Reading patch net.minecraft.client.gui.screens.Screen$NarratableSearchResult.binpatch
    Checksum: a5e6586d Exists: true
  Reading patch net.minecraft.client.gui.screens.Screen.binpatch
    Checksum: bc89101d Exists: true
  Reading patch net.minecraft.client.gui.screens.TitleScreen.binpatch
    Checksum: 7bab15af Exists: true
  Reading patch net.minecraft.client.gui.screens.advancements.AdvancementTab.binpatch
    Checksum: ffea7d24 Exists: true
  Reading patch net.minecraft.client.gui.screens.advancements.AdvancementTabType$Sprites.binpatch
    Checksum: b27c4fee Exists: true
  Reading patch net.minecraft.client.gui.screens.advancements.AdvancementTabType.binpatch
    Checksum: 5ec1f36 Exists: true
  Reading patch net.minecraft.client.gui.screens.advancements.AdvancementsScreen.binpatch
    Checksum: 1d59a4a7 Exists: true
  Reading patch net.minecraft.client.gui.screens.inventory.AbstractContainerScreen.binpatch
    Checksum: e84953b3 Exists: true
  Reading patch net.minecraft.client.gui.screens.inventory.CreativeModeInventoryScreen$CustomCreativeSlot.binpatch
    Checksum: e39c0964 Exists: true
  Reading patch net.minecraft.client.gui.screens.inventory.CreativeModeInventoryScreen$ItemPickerMenu.binpatch
    Checksum: 27ec9a5d Exists: true
  Reading patch net.minecraft.client.gui.screens.inventory.CreativeModeInventoryScreen$SlotWrapper.binpatch
    Checksum: dce2ad12 Exists: true
  Reading patch net.minecraft.client.gui.screens.inventory.CreativeModeInventoryScreen.binpatch
    Checksum: 9eb314d3 Exists: true
  Reading patch net.minecraft.client.gui.screens.inventory.EffectRenderingInventoryScreen.binpatch
    Checksum: bc436c29 Exists: true
  Reading patch net.minecraft.client.gui.screens.inventory.EnchantmentScreen.binpatch
    Checksum: 1f08378d Exists: true
  Reading patch net.minecraft.client.gui.screens.inventory.HangingSignEditScreen.binpatch
    Checksum: 6c314c4b Exists: true
  Reading patch net.minecraft.client.gui.screens.inventory.MerchantScreen$TradeOfferButton.binpatch
    Checksum: 4e419ed1 Exists: true
  Reading patch net.minecraft.client.gui.screens.inventory.MerchantScreen.binpatch
    Checksum: 776d8858 Exists: true
  Reading patch net.minecraft.client.gui.screens.inventory.tooltip.ClientTooltipComponent.binpatch
    Checksum: 90a698b3 Exists: true
  Reading patch net.minecraft.client.gui.screens.inventory.tooltip.TooltipRenderUtil.binpatch
    Checksum: 19ffe5e9 Exists: true
  Reading patch net.minecraft.client.gui.screens.multiplayer.ServerSelectionList$1.binpatch
    Checksum: fa572cd1 Exists: true
  Reading patch net.minecraft.client.gui.screens.multiplayer.ServerSelectionList$Entry.binpatch
    Checksum: 66fc3147 Exists: true
  Reading patch net.minecraft.client.gui.screens.multiplayer.ServerSelectionList$LANHeader.binpatch
    Checksum: ad839886 Exists: true
  Reading patch net.minecraft.client.gui.screens.multiplayer.ServerSelectionList$NetworkServerEntry.binpatch
    Checksum: d2da4e32 Exists: true
  Reading patch net.minecraft.client.gui.screens.multiplayer.ServerSelectionList$OnlineServerEntry.binpatch
    Checksum: 308a09db Exists: true
  Reading patch net.minecraft.client.gui.screens.multiplayer.ServerSelectionList.binpatch
    Checksum: 75c456fd Exists: true
  Reading patch net.minecraft.client.gui.screens.options.controls.KeyBindsList$CategoryEntry$1.binpatch
    Checksum: dc7a6a6b Exists: true
  Reading patch net.minecraft.client.gui.screens.options.controls.KeyBindsList$CategoryEntry.binpatch
    Checksum: 2ec1921a Exists: true
  Reading patch net.minecraft.client.gui.screens.options.controls.KeyBindsList$Entry.binpatch
    Checksum: 43d92081 Exists: true
  Reading patch net.minecraft.client.gui.screens.options.controls.KeyBindsList$KeyEntry.binpatch
    Checksum: 164985ba Exists: true
  Reading patch net.minecraft.client.gui.screens.options.controls.KeyBindsList.binpatch
    Checksum: 5d611e4b Exists: true
  Reading patch net.minecraft.client.gui.screens.options.controls.KeyBindsScreen.binpatch
    Checksum: 4c5ed3f8 Exists: true
  Reading patch net.minecraft.client.gui.screens.packs.PackSelectionModel$Entry.binpatch
    Checksum: 87a175b6 Exists: true
  Reading patch net.minecraft.client.gui.screens.packs.PackSelectionModel$EntryBase.binpatch
    Checksum: 247b8df0 Exists: true
  Reading patch net.minecraft.client.gui.screens.packs.PackSelectionModel$SelectedPackEntry.binpatch
    Checksum: a7d3852e Exists: true
  Reading patch net.minecraft.client.gui.screens.packs.PackSelectionModel$UnselectedPackEntry.binpatch
    Checksum: 242c84eb Exists: true
  Reading patch net.minecraft.client.gui.screens.packs.PackSelectionModel.binpatch
    Checksum: aa82cfa2 Exists: true
  Reading patch net.minecraft.client.gui.screens.packs.PackSelectionScreen$1.binpatch
    Checksum: 98b3b233 Exists: true
  Reading patch net.minecraft.client.gui.screens.packs.PackSelectionScreen$Watcher.binpatch
    Checksum: dc2a3749 Exists: true
  Reading patch net.minecraft.client.gui.screens.packs.PackSelectionScreen.binpatch
    Checksum: f4bea6 Exists: true
  Reading patch net.minecraft.client.gui.screens.recipebook.RecipeBookComponent.binpatch
    Checksum: 266760ef Exists: true
  Reading patch net.minecraft.client.gui.screens.worldselection.CreateWorldScreen$DataPackReloadCookie.binpatch
    Checksum: b6839be6 Exists: true
  Reading patch net.minecraft.client.gui.screens.worldselection.CreateWorldScreen$GameTab.binpatch
    Checksum: 1072bd5b Exists: true
  Reading patch net.minecraft.client.gui.screens.worldselection.CreateWorldScreen$MoreTab.binpatch
    Checksum: 393579b0 Exists: true
  Reading patch net.minecraft.client.gui.screens.worldselection.CreateWorldScreen$WorldTab$1.binpatch
    Checksum: be0d4ca7 Exists: true
  Reading patch net.minecraft.client.gui.screens.worldselection.CreateWorldScreen$WorldTab$2.binpatch
    Checksum: aba8c632 Exists: true
  Reading patch net.minecraft.client.gui.screens.worldselection.CreateWorldScreen$WorldTab.binpatch
    Checksum: 5892a17a Exists: true
  Reading patch net.minecraft.client.gui.screens.worldselection.CreateWorldScreen.binpatch
    Checksum: daf89355 Exists: true
  Reading patch net.minecraft.client.gui.screens.worldselection.PresetEditor.binpatch
    Checksum: ff7ded35 Exists: true
  Reading patch net.minecraft.client.gui.screens.worldselection.WorldCreationContext$DimensionsUpdater.binpatch
    Checksum: 24ec1aa9 Exists: true
  Reading patch net.minecraft.client.gui.screens.worldselection.WorldCreationContext$OptionsModifier.binpatch
    Checksum: 6157ca4c Exists: true
  Reading patch net.minecraft.client.gui.screens.worldselection.WorldCreationContext.binpatch
    Checksum: cfe73aa1 Exists: true
  Reading patch net.minecraft.client.gui.screens.worldselection.WorldCreationUiState$SelectedGameMode.binpatch
    Checksum: ae3a5275 Exists: true
  Reading patch net.minecraft.client.gui.screens.worldselection.WorldCreationUiState$WorldTypeEntry.binpatch
    Checksum: f3310a59 Exists: true
  Reading patch net.minecraft.client.gui.screens.worldselection.WorldCreationUiState.binpatch
    Checksum: 8882ccd6 Exists: true
  Reading patch net.minecraft.client.gui.screens.worldselection.WorldOpenFlows$1Data.binpatch
    Checksum: 17f28dda Exists: true
  Reading patch net.minecraft.client.gui.screens.worldselection.WorldOpenFlows.binpatch
    Checksum: 3cb02eab Exists: true
  Reading patch net.minecraft.client.gui.screens.worldselection.WorldSelectionList$Entry.binpatch
    Checksum: 1be632eb Exists: true
  Reading patch net.minecraft.client.gui.screens.worldselection.WorldSelectionList$LoadingHeader.binpatch
    Checksum: b14ee410 Exists: true
  Reading patch net.minecraft.client.gui.screens.worldselection.WorldSelectionList$WorldListEntry.binpatch
    Checksum: 4d74db0e Exists: true
  Reading patch net.minecraft.client.gui.screens.worldselection.WorldSelectionList.binpatch
    Checksum: 31261593 Exists: true
  Reading patch net.minecraft.client.main.Main$1.binpatch
    Checksum: 628df43e Exists: true
  Reading patch net.minecraft.client.main.Main$2.binpatch
    Checksum: fbe229c Exists: true
  Reading patch net.minecraft.client.main.Main.binpatch
    Checksum: b4e1d58f Exists: true
  Reading patch net.minecraft.client.model.HumanoidModel$ArmPose.binpatch
    Checksum: fffccc5 Exists: true
  Reading patch net.minecraft.client.model.HumanoidModel.binpatch
    Checksum: fb1e8c1d Exists: true
  Reading patch net.minecraft.client.model.geom.LayerDefinitions.binpatch
    Checksum: c28ca3af Exists: true
  Reading patch net.minecraft.client.model.geom.ModelLayers.binpatch
    Checksum: eac5f865 Exists: true
  Reading patch net.minecraft.client.multiplayer.AccountProfileKeyPairManager.binpatch
    Checksum: ff945c40 Exists: true
  Reading patch net.minecraft.client.multiplayer.ClientChunkCache$Storage.binpatch
    Checksum: 1107f69f Exists: true
  Reading patch net.minecraft.client.multiplayer.ClientChunkCache.binpatch
    Checksum: cdc2fad5 Exists: true
  Reading patch net.minecraft.client.multiplayer.ClientCommonPacketListenerImpl$DeferredPacket.binpatch
    Checksum: 98a50322 Exists: true
  Reading patch net.minecraft.client.multiplayer.ClientCommonPacketListenerImpl$PackConfirmScreen$PendingRequest.binpatch
    Checksum: 703c8559 Exists: true
  Reading patch net.minecraft.client.multiplayer.ClientCommonPacketListenerImpl$PackConfirmScreen.binpatch
    Checksum: 746ccb54 Exists: true
  Reading patch net.minecraft.client.multiplayer.ClientCommonPacketListenerImpl.binpatch
    Checksum: a705732f Exists: true
  Reading patch net.minecraft.client.multiplayer.ClientConfigurationPacketListenerImpl.binpatch
    Checksum: f54423f4 Exists: true
  Reading patch net.minecraft.client.multiplayer.ClientHandshakePacketListenerImpl$State.binpatch
    Checksum: f30af219 Exists: true
  Reading patch net.minecraft.client.multiplayer.ClientHandshakePacketListenerImpl.binpatch
    Checksum: 29706e6a Exists: true
  Reading patch net.minecraft.client.multiplayer.ClientLevel$1.binpatch
    Checksum: 458eca0 Exists: true
  Reading patch net.minecraft.client.multiplayer.ClientLevel$ClientLevelData.binpatch
    Checksum: 9ab57f Exists: true
  Reading patch net.minecraft.client.multiplayer.ClientLevel$EntityCallbacks.binpatch
    Checksum: 714e386d Exists: true
  Reading patch net.minecraft.client.multiplayer.ClientLevel.binpatch
    Checksum: eb62b3e5 Exists: true
  Reading patch net.minecraft.client.multiplayer.ClientPacketListener$1.binpatch
    Checksum: 5da5e561 Exists: true
  Reading patch net.minecraft.client.multiplayer.ClientPacketListener.binpatch
    Checksum: 58053ba4 Exists: true
  Reading patch net.minecraft.client.multiplayer.MultiPlayerGameMode.binpatch
    Checksum: 17a10ed0 Exists: true
  Reading patch net.minecraft.client.multiplayer.PlayerInfo.binpatch
    Checksum: 41c08c45 Exists: true
  Reading patch net.minecraft.client.multiplayer.ServerData$ServerPackStatus.binpatch
    Checksum: 81f99d9e Exists: true
  Reading patch net.minecraft.client.multiplayer.ServerData$State.binpatch
    Checksum: ff77847f Exists: true
  Reading patch net.minecraft.client.multiplayer.ServerData$Type.binpatch
    Checksum: 922168ae Exists: true
  Reading patch net.minecraft.client.multiplayer.ServerData.binpatch
    Checksum: 952b02ff Exists: true
  Reading patch net.minecraft.client.multiplayer.ServerStatusPinger$1.binpatch
    Checksum: b07d8c6 Exists: true
  Reading patch net.minecraft.client.multiplayer.ServerStatusPinger$2.binpatch
    Checksum: bb223000 Exists: true
  Reading patch net.minecraft.client.multiplayer.ServerStatusPinger.binpatch
    Checksum: 8278b5a2 Exists: true
  Reading patch net.minecraft.client.multiplayer.SessionSearchTrees$Key.binpatch
    Checksum: 8db7a5cc Exists: true
  Reading patch net.minecraft.client.multiplayer.SessionSearchTrees.binpatch
    Checksum: 645fa0ae Exists: true
  Reading patch net.minecraft.client.multiplayer.chat.ChatListener$Message.binpatch
    Checksum: c4bd685b Exists: true
  Reading patch net.minecraft.client.multiplayer.chat.ChatListener.binpatch
    Checksum: 45773f30 Exists: true
  Reading patch net.minecraft.client.multiplayer.resolver.AddressCheck$1.binpatch
    Checksum: f4981f15 Exists: true
  Reading patch net.minecraft.client.multiplayer.resolver.AddressCheck.binpatch
    Checksum: 5b11e9a8 Exists: true
  Reading patch net.minecraft.client.particle.BreakingItemParticle$CobwebProvider.binpatch
    Checksum: 4cdc2348 Exists: true
  Reading patch net.minecraft.client.particle.BreakingItemParticle$Provider.binpatch
    Checksum: 9e73f4ad Exists: true
  Reading patch net.minecraft.client.particle.BreakingItemParticle$SlimeProvider.binpatch
    Checksum: 7e9121f7 Exists: true
  Reading patch net.minecraft.client.particle.BreakingItemParticle$SnowballProvider.binpatch
    Checksum: e3a02610 Exists: true
  Reading patch net.minecraft.client.particle.BreakingItemParticle.binpatch
    Checksum: c8eaa701 Exists: true
  Reading patch net.minecraft.client.particle.FireworkParticles$1.binpatch
    Checksum: 78292a29 Exists: true
  Reading patch net.minecraft.client.particle.FireworkParticles$FlashProvider.binpatch
    Checksum: 18a84079 Exists: true
  Reading patch net.minecraft.client.particle.FireworkParticles$OverlayParticle.binpatch
    Checksum: 99edac1d Exists: true
  Reading patch net.minecraft.client.particle.FireworkParticles$SparkParticle.binpatch
    Checksum: 556de3d4 Exists: true
  Reading patch net.minecraft.client.particle.FireworkParticles$SparkProvider.binpatch
    Checksum: 9dfd9923 Exists: true
  Reading patch net.minecraft.client.particle.FireworkParticles$Starter.binpatch
    Checksum: 7e4d683b Exists: true
  Reading patch net.minecraft.client.particle.FireworkParticles.binpatch
    Checksum: 72153121 Exists: true
  Reading patch net.minecraft.client.particle.FlyTowardsPositionParticle$EnchantProvider.binpatch
    Checksum: e080314c Exists: true
  Reading patch net.minecraft.client.particle.FlyTowardsPositionParticle$NautilusProvider.binpatch
    Checksum: 6243399 Exists: true
  Reading patch net.minecraft.client.particle.FlyTowardsPositionParticle$VaultConnectionProvider.binpatch
    Checksum: e6209be7 Exists: true
  Reading patch net.minecraft.client.particle.FlyTowardsPositionParticle.binpatch
    Checksum: 43ed6983 Exists: true
  Reading patch net.minecraft.client.particle.Particle$LifetimeAlpha.binpatch
    Checksum: afdf590b Exists: true
  Reading patch net.minecraft.client.particle.Particle.binpatch
    Checksum: a26033e3 Exists: true
  Reading patch net.minecraft.client.particle.ParticleEngine$1ParticleDefinition.binpatch
    Checksum: 16f068db Exists: true
  Reading patch net.minecraft.client.particle.ParticleEngine$MutableSpriteSet.binpatch
    Checksum: df27ebc2 Exists: true
  Reading patch net.minecraft.client.particle.ParticleEngine$SpriteParticleRegistration.binpatch
    Checksum: 8c5cf888 Exists: true
  Reading patch net.minecraft.client.particle.ParticleEngine.binpatch
    Checksum: b33e286d Exists: true
  Reading patch net.minecraft.client.particle.PortalParticle$Provider.binpatch
    Checksum: 57150f94 Exists: true
  Reading patch net.minecraft.client.particle.PortalParticle.binpatch
    Checksum: 1b86c7df Exists: true
  Reading patch net.minecraft.client.particle.ReversePortalParticle$ReversePortalProvider.binpatch
    Checksum: 192c72 Exists: true
  Reading patch net.minecraft.client.particle.ReversePortalParticle.binpatch
    Checksum: 6a977ba6 Exists: true
  Reading patch net.minecraft.client.particle.TerrainParticle$DustPillarProvider.binpatch
    Checksum: 942c8b72 Exists: true
  Reading patch net.minecraft.client.particle.TerrainParticle$Provider.binpatch
    Checksum: 2fa40071 Exists: true
  Reading patch net.minecraft.client.particle.TerrainParticle.binpatch
    Checksum: e454c9c3 Exists: true
  Reading patch net.minecraft.client.particle.VibrationSignalParticle$Provider.binpatch
    Checksum: fa586392 Exists: true
  Reading patch net.minecraft.client.particle.VibrationSignalParticle.binpatch
    Checksum: 77dbf93b Exists: true
  Reading patch net.minecraft.client.player.AbstractClientPlayer.binpatch
    Checksum: a370fb94 Exists: true
  Reading patch net.minecraft.client.player.LocalPlayer.binpatch
    Checksum: caa808e7 Exists: true
  Reading patch net.minecraft.client.player.RemotePlayer.binpatch
    Checksum: 9f5d14e8 Exists: true
  Reading patch net.minecraft.client.renderer.DimensionSpecialEffects$EndEffects.binpatch
    Checksum: 7d2d6dc2 Exists: true
  Reading patch net.minecraft.client.renderer.DimensionSpecialEffects$NetherEffects.binpatch
    Checksum: 4ca63c46 Exists: true
  Reading patch net.minecraft.client.renderer.DimensionSpecialEffects$OverworldEffects.binpatch
    Checksum: 26587578 Exists: true
  Reading patch net.minecraft.client.renderer.DimensionSpecialEffects$SkyType.binpatch
    Checksum: 82768d7e Exists: true
  Reading patch net.minecraft.client.renderer.DimensionSpecialEffects.binpatch
    Checksum: d7b6db8b Exists: true
  Reading patch net.minecraft.client.renderer.EffectInstance.binpatch
    Checksum: 85884151 Exists: true
  Reading patch net.minecraft.client.renderer.FogRenderer$BlindnessFogFunction.binpatch
    Checksum: 50873ae0 Exists: true
  Reading patch net.minecraft.client.renderer.FogRenderer$DarknessFogFunction.binpatch
    Checksum: 3225636a Exists: true
  Reading patch net.minecraft.client.renderer.FogRenderer$FogData.binpatch
    Checksum: 27260821 Exists: true
  Reading patch net.minecraft.client.renderer.FogRenderer$FogMode.binpatch
    Checksum: 5ba5fea Exists: true
  Reading patch net.minecraft.client.renderer.FogRenderer$MobEffectFogFunction.binpatch
    Checksum: e8b6fd0e Exists: true
  Reading patch net.minecraft.client.renderer.FogRenderer.binpatch
    Checksum: d18ea533 Exists: true
  Reading patch net.minecraft.client.renderer.GameRenderer$1.binpatch
    Checksum: 3c387d29 Exists: true
  Reading patch net.minecraft.client.renderer.GameRenderer$ResourceCache.binpatch
    Checksum: cb4e9697 Exists: true
  Reading patch net.minecraft.client.renderer.GameRenderer.binpatch
    Checksum: b03d2693 Exists: true
  Reading patch net.minecraft.client.renderer.ItemBlockRenderTypes.binpatch
    Checksum: c59740fe Exists: true
  Reading patch net.minecraft.client.renderer.ItemInHandRenderer$1.binpatch
    Checksum: 50d6fd4e Exists: true
  Reading patch net.minecraft.client.renderer.ItemInHandRenderer$HandRenderSelection.binpatch
    Checksum: 3b8c4adb Exists: true
  Reading patch net.minecraft.client.renderer.ItemInHandRenderer.binpatch
    Checksum: ce5b34ab Exists: true
  Reading patch net.minecraft.client.renderer.LevelRenderer$1.binpatch
    Checksum: 3ed2eb87 Exists: true
  Reading patch net.minecraft.client.renderer.LevelRenderer$TransparencyShaderException.binpatch
    Checksum: 42e2e094 Exists: true
  Reading patch net.minecraft.client.renderer.LevelRenderer.binpatch
    Checksum: dd24f868 Exists: true
  Reading patch net.minecraft.client.renderer.LightTexture.binpatch
    Checksum: d216e0da Exists: true
  Reading patch net.minecraft.client.renderer.PostChain.binpatch
    Checksum: 29a02aaf Exists: true
  Reading patch net.minecraft.client.renderer.RenderType$CompositeRenderType.binpatch
    Checksum: 1ad12022 Exists: true
  Reading patch net.minecraft.client.renderer.RenderType$CompositeState$CompositeStateBuilder.binpatch
    Checksum: 4f4e5953 Exists: true
  Reading patch net.minecraft.client.renderer.RenderType$CompositeState.binpatch
    Checksum: 4bcde086 Exists: true
  Reading patch net.minecraft.client.renderer.RenderType$OutlineProperty.binpatch
    Checksum: c6c1bd0e Exists: true
  Reading patch net.minecraft.client.renderer.RenderType.binpatch
    Checksum: 85089009 Exists: true
  Reading patch net.minecraft.client.renderer.ScreenEffectRenderer.binpatch
    Checksum: 20a13195 Exists: true
  Reading patch net.minecraft.client.renderer.ShaderInstance$1.binpatch
    Checksum: f0fabae0 Exists: true
  Reading patch net.minecraft.client.renderer.ShaderInstance.binpatch
    Checksum: c0e663c Exists: true
  Reading patch net.minecraft.client.renderer.Sheets$1.binpatch
    Checksum: e5fbf225 Exists: true
  Reading patch net.minecraft.client.renderer.Sheets.binpatch
    Checksum: 92a02de7 Exists: true
  Reading patch net.minecraft.client.renderer.SpriteCoordinateExpander.binpatch
    Checksum: 2671f6fa Exists: true
  Reading patch net.minecraft.client.renderer.block.BlockModelShaper.binpatch
    Checksum: a961124a Exists: true
  Reading patch net.minecraft.client.renderer.block.BlockRenderDispatcher$1.binpatch
    Checksum: 570ff2fd Exists: true
  Reading patch net.minecraft.client.renderer.block.BlockRenderDispatcher.binpatch
    Checksum: 9651e760 Exists: true
  Reading patch net.minecraft.client.renderer.block.LiquidBlockRenderer$1.binpatch
    Checksum: 27a1e290 Exists: true
  Reading patch net.minecraft.client.renderer.block.LiquidBlockRenderer.binpatch
    Checksum: b52dc4e8 Exists: true
  Reading patch net.minecraft.client.renderer.block.ModelBlockRenderer$1.binpatch
    Checksum: afdef648 Exists: true
  Reading patch net.minecraft.client.renderer.block.ModelBlockRenderer$AdjacencyInfo.binpatch
    Checksum: 466ba693 Exists: true
  Reading patch net.minecraft.client.renderer.block.ModelBlockRenderer$AmbientOcclusionFace.binpatch
    Checksum: fcfa8dd2 Exists: true
  Reading patch net.minecraft.client.renderer.block.ModelBlockRenderer$AmbientVertexRemap.binpatch
    Checksum: dc57638e Exists: true
  Reading patch net.minecraft.client.renderer.block.ModelBlockRenderer$Cache$1.binpatch
    Checksum: 6d6c25f5 Exists: true
  Reading patch net.minecraft.client.renderer.block.ModelBlockRenderer$Cache$2.binpatch
    Checksum: 228426d7 Exists: true
  Reading patch net.minecraft.client.renderer.block.ModelBlockRenderer$Cache.binpatch
    Checksum: 5d2e5e36 Exists: true
  Reading patch net.minecraft.client.renderer.block.ModelBlockRenderer$SizeInfo.binpatch
    Checksum: 4e596b3c Exists: true
  Reading patch net.minecraft.client.renderer.block.ModelBlockRenderer.binpatch
    Checksum: 29fa609 Exists: true
  Reading patch net.minecraft.client.renderer.block.model.BakedQuad.binpatch
    Checksum: 3cd93c70 Exists: true
  Reading patch net.minecraft.client.renderer.block.model.BlockElementFace$Deserializer.binpatch
    Checksum: b4737bfc Exists: true
  Reading patch net.minecraft.client.renderer.block.model.BlockElementFace.binpatch
    Checksum: d0429427 Exists: true
  Reading patch net.minecraft.client.renderer.block.model.BlockModel$Deserializer.binpatch
    Checksum: 569d9cba Exists: true
  Reading patch net.minecraft.client.renderer.block.model.BlockModel$GuiLight.binpatch
    Checksum: 354868d Exists: true
  Reading patch net.minecraft.client.renderer.block.model.BlockModel$LoopException.binpatch
    Checksum: e604c3b0 Exists: true
  Reading patch net.minecraft.client.renderer.block.model.BlockModel.binpatch
    Checksum: 96e0894d Exists: true
  Reading patch net.minecraft.client.renderer.block.model.FaceBakery$1.binpatch
    Checksum: e75ceaf4 Exists: true
  Reading patch net.minecraft.client.renderer.block.model.FaceBakery.binpatch
    Checksum: 2301da3d Exists: true
  Reading patch net.minecraft.client.renderer.block.model.ItemModelGenerator$Span.binpatch
    Checksum: de357402 Exists: true
  Reading patch net.minecraft.client.renderer.block.model.ItemModelGenerator$SpanFacing.binpatch
    Checksum: b22449d7 Exists: true
  Reading patch net.minecraft.client.renderer.block.model.ItemModelGenerator.binpatch
    Checksum: 4eb9eb77 Exists: true
  Reading patch net.minecraft.client.renderer.block.model.ItemOverrides$BakedOverride.binpatch
    Checksum: c91fa772 Exists: true
  Reading patch net.minecraft.client.renderer.block.model.ItemOverrides$PropertyMatcher.binpatch
    Checksum: e3c5c801 Exists: true
  Reading patch net.minecraft.client.renderer.block.model.ItemOverrides.binpatch
    Checksum: 892df4e0 Exists: true
  Reading patch net.minecraft.client.renderer.block.model.ItemTransform$Deserializer.binpatch
    Checksum: 39672a8c Exists: true
  Reading patch net.minecraft.client.renderer.block.model.ItemTransform.binpatch
    Checksum: fcba018a Exists: true
  Reading patch net.minecraft.client.renderer.block.model.ItemTransforms$1.binpatch
    Checksum: 5c672ed1 Exists: true
  Reading patch net.minecraft.client.renderer.block.model.ItemTransforms$Deserializer.binpatch
    Checksum: ecd10d60 Exists: true
  Reading patch net.minecraft.client.renderer.block.model.ItemTransforms.binpatch
    Checksum: e0e4d286 Exists: true
  Reading patch net.minecraft.client.renderer.block.model.MultiVariant$Deserializer.binpatch
    Checksum: 1badf11c Exists: true
  Reading patch net.minecraft.client.renderer.block.model.MultiVariant.binpatch
    Checksum: af11c4cb Exists: true
  Reading patch net.minecraft.client.renderer.blockentity.BlockEntityRenderers.binpatch
    Checksum: 85e1fe8c Exists: true
  Reading patch net.minecraft.client.renderer.blockentity.ChestRenderer.binpatch
    Checksum: bcba780a Exists: true
  Reading patch net.minecraft.client.renderer.blockentity.PistonHeadRenderer.binpatch
    Checksum: 32e60581 Exists: true
  Reading patch net.minecraft.client.renderer.blockentity.SkullBlockRenderer.binpatch
    Checksum: 667f70c7 Exists: true
  Reading patch net.minecraft.client.renderer.chunk.RenderChunkRegion.binpatch
    Checksum: da7be77c Exists: true
  Reading patch net.minecraft.client.renderer.chunk.SectionCompiler$Results.binpatch
    Checksum: a79480d6 Exists: true
  Reading patch net.minecraft.client.renderer.chunk.SectionCompiler.binpatch
    Checksum: 748e6604 Exists: true
  Reading patch net.minecraft.client.renderer.culling.Frustum.binpatch
    Checksum: bce8d2cd Exists: true
  Reading patch net.minecraft.client.renderer.entity.BoatRenderer.binpatch
    Checksum: 78961441 Exists: true
  Reading patch net.minecraft.client.renderer.entity.EntityRenderDispatcher.binpatch
    Checksum: a0869edf Exists: true
  Reading patch net.minecraft.client.renderer.entity.EntityRenderer.binpatch
    Checksum: 58dca0d5 Exists: true
  Reading patch net.minecraft.client.renderer.entity.FallingBlockRenderer.binpatch
    Checksum: 9d2604d4 Exists: true
  Reading patch net.minecraft.client.renderer.entity.FishingHookRenderer.binpatch
    Checksum: 3a2c28f3 Exists: true
  Reading patch net.minecraft.client.renderer.entity.ItemFrameRenderer.binpatch
    Checksum: 5dfceece Exists: true
  Reading patch net.minecraft.client.renderer.entity.ItemRenderer.binpatch
    Checksum: 39e6839b Exists: true
  Reading patch net.minecraft.client.renderer.entity.LivingEntityRenderer$1.binpatch
    Checksum: 8c4640d Exists: true
  Reading patch net.minecraft.client.renderer.entity.LivingEntityRenderer.binpatch
    Checksum: c7ec5d9c Exists: true
  Reading patch net.minecraft.client.renderer.entity.layers.ElytraLayer.binpatch
    Checksum: f1ccbc9a Exists: true
  Reading patch net.minecraft.client.renderer.entity.layers.HumanoidArmorLayer$1.binpatch
    Checksum: 36c8fd26 Exists: true
  Reading patch net.minecraft.client.renderer.entity.layers.HumanoidArmorLayer.binpatch
    Checksum: 18ed114b Exists: true
  Reading patch net.minecraft.client.renderer.entity.player.PlayerRenderer.binpatch
    Checksum: 80936491 Exists: true
  Reading patch net.minecraft.client.renderer.item.ItemProperties$1.binpatch
    Checksum: d122905e Exists: true
  Reading patch net.minecraft.client.renderer.item.ItemProperties.binpatch
    Checksum: 32b2217 Exists: true
  Reading patch net.minecraft.client.renderer.texture.AbstractTexture.binpatch
    Checksum: eaf76b8d Exists: true
  Reading patch net.minecraft.client.renderer.texture.MipmapGenerator.binpatch
    Checksum: 173281f0 Exists: true
  Reading patch net.minecraft.client.renderer.texture.SpriteContents$AnimatedTexture.binpatch
    Checksum: dc8a229b Exists: true
  Reading patch net.minecraft.client.renderer.texture.SpriteContents$FrameInfo.binpatch
    Checksum: 2fbcbc72 Exists: true
  Reading patch net.minecraft.client.renderer.texture.SpriteContents$InterpolationData.binpatch
    Checksum: 99c0b300 Exists: true
  Reading patch net.minecraft.client.renderer.texture.SpriteContents$Ticker.binpatch
    Checksum: aec18d4d Exists: true
  Reading patch net.minecraft.client.renderer.texture.SpriteContents.binpatch
    Checksum: 5a34caba Exists: true
  Reading patch net.minecraft.client.renderer.texture.SpriteLoader$Preparations.binpatch
    Checksum: ff21d9f2 Exists: true
  Reading patch net.minecraft.client.renderer.texture.SpriteLoader.binpatch
    Checksum: 21ca0f8c Exists: true
  Reading patch net.minecraft.client.renderer.texture.Stitcher$Entry.binpatch
    Checksum: 55a8c5a Exists: true
  Reading patch net.minecraft.client.renderer.texture.Stitcher$Holder.binpatch
    Checksum: 377baff8 Exists: true
  Reading patch net.minecraft.client.renderer.texture.Stitcher$Region.binpatch
    Checksum: 84b5a86a Exists: true
  Reading patch net.minecraft.client.renderer.texture.Stitcher$SpriteLoader.binpatch
    Checksum: 2286cb9a Exists: true
  Reading patch net.minecraft.client.renderer.texture.Stitcher.binpatch
    Checksum: d24a18a8 Exists: true
  Reading patch net.minecraft.client.renderer.texture.TextureAtlas.binpatch
    Checksum: 6fb42c25 Exists: true
  Reading patch net.minecraft.client.renderer.texture.TextureAtlasSprite$1.binpatch
    Checksum: 926b8836 Exists: true
  Reading patch net.minecraft.client.renderer.texture.TextureAtlasSprite$Ticker.binpatch
    Checksum: efe8ead Exists: true
  Reading patch net.minecraft.client.renderer.texture.TextureAtlasSprite.binpatch
    Checksum: b52a3884 Exists: true
  Reading patch net.minecraft.client.renderer.texture.atlas.SpriteResourceLoader.binpatch
    Checksum: b7f7101a Exists: true
  Reading patch net.minecraft.client.resources.language.ClientLanguage.binpatch
    Checksum: c9d2bb3f Exists: true
  Reading patch net.minecraft.client.resources.language.I18n.binpatch
    Checksum: d1dac437 Exists: true
  Reading patch net.minecraft.client.resources.language.LanguageManager.binpatch
    Checksum: 56636dea Exists: true
  Reading patch net.minecraft.client.resources.model.BakedModel.binpatch
    Checksum: 4079406e Exists: true
  Reading patch net.minecraft.client.resources.model.ModelBaker.binpatch
    Checksum: b370bfb5 Exists: true
  Reading patch net.minecraft.client.resources.model.ModelBakery$BakedCacheKey.binpatch
    Checksum: 8e703f9d Exists: true
  Reading patch net.minecraft.client.resources.model.ModelBakery$ModelBakerImpl.binpatch
    Checksum: 81ee0af3 Exists: true
  Reading patch net.minecraft.client.resources.model.ModelBakery$TextureGetter.binpatch
    Checksum: 19c1cb82 Exists: true
  Reading patch net.minecraft.client.resources.model.ModelBakery.binpatch
    Checksum: 46c5cfca Exists: true
  Reading patch net.minecraft.client.resources.model.ModelManager$ReloadState.binpatch
    Checksum: 3b3b99f4 Exists: true
  Reading patch net.minecraft.client.resources.model.ModelManager.binpatch
    Checksum: 9842ef05 Exists: true
  Reading patch net.minecraft.client.resources.model.MultiPartBakedModel$Builder.binpatch
    Checksum: a2f224ee Exists: true
  Reading patch net.minecraft.client.resources.model.MultiPartBakedModel.binpatch
    Checksum: f4dc9493 Exists: true
  Reading patch net.minecraft.client.resources.model.SimpleBakedModel$Builder.binpatch
    Checksum: a606f919 Exists: true
  Reading patch net.minecraft.client.resources.model.SimpleBakedModel.binpatch
    Checksum: 5cbcf0f8 Exists: true
  Reading patch net.minecraft.client.resources.model.WeightedBakedModel$Builder.binpatch
    Checksum: 8019277a Exists: true
  Reading patch net.minecraft.client.resources.model.WeightedBakedModel.binpatch
    Checksum: 57d5e59d Exists: true
  Reading patch net.minecraft.client.resources.sounds.SoundInstance$Attenuation.binpatch
    Checksum: be19894b Exists: true
  Reading patch net.minecraft.client.resources.sounds.SoundInstance.binpatch
    Checksum: d40b6877 Exists: true
  Reading patch net.minecraft.client.server.IntegratedServer.binpatch
    Checksum: 9d7c2c29 Exists: true
  Reading patch net.minecraft.client.server.LanServerDetection$LanServerDetector.binpatch
    Checksum: 60901ee Exists: true
  Reading patch net.minecraft.client.server.LanServerDetection$LanServerList.binpatch
    Checksum: dea0e2bf Exists: true
  Reading patch net.minecraft.client.server.LanServerDetection.binpatch
    Checksum: 14f116ab Exists: true
  Reading patch net.minecraft.client.server.LanServerPinger.binpatch
    Checksum: 8c5348e Exists: true
  Reading patch net.minecraft.client.sounds.SoundEngine$DeviceCheckState.binpatch
    Checksum: 1c577eb4 Exists: true
  Reading patch net.minecraft.client.sounds.SoundEngine.binpatch
    Checksum: 8b3620f9 Exists: true
  Reading patch net.minecraft.commands.CommandSourceStack.binpatch
    Checksum: e252600e Exists: true
  Reading patch net.minecraft.commands.Commands$1$1.binpatch
    Checksum: 867a81c4 Exists: true
  Reading patch net.minecraft.commands.Commands$1.binpatch
    Checksum: 1745960f Exists: true
  Reading patch net.minecraft.commands.Commands$CommandSelection.binpatch
    Checksum: 276d5643 Exists: true
  Reading patch net.minecraft.commands.Commands$ParseFunction.binpatch
    Checksum: 49497f3a Exists: true
  Reading patch net.minecraft.commands.Commands.binpatch
    Checksum: 426c0f4f Exists: true
  Reading patch net.minecraft.commands.arguments.EntityArgument$Info$Template.binpatch
    Checksum: 11bafb05 Exists: true
  Reading patch net.minecraft.commands.arguments.EntityArgument$Info.binpatch
    Checksum: 866ab6e5 Exists: true
  Reading patch net.minecraft.commands.arguments.EntityArgument.binpatch
    Checksum: 646271e8 Exists: true
  Reading patch net.minecraft.commands.arguments.MessageArgument$Message.binpatch
    Checksum: 9e5fb40d Exists: true
  Reading patch net.minecraft.commands.arguments.MessageArgument$Part.binpatch
    Checksum: 7e9a8895 Exists: true
  Reading patch net.minecraft.commands.arguments.MessageArgument.binpatch
    Checksum: e4e39eac Exists: true
  Reading patch net.minecraft.commands.arguments.ObjectiveArgument.binpatch
    Checksum: e976ed2c Exists: true
  Reading patch net.minecraft.commands.arguments.ResourceLocationArgument.binpatch
    Checksum: 96cc1820 Exists: true
  Reading patch net.minecraft.commands.arguments.TeamArgument.binpatch
    Checksum: e943e1fe Exists: true
  Reading patch net.minecraft.commands.arguments.coordinates.BlockPosArgument.binpatch
    Checksum: c3cd660 Exists: true
  Reading patch net.minecraft.commands.arguments.selector.EntitySelector$1.binpatch
    Checksum: ae81418c Exists: true
  Reading patch net.minecraft.commands.arguments.selector.EntitySelector.binpatch
    Checksum: b812cd1e Exists: true
  Reading patch net.minecraft.commands.arguments.selector.EntitySelectorParser.binpatch
    Checksum: 5aa79ef Exists: true
  Reading patch net.minecraft.commands.synchronization.ArgumentTypeInfos.binpatch
    Checksum: 4b91c90 Exists: true
  Reading patch net.minecraft.core.DefaultedMappedRegistry.binpatch
    Checksum: 97ba8a33 Exists: true
  Reading patch net.minecraft.core.Holder$Direct.binpatch
    Checksum: 9d62c227 Exists: true
  Reading patch net.minecraft.core.Holder$Kind.binpatch
    Checksum: 2123f889 Exists: true
  Reading patch net.minecraft.core.Holder$Reference$Type.binpatch
    Checksum: abe1259d Exists: true
  Reading patch net.minecraft.core.Holder$Reference.binpatch
    Checksum: 29be29fe Exists: true
  Reading patch net.minecraft.core.Holder.binpatch
    Checksum: ceb5a0af Exists: true
  Reading patch net.minecraft.core.HolderSet$1.binpatch
    Checksum: 50daea1d Exists: true
  Reading patch net.minecraft.core.HolderSet$Direct.binpatch
    Checksum: ffd932f9 Exists: true
  Reading patch net.minecraft.core.HolderSet$ListBacked.binpatch
    Checksum: 1fc127b2 Exists: true
  Reading patch net.minecraft.core.HolderSet$Named.binpatch
    Checksum: 18dba46d Exists: true
  Reading patch net.minecraft.core.HolderSet.binpatch
    Checksum: 1b244785 Exists: true
  Reading patch net.minecraft.core.MappedRegistry$1.binpatch
    Checksum: 16347e00 Exists: true
  Reading patch net.minecraft.core.MappedRegistry$2.binpatch
    Checksum: 60be6aeb Exists: true
  Reading patch net.minecraft.core.MappedRegistry.binpatch
    Checksum: e48296b8 Exists: true
  Reading patch net.minecraft.core.RegistrySetBuilder$1.binpatch
    Checksum: 3e1acd21 Exists: true
  Reading patch net.minecraft.core.RegistrySetBuilder$1Entry.binpatch
    Checksum: a633dc6 Exists: true
  Reading patch net.minecraft.core.RegistrySetBuilder$2.binpatch
    Checksum: 6aefd432 Exists: true
  Reading patch net.minecraft.core.RegistrySetBuilder$3$1.binpatch
    Checksum: c0edbd13 Exists: true
  Reading patch net.minecraft.core.RegistrySetBuilder$3.binpatch
    Checksum: 2eb86a71 Exists: true
  Reading patch net.minecraft.core.RegistrySetBuilder$BuildState$1.binpatch
    Checksum: 13057561 Exists: true
  Reading patch net.minecraft.core.RegistrySetBuilder$BuildState.binpatch
    Checksum: 7bb23c89 Exists: true
  Reading patch net.minecraft.core.RegistrySetBuilder$EmptyTagLookup.binpatch
    Checksum: 15fdadcb Exists: true
  Reading patch net.minecraft.core.RegistrySetBuilder$EmptyTagLookupWrapper.binpatch
    Checksum: 71f1ff01 Exists: true
  Reading patch net.minecraft.core.RegistrySetBuilder$EmptyTagRegistryLookup.binpatch
    Checksum: 2421b2e3 Exists: true
  Reading patch net.minecraft.core.RegistrySetBuilder$LazyHolder.binpatch
    Checksum: dd401300 Exists: true
  Reading patch net.minecraft.core.RegistrySetBuilder$PatchedRegistries.binpatch
    Checksum: cb01f7ec Exists: true
  Reading patch net.minecraft.core.RegistrySetBuilder$RegisteredValue.binpatch
    Checksum: b61d16cb Exists: true
  Reading patch net.minecraft.core.RegistrySetBuilder$RegistryBootstrap.binpatch
    Checksum: beb39d42 Exists: true
  Reading patch net.minecraft.core.RegistrySetBuilder$RegistryContents.binpatch
    Checksum: 5472eaa8 Exists: true
  Reading patch net.minecraft.core.RegistrySetBuilder$RegistryStub.binpatch
    Checksum: 3d4b5ff2 Exists: true
  Reading patch net.minecraft.core.RegistrySetBuilder$UniversalLookup.binpatch
    Checksum: 1000686b Exists: true
  Reading patch net.minecraft.core.RegistrySetBuilder$UniversalOwner.binpatch
    Checksum: b6c6c49f Exists: true
  Reading patch net.minecraft.core.RegistrySetBuilder$ValueAndHolder.binpatch
    Checksum: 6f54e998 Exists: true
  Reading patch net.minecraft.core.RegistrySetBuilder.binpatch
    Checksum: eac82b97 Exists: true
  Reading patch net.minecraft.core.RegistrySynchronization$PackedRegistryEntry.binpatch
    Checksum: 2b1ae0a3 Exists: true
  Reading patch net.minecraft.core.RegistrySynchronization.binpatch
    Checksum: 366e5093 Exists: true
  Reading patch net.minecraft.core.dispenser.BoatDispenseItemBehavior.binpatch
    Checksum: 5add7e1 Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$1.binpatch
    Checksum: 3f37d798 Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$10.binpatch
    Checksum: 8fbe5deb Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$11.binpatch
    Checksum: 95f81508 Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$12.binpatch
    Checksum: 7c999f73 Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$13.binpatch
    Checksum: f9fc6872 Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$14.binpatch
    Checksum: 6640ab1f Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$15.binpatch
    Checksum: bbcd20db Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$16.binpatch
    Checksum: 884d1dd2 Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$17.binpatch
    Checksum: f7a205aa Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$18.binpatch
    Checksum: 361c9172 Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$2.binpatch
    Checksum: 8f234a8a Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$3.binpatch
    Checksum: 66a3f0e4 Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$4.binpatch
    Checksum: 13842e0b Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$5.binpatch
    Checksum: 533007d Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$6.binpatch
    Checksum: bafd43aa Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$7.binpatch
    Checksum: 35a9f2fe Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$8.binpatch
    Checksum: 2733a6f2 Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior$9.binpatch
    Checksum: bcaba865 Exists: true
  Reading patch net.minecraft.core.dispenser.DispenseItemBehavior.binpatch
    Checksum: 56016f9b Exists: true
  Reading patch net.minecraft.core.particles.BlockParticleOption.binpatch
    Checksum: 7e23e200 Exists: true
  Reading patch net.minecraft.core.particles.ItemParticleOption.binpatch
    Checksum: 90d36fea Exists: true
  Reading patch net.minecraft.core.registries.BuiltInRegistries$RegistryBootstrap.binpatch
    Checksum: a95fa396 Exists: true
  Reading patch net.minecraft.core.registries.BuiltInRegistries.binpatch
    Checksum: 7213b154 Exists: true
  Reading patch net.minecraft.core.registries.Registries.binpatch
    Checksum: 840dbf00 Exists: true
  Reading patch net.minecraft.data.DataGenerator$PackGenerator.binpatch
    Checksum: 80dcbabb Exists: true
  Reading patch net.minecraft.data.DataGenerator.binpatch
    Checksum: ebad310f Exists: true
  Reading patch net.minecraft.data.HashCache$1.binpatch
    Checksum: bdd62eb3 Exists: true
  Reading patch net.minecraft.data.HashCache$CacheUpdater.binpatch
    Checksum: b5835934 Exists: true
  Reading patch net.minecraft.data.HashCache$ProviderCache.binpatch
    Checksum: 1d6699db Exists: true
  Reading patch net.minecraft.data.HashCache$ProviderCacheBuilder.binpatch
    Checksum: e0202a01 Exists: true
  Reading patch net.minecraft.data.HashCache$UpdateFunction.binpatch
    Checksum: 41269324 Exists: true
  Reading patch net.minecraft.data.HashCache$UpdateResult.binpatch
    Checksum: 3bedd816 Exists: true
  Reading patch net.minecraft.data.HashCache.binpatch
    Checksum: 8b765434 Exists: true
  Reading patch net.minecraft.data.Main.binpatch
    Checksum: bf753f0a Exists: true
  Reading patch net.minecraft.data.advancements.AdvancementProvider.binpatch
    Checksum: fbfff3b4 Exists: true
  Reading patch net.minecraft.data.loot.BlockLootSubProvider.binpatch
    Checksum: 90c01897 Exists: true
  Reading patch net.minecraft.data.loot.EntityLootSubProvider.binpatch
    Checksum: e059e92d Exists: true
  Reading patch net.minecraft.data.loot.LootTableProvider$SubProviderEntry.binpatch
    Checksum: 1c0cec0d Exists: true
  Reading patch net.minecraft.data.loot.LootTableProvider.binpatch
    Checksum: 9ce6fb66 Exists: true
  Reading patch net.minecraft.data.recipes.RecipeOutput.binpatch
    Checksum: 6fb9e910 Exists: true
  Reading patch net.minecraft.data.recipes.RecipeProvider$1.binpatch
    Checksum: 8fcd945 Exists: true
  Reading patch net.minecraft.data.recipes.RecipeProvider.binpatch
    Checksum: 95f9f99a Exists: true
  Reading patch net.minecraft.data.registries.RegistriesDatapackGenerator.binpatch
    Checksum: 93206041 Exists: true
  Reading patch net.minecraft.data.registries.RegistryPatchGenerator.binpatch
    Checksum: a4a47a15 Exists: true
  Reading patch net.minecraft.data.registries.VanillaRegistries.binpatch
    Checksum: 4dc6d8f7 Exists: true
  Reading patch net.minecraft.data.tags.BannerPatternTagsProvider.binpatch
    Checksum: 65bfba12 Exists: true
  Reading patch net.minecraft.data.tags.BiomeTagsProvider.binpatch
    Checksum: ae83b804 Exists: true
  Reading patch net.minecraft.data.tags.CatVariantTagsProvider.binpatch
    Checksum: 1cb846 Exists: true
  Reading patch net.minecraft.data.tags.DamageTypeTagsProvider.binpatch
    Checksum: 92344fd6 Exists: true
  Reading patch net.minecraft.data.tags.EntityTypeTagsProvider.binpatch
    Checksum: 70784c5d Exists: true
  Reading patch net.minecraft.data.tags.FlatLevelGeneratorPresetTagsProvider.binpatch
    Checksum: 8b909405 Exists: true
  Reading patch net.minecraft.data.tags.FluidTagsProvider.binpatch
    Checksum: 2e6ec74c Exists: true
  Reading patch net.minecraft.data.tags.GameEventTagsProvider.binpatch
    Checksum: bc5afe07 Exists: true
  Reading patch net.minecraft.data.tags.InstrumentTagsProvider.binpatch
    Checksum: 3d37773a Exists: true
  Reading patch net.minecraft.data.tags.IntrinsicHolderTagsProvider$IntrinsicTagAppender.binpatch
    Checksum: dc6f4b96 Exists: true
  Reading patch net.minecraft.data.tags.IntrinsicHolderTagsProvider.binpatch
    Checksum: 5c6fa2d6 Exists: true
  Reading patch net.minecraft.data.tags.ItemTagsProvider.binpatch
    Checksum: b3f684d1 Exists: true
  Reading patch net.minecraft.data.tags.PaintingVariantTagsProvider.binpatch
    Checksum: 895c6bc2 Exists: true
  Reading patch net.minecraft.data.tags.PoiTypeTagsProvider.binpatch
    Checksum: 6a62c059 Exists: true
  Reading patch net.minecraft.data.tags.StructureTagsProvider.binpatch
    Checksum: fb9b42da Exists: true
  Reading patch net.minecraft.data.tags.TagsProvider$1CombinedData.binpatch
    Checksum: 1b05d1d2 Exists: true
  Reading patch net.minecraft.data.tags.TagsProvider$TagAppender.binpatch
    Checksum: a0edb108 Exists: true
  Reading patch net.minecraft.data.tags.TagsProvider$TagLookup.binpatch
    Checksum: 265314a6 Exists: true
  Reading patch net.minecraft.data.tags.TagsProvider.binpatch
    Checksum: f87a0533 Exists: true
  Reading patch net.minecraft.data.tags.WorldPresetTagsProvider.binpatch
    Checksum: 7f35724a Exists: true
  Reading patch net.minecraft.data.worldgen.BootstrapContext.binpatch
    Checksum: 3384e246 Exists: true
  Reading patch net.minecraft.gametest.framework.GameTestHelper$1.binpatch
    Checksum: f326938a Exists: true
  Reading patch net.minecraft.gametest.framework.GameTestHelper$2.binpatch
    Checksum: 7feb8ac1 Exists: true
  Reading patch net.minecraft.gametest.framework.GameTestHelper.binpatch
    Checksum: a9498b86 Exists: true
  Reading patch net.minecraft.gametest.framework.GameTestRegistry.binpatch
    Checksum: e92c632e Exists: true
  Reading patch net.minecraft.gametest.framework.GameTestServer$1.binpatch
    Checksum: 17cf81b7 Exists: true
  Reading patch net.minecraft.gametest.framework.GameTestServer.binpatch
    Checksum: 126d53e0 Exists: true
  Reading patch net.minecraft.locale.Language$1.binpatch
    Checksum: 964c68cf Exists: true
  Reading patch net.minecraft.locale.Language.binpatch
    Checksum: 17bf38f9 Exists: true
  Reading patch net.minecraft.nbt.CompoundTag$1.binpatch
    Checksum: a5c7d3a6 Exists: true
  Reading patch net.minecraft.nbt.CompoundTag$2.binpatch
    Checksum: eaa115fd Exists: true
  Reading patch net.minecraft.nbt.CompoundTag$3.binpatch
    Checksum: 0 Exists: false
  Reading patch net.minecraft.nbt.CompoundTag.binpatch
    Checksum: c971fa0e Exists: true
  Reading patch net.minecraft.network.CompressionEncoder.binpatch
    Checksum: 71a2997c Exists: true
  Reading patch net.minecraft.network.Connection$1.binpatch
    Checksum: 6dfe6e02 Exists: true
  Reading patch net.minecraft.network.Connection$2.binpatch
    Checksum: e5ab0291 Exists: true
  Reading patch net.minecraft.network.Connection$3.binpatch
    Checksum: 66616334 Exists: true
  Reading patch net.minecraft.network.Connection.binpatch
    Checksum: 998ba9a Exists: true
  Reading patch net.minecraft.network.FriendlyByteBuf.binpatch
    Checksum: 74c4ad3e Exists: true
  Reading patch net.minecraft.network.RegistryFriendlyByteBuf.binpatch
    Checksum: 3035fa78 Exists: true
  Reading patch net.minecraft.network.chat.contents.TranslatableContents.binpatch
    Checksum: f4b5ee54 Exists: true
  Reading patch net.minecraft.network.protocol.common.ClientboundCustomPayloadPacket.binpatch
    Checksum: 75a47411 Exists: true
  Reading patch net.minecraft.network.protocol.common.ServerboundCustomPayloadPacket.binpatch
    Checksum: f1ff2aaa Exists: true
  Reading patch net.minecraft.network.protocol.common.custom.CustomPacketPayload$1.binpatch
    Checksum: be87f407 Exists: true
  Reading patch net.minecraft.network.protocol.common.custom.CustomPacketPayload$FallbackProvider.binpatch
    Checksum: 1ef0da22 Exists: true
  Reading patch net.minecraft.network.protocol.common.custom.CustomPacketPayload$Type.binpatch
    Checksum: eac02591 Exists: true
  Reading patch net.minecraft.network.protocol.common.custom.CustomPacketPayload$TypeAndCodec.binpatch
    Checksum: e4246215 Exists: true
  Reading patch net.minecraft.network.protocol.common.custom.CustomPacketPayload.binpatch
    Checksum: 5d3d6118 Exists: true
  Reading patch net.minecraft.network.protocol.login.custom.DiscardedQueryAnswerPayload.binpatch
    Checksum: c073c8a5 Exists: true
  Reading patch net.minecraft.network.protocol.login.custom.DiscardedQueryPayload.binpatch
    Checksum: f3c2f2aa Exists: true
  Reading patch net.minecraft.network.protocol.status.ClientboundStatusResponsePacket.binpatch
    Checksum: d9665124 Exists: true
  Reading patch net.minecraft.network.protocol.status.ServerStatus$Favicon.binpatch
    Checksum: 7da20de2 Exists: true
  Reading patch net.minecraft.network.protocol.status.ServerStatus$Players.binpatch
    Checksum: 9609d43a Exists: true
  Reading patch net.minecraft.network.protocol.status.ServerStatus$Version.binpatch
    Checksum: 284be670 Exists: true
  Reading patch net.minecraft.network.protocol.status.ServerStatus.binpatch
    Checksum: 8633c95c Exists: true
  Reading patch net.minecraft.network.syncher.EntityDataSerializers$1.binpatch
    Checksum: 50137f5e Exists: true
  Reading patch net.minecraft.network.syncher.EntityDataSerializers$2.binpatch
    Checksum: 88c1866f Exists: true
  Reading patch net.minecraft.network.syncher.EntityDataSerializers$3.binpatch
    Checksum: e4f582f4 Exists: true
  Reading patch net.minecraft.network.syncher.EntityDataSerializers$4.binpatch
    Checksum: 78419ff0 Exists: true
  Reading patch net.minecraft.network.syncher.EntityDataSerializers.binpatch
    Checksum: 8dc01f47 Exists: true
  Reading patch net.minecraft.network.syncher.SynchedEntityData$Builder.binpatch
    Checksum: 38db0ddb Exists: true
  Reading patch net.minecraft.network.syncher.SynchedEntityData$DataItem.binpatch
    Checksum: 43572ff2 Exists: true
  Reading patch net.minecraft.network.syncher.SynchedEntityData$DataValue.binpatch
    Checksum: 419569de Exists: true
  Reading patch net.minecraft.network.syncher.SynchedEntityData.binpatch
    Checksum: 5fd0b865 Exists: true
  Reading patch net.minecraft.recipebook.PlaceRecipe.binpatch
    Checksum: 5cbe063d Exists: true
  Reading patch net.minecraft.resources.DelegatingOps.binpatch
    Checksum: b3e4756f Exists: true
  Reading patch net.minecraft.resources.HolderSetCodec.binpatch
    Checksum: d2c0f388 Exists: true
  Reading patch net.minecraft.resources.RegistryDataLoader$1.binpatch
    Checksum: ca75bc62 Exists: true
  Reading patch net.minecraft.resources.RegistryDataLoader$Loader.binpatch
    Checksum: 4d48e44a Exists: true
  Reading patch net.minecraft.resources.RegistryDataLoader$LoadingFunction.binpatch
    Checksum: a1cf01c6 Exists: true
  Reading patch net.minecraft.resources.RegistryDataLoader$RegistryData.binpatch
    Checksum: 643e0c0a Exists: true
  Reading patch net.minecraft.resources.RegistryDataLoader.binpatch
    Checksum: 7642321c Exists: true
  Reading patch net.minecraft.resources.RegistryOps$HolderLookupAdapter.binpatch
    Checksum: ea6de183 Exists: true
  Reading patch net.minecraft.resources.RegistryOps$RegistryInfo.binpatch
    Checksum: f2f569cf Exists: true
  Reading patch net.minecraft.resources.RegistryOps$RegistryInfoLookup.binpatch
    Checksum: 453be57 Exists: true
  Reading patch net.minecraft.resources.RegistryOps.binpatch
    Checksum: 66d087c7 Exists: true
  Reading patch net.minecraft.resources.ResourceKey$InternKey.binpatch
    Checksum: 2094c062 Exists: true
  Reading patch net.minecraft.resources.ResourceKey.binpatch
    Checksum: a7392a35 Exists: true
  Reading patch net.minecraft.resources.ResourceLocation$Serializer.binpatch
    Checksum: 43828c84 Exists: true
  Reading patch net.minecraft.resources.ResourceLocation.binpatch
    Checksum: 454578c3 Exists: true
  Reading patch net.minecraft.server.Bootstrap$1.binpatch
    Checksum: 33a2e5e7 Exists: true
  Reading patch net.minecraft.server.Bootstrap.binpatch
    Checksum: 8606f3c5 Exists: true
  Reading patch net.minecraft.server.Eula.binpatch
    Checksum: f1b03d14 Exists: true
  Reading patch net.minecraft.server.Main$1.binpatch
    Checksum: f734ba89 Exists: true
  Reading patch net.minecraft.server.Main.binpatch
    Checksum: 4f8c9c91 Exists: true
  Reading patch net.minecraft.server.MinecraftServer$1.binpatch
    Checksum: 702f5f33 Exists: true
  Reading patch net.minecraft.server.MinecraftServer$ReloadableResources.binpatch
    Checksum: 18545218 Exists: true
  Reading patch net.minecraft.server.MinecraftServer$ServerResourcePackInfo.binpatch
    Checksum: fa9c4629 Exists: true
  Reading patch net.minecraft.server.MinecraftServer$TimeProfiler$1.binpatch
    Checksum: 7488c95 Exists: true
  Reading patch net.minecraft.server.MinecraftServer$TimeProfiler.binpatch
    Checksum: 9d47ca67 Exists: true
  Reading patch net.minecraft.server.MinecraftServer.binpatch
    Checksum: 36726a98 Exists: true
  Reading patch net.minecraft.server.PlayerAdvancements$Data.binpatch
    Checksum: 61183607 Exists: true
  Reading patch net.minecraft.server.PlayerAdvancements.binpatch
    Checksum: 96326872 Exists: true
  Reading patch net.minecraft.server.ReloadableServerResources$ConfigurableRegistryLookup$1.binpatch
    Checksum: 4c648401 Exists: true
  Reading patch net.minecraft.server.ReloadableServerResources$ConfigurableRegistryLookup.binpatch
    Checksum: 9b229d68 Exists: true
  Reading patch net.minecraft.server.ReloadableServerResources$MissingTagAccessPolicy.binpatch
    Checksum: 162c730a Exists: true
  Reading patch net.minecraft.server.ReloadableServerResources.binpatch
    Checksum: a40dc820 Exists: true
  Reading patch net.minecraft.server.ServerAdvancementManager.binpatch
    Checksum: 8613e48b Exists: true
  Reading patch net.minecraft.server.advancements.AdvancementVisibilityEvaluator$Output.binpatch
    Checksum: c712887d Exists: true
  Reading patch net.minecraft.server.advancements.AdvancementVisibilityEvaluator$VisibilityRule.binpatch
    Checksum: 7b1997ba Exists: true
  Reading patch net.minecraft.server.advancements.AdvancementVisibilityEvaluator.binpatch
    Checksum: cca42829 Exists: true
  Reading patch net.minecraft.server.commands.SpreadPlayersCommand$Position.binpatch
    Checksum: e4873089 Exists: true
  Reading patch net.minecraft.server.commands.SpreadPlayersCommand.binpatch
    Checksum: 3965aec6 Exists: true
  Reading patch net.minecraft.server.commands.TeleportCommand$LookAt.binpatch
    Checksum: de547eae Exists: true
  Reading patch net.minecraft.server.commands.TeleportCommand$LookAtEntity.binpatch
    Checksum: c8db7cce Exists: true
  Reading patch net.minecraft.server.commands.TeleportCommand$LookAtPosition.binpatch
    Checksum: 5bdb0c2 Exists: true
  Reading patch net.minecraft.server.commands.TeleportCommand.binpatch
    Checksum: fa9e3a59 Exists: true
  Reading patch net.minecraft.server.dedicated.DedicatedServer$1.binpatch
    Checksum: c509cf7c Exists: true
  Reading patch net.minecraft.server.dedicated.DedicatedServer.binpatch
    Checksum: ff58b16c Exists: true
  Reading patch net.minecraft.server.dedicated.ServerWatchdog$1.binpatch
    Checksum: bfd0bf97 Exists: true
  Reading patch net.minecraft.server.dedicated.ServerWatchdog.binpatch
    Checksum: a8108778 Exists: true
  Reading patch net.minecraft.server.dedicated.Settings$MutableValue.binpatch
    Checksum: 153f452c Exists: true
  Reading patch net.minecraft.server.dedicated.Settings.binpatch
    Checksum: f93a335f Exists: true
  Reading patch net.minecraft.server.gui.MinecraftServerGui$1.binpatch
    Checksum: 4e095f2 Exists: true
  Reading patch net.minecraft.server.gui.MinecraftServerGui$2.binpatch
    Checksum: e125cce9 Exists: true
  Reading patch net.minecraft.server.gui.MinecraftServerGui.binpatch
    Checksum: 1ee149b7 Exists: true
  Reading patch net.minecraft.server.level.ChunkMap$DistanceManager.binpatch
    Checksum: 7a649af3 Exists: true
  Reading patch net.minecraft.server.level.ChunkMap$TrackedEntity.binpatch
    Checksum: 33d478ea Exists: true
  Reading patch net.minecraft.server.level.ChunkMap.binpatch
    Checksum: c61573ff Exists: true
  Reading patch net.minecraft.server.level.DistanceManager$ChunkTicketTracker.binpatch
    Checksum: fb255b49 Exists: true
  Reading patch net.minecraft.server.level.DistanceManager$FixedPlayerDistanceChunkTracker.binpatch
    Checksum: c533a466 Exists: true
  Reading patch net.minecraft.server.level.DistanceManager$PlayerTicketTracker.binpatch
    Checksum: fd9513c Exists: true
  Reading patch net.minecraft.server.level.DistanceManager.binpatch
    Checksum: 81e23502 Exists: true
  Reading patch net.minecraft.server.level.GenerationChunkHolder.binpatch
    Checksum: 8988c88e Exists: true
  Reading patch net.minecraft.server.level.ServerChunkCache$ChunkAndHolder.binpatch
    Checksum: eb6dfa2d Exists: true
  Reading patch net.minecraft.server.level.ServerChunkCache$MainThreadExecutor.binpatch
    Checksum: 6648df80 Exists: true
  Reading patch net.minecraft.server.level.ServerChunkCache.binpatch
    Checksum: e3815711 Exists: true
  Reading patch net.minecraft.server.level.ServerEntity.binpatch
    Checksum: 8586c141 Exists: true
  Reading patch net.minecraft.server.level.ServerLevel$EntityCallbacks.binpatch
    Checksum: 724456bf Exists: true
  Reading patch net.minecraft.server.level.ServerLevel.binpatch
    Checksum: 29e5ab64 Exists: true
  Reading patch net.minecraft.server.level.ServerPlayer$1.binpatch
    Checksum: b99334ac Exists: true
  Reading patch net.minecraft.server.level.ServerPlayer$2.binpatch
    Checksum: 4f9d374f Exists: true
  Reading patch net.minecraft.server.level.ServerPlayer$RespawnPosAngle.binpatch
    Checksum: eee6946f Exists: true
  Reading patch net.minecraft.server.level.ServerPlayer.binpatch
    Checksum: 838a055a Exists: true
  Reading patch net.minecraft.server.level.ServerPlayerGameMode.binpatch
    Checksum: c9006ee8 Exists: true
  Reading patch net.minecraft.server.level.Ticket.binpatch
    Checksum: 64e92b6c Exists: true
  Reading patch net.minecraft.server.level.WorldGenRegion.binpatch
    Checksum: dc4248f2 Exists: true
  Reading patch net.minecraft.server.network.ConfigurationTask$Type.binpatch
    Checksum: 3033686d Exists: true
  Reading patch net.minecraft.server.network.ConfigurationTask.binpatch
    Checksum: 251d8f02 Exists: true
  Reading patch net.minecraft.server.network.MemoryServerHandshakePacketListenerImpl.binpatch
    Checksum: 96ab1f8a Exists: true
  Reading patch net.minecraft.server.network.PlayerChunkSender.binpatch
    Checksum: f62e707 Exists: true
  Reading patch net.minecraft.server.network.ServerCommonPacketListenerImpl.binpatch
    Checksum: 7e6f14c0 Exists: true
  Reading patch net.minecraft.server.network.ServerConfigurationPacketListenerImpl.binpatch
    Checksum: 2fc28729 Exists: true
  Reading patch net.minecraft.server.network.ServerConnectionListener$1.binpatch
    Checksum: a9bfa9ab Exists: true
  Reading patch net.minecraft.server.network.ServerConnectionListener$2.binpatch
    Checksum: 773d44de Exists: true
  Reading patch net.minecraft.server.network.ServerConnectionListener$LatencySimulator$DelayedMessage.binpatch
    Checksum: 6f9df8b3 Exists: true
  Reading patch net.minecraft.server.network.ServerConnectionListener$LatencySimulator.binpatch
    Checksum: c6b849ac Exists: true
  Reading patch net.minecraft.server.network.ServerConnectionListener.binpatch
    Checksum: 46c6cae5 Exists: true
  Reading patch net.minecraft.server.network.ServerGamePacketListenerImpl$1.binpatch
    Checksum: 3a02493a Exists: true
  Reading patch net.minecraft.server.network.ServerGamePacketListenerImpl$2.binpatch
    Checksum: 77674f3b Exists: true
  Reading patch net.minecraft.server.network.ServerGamePacketListenerImpl$EntityInteraction.binpatch
    Checksum: 5a54b3af Exists: true
  Reading patch net.minecraft.server.network.ServerGamePacketListenerImpl.binpatch
    Checksum: 9dd3774e Exists: true
  Reading patch net.minecraft.server.network.ServerHandshakePacketListenerImpl$1.binpatch
    Checksum: 1b7fde02 Exists: true
  Reading patch net.minecraft.server.network.ServerHandshakePacketListenerImpl.binpatch
    Checksum: 4b2fad7a Exists: true
  Reading patch net.minecraft.server.network.ServerLoginPacketListenerImpl$1.binpatch
    Checksum: d3f07a96 Exists: true
  Reading patch net.minecraft.server.network.ServerLoginPacketListenerImpl$State.binpatch
    Checksum: 2be8a592 Exists: true
  Reading patch net.minecraft.server.network.ServerLoginPacketListenerImpl.binpatch
    Checksum: eede4ce5 Exists: true
  Reading patch net.minecraft.server.network.ServerStatusPacketListenerImpl.binpatch
    Checksum: bc9480 Exists: true
  Reading patch net.minecraft.server.packs.AbstractPackResources.binpatch
    Checksum: cf0b03e3 Exists: true
  Reading patch net.minecraft.server.packs.PackResources$ResourceOutput.binpatch
    Checksum: 2376adeb Exists: true
  Reading patch net.minecraft.server.packs.PackResources.binpatch
    Checksum: 80a37796 Exists: true
  Reading patch net.minecraft.server.packs.repository.Pack$Metadata.binpatch
    Checksum: a900d9cb Exists: true
  Reading patch net.minecraft.server.packs.repository.Pack$Position.binpatch
    Checksum: 75d7c336 Exists: true
  Reading patch net.minecraft.server.packs.repository.Pack$ResourcesSupplier.binpatch
    Checksum: a240be51 Exists: true
  Reading patch net.minecraft.server.packs.repository.Pack.binpatch
    Checksum: 737b149a Exists: true
  Reading patch net.minecraft.server.packs.repository.PackDetector.binpatch
    Checksum: 143006d7 Exists: true
  Reading patch net.minecraft.server.packs.repository.PackRepository.binpatch
    Checksum: 8e4160c1 Exists: true
  Reading patch net.minecraft.server.packs.repository.ServerPacksSource.binpatch
    Checksum: d6ecbd74 Exists: true
  Reading patch net.minecraft.server.packs.resources.FallbackResourceManager$1ResourceWithSourceAndIndex.binpatch
    Checksum: 45880440 Exists: true
  Reading patch net.minecraft.server.packs.resources.FallbackResourceManager$EntryStack.binpatch
    Checksum: 975c2001 Exists: true
  Reading patch net.minecraft.server.packs.resources.FallbackResourceManager$LeakedResourceWarningInputStream.binpatch
    Checksum: ea228339 Exists: true
  Reading patch net.minecraft.server.packs.resources.FallbackResourceManager$PackEntry.binpatch
    Checksum: 45978aac Exists: true
  Reading patch net.minecraft.server.packs.resources.FallbackResourceManager$ResourceWithSource.binpatch
    Checksum: e26b2e0 Exists: true
  Reading patch net.minecraft.server.packs.resources.FallbackResourceManager.binpatch
    Checksum: a09095e7 Exists: true
  Reading patch net.minecraft.server.packs.resources.ReloadableResourceManager.binpatch
    Checksum: 20fa5ad9 Exists: true
  Reading patch net.minecraft.server.packs.resources.SimpleJsonResourceReloadListener.binpatch
    Checksum: 589b40aa Exists: true
  Reading patch net.minecraft.server.players.PlayerList$1.binpatch
    Checksum: 32373b88 Exists: true
  Reading patch net.minecraft.server.players.PlayerList.binpatch
    Checksum: c1aff9fb Exists: true
  Reading patch net.minecraft.server.rcon.RconConsoleSource.binpatch
    Checksum: bc8d831f Exists: true
  Reading patch net.minecraft.server.rcon.thread.RconClient.binpatch
    Checksum: cb758e2c Exists: true
  Reading patch net.minecraft.stats.RecipeBookSettings$TypeSettings.binpatch
    Checksum: fd0a715c Exists: true
  Reading patch net.minecraft.stats.RecipeBookSettings.binpatch
    Checksum: d5768ef9 Exists: true
  Reading patch net.minecraft.tags.BannerPatternTags.binpatch
    Checksum: c851c55d Exists: true
  Reading patch net.minecraft.tags.BiomeTags.binpatch
    Checksum: c92266b Exists: true
  Reading patch net.minecraft.tags.BlockTags.binpatch
    Checksum: 8e3de02 Exists: true
  Reading patch net.minecraft.tags.CatVariantTags.binpatch
    Checksum: 4f0554a9 Exists: true
  Reading patch net.minecraft.tags.DamageTypeTags.binpatch
    Checksum: 2969257d Exists: true
  Reading patch net.minecraft.tags.EnchantmentTags.binpatch
    Checksum: afb47b35 Exists: true
  Reading patch net.minecraft.tags.EntityTypeTags.binpatch
    Checksum: ad2f3c88 Exists: true
  Reading patch net.minecraft.tags.FlatLevelGeneratorPresetTags.binpatch
    Checksum: b4bf65ff Exists: true
  Reading patch net.minecraft.tags.FluidTags.binpatch
    Checksum: 816c4202 Exists: true
  Reading patch net.minecraft.tags.GameEventTags.binpatch
    Checksum: 65427eda Exists: true
  Reading patch net.minecraft.tags.InstrumentTags.binpatch
    Checksum: efeb468d Exists: true
  Reading patch net.minecraft.tags.ItemTags.binpatch
    Checksum: 6fed33d8 Exists: true
  Reading patch net.minecraft.tags.PaintingVariantTags.binpatch
    Checksum: 90c053d8 Exists: true
  Reading patch net.minecraft.tags.PoiTypeTags.binpatch
    Checksum: c2e55c39 Exists: true
  Reading patch net.minecraft.tags.StructureTags.binpatch
    Checksum: e600558b Exists: true
  Reading patch net.minecraft.tags.TagBuilder.binpatch
    Checksum: 13407249 Exists: true
  Reading patch net.minecraft.tags.TagEntry$Lookup.binpatch
    Checksum: d3f6bf87 Exists: true
  Reading patch net.minecraft.tags.TagEntry.binpatch
    Checksum: f630dec6 Exists: true
  Reading patch net.minecraft.tags.TagFile.binpatch
    Checksum: 3b2badf8 Exists: true
  Reading patch net.minecraft.tags.TagKey.binpatch
    Checksum: 7e20da51 Exists: true
  Reading patch net.minecraft.tags.TagLoader$1.binpatch
    Checksum: bf30be1a Exists: true
  Reading patch net.minecraft.tags.TagLoader$EntryWithSource.binpatch
    Checksum: 99aa1209 Exists: true
  Reading patch net.minecraft.tags.TagLoader$SortingEntry.binpatch
    Checksum: 345cb5e2 Exists: true
  Reading patch net.minecraft.tags.TagLoader.binpatch
    Checksum: 3d562b23 Exists: true
  Reading patch net.minecraft.tags.WorldPresetTags.binpatch
    Checksum: 274a57ca Exists: true
  Reading patch net.minecraft.util.SpawnUtil$Strategy.binpatch
    Checksum: a30072ca Exists: true
  Reading patch net.minecraft.util.SpawnUtil.binpatch
    Checksum: 511626ce Exists: true
  Reading patch net.minecraft.util.datafix.fixes.StructuresBecomeConfiguredFix$Conversion.binpatch
    Checksum: 643983d8 Exists: true
  Reading patch net.minecraft.util.datafix.fixes.StructuresBecomeConfiguredFix.binpatch
    Checksum: 194f8ea1 Exists: true
  Reading patch net.minecraft.util.datafix.schemas.V2832.binpatch
    Checksum: 7f741ec2 Exists: true
  Reading patch net.minecraft.world.effect.MobEffect$AttributeTemplate.binpatch
    Checksum: 370889f2 Exists: true
  Reading patch net.minecraft.world.effect.MobEffect.binpatch
    Checksum: a27ab7cf Exists: true
  Reading patch net.minecraft.world.entity.Entity$1.binpatch
    Checksum: eed1fc74 Exists: true
  Reading patch net.minecraft.world.entity.Entity$MoveFunction.binpatch
    Checksum: d94f66a8 Exists: true
  Reading patch net.minecraft.world.entity.Entity$MovementEmission.binpatch
    Checksum: 5e5c83fc Exists: true
  Reading patch net.minecraft.world.entity.Entity$RemovalReason.binpatch
    Checksum: 2e1182a0 Exists: true
  Reading patch net.minecraft.world.entity.Entity.binpatch
    Checksum: d6b84291 Exists: true
  Reading patch net.minecraft.world.entity.EntityType$1.binpatch
    Checksum: 6c04761f Exists: true
  Reading patch net.minecraft.world.entity.EntityType$Builder.binpatch
    Checksum: 2ce6f6eb Exists: true
  Reading patch net.minecraft.world.entity.EntityType$EntityFactory.binpatch
    Checksum: 4dba8ad Exists: true
  Reading patch net.minecraft.world.entity.EntityType.binpatch
    Checksum: 709cf2b2 Exists: true
  Reading patch net.minecraft.world.entity.ExperienceOrb.binpatch
    Checksum: 661b3834 Exists: true
  Reading patch net.minecraft.world.entity.FlyingMob.binpatch
    Checksum: 40bbca6e Exists: true
  Reading patch net.minecraft.world.entity.LightningBolt.binpatch
    Checksum: b9feaa95 Exists: true
  Reading patch net.minecraft.world.entity.LivingEntity$1.binpatch
    Checksum: 82dd328f Exists: true
  Reading patch net.minecraft.world.entity.LivingEntity$Fallsounds.binpatch
    Checksum: 9905b77b Exists: true
  Reading patch net.minecraft.world.entity.LivingEntity.binpatch
    Checksum: 48462041 Exists: true
  Reading patch net.minecraft.world.entity.Mob$1.binpatch
    Checksum: 48870cbe Exists: true
  Reading patch net.minecraft.world.entity.Mob.binpatch
    Checksum: 77e2ef73 Exists: true
  Reading patch net.minecraft.world.entity.MobCategory.binpatch
    Checksum: 8a6f32b9 Exists: true
  Reading patch net.minecraft.world.entity.Shearable.binpatch
    Checksum: 9f27342a Exists: true
  Reading patch net.minecraft.world.entity.SpawnPlacementTypes$1.binpatch
    Checksum: 97d86720 Exists: true
  Reading patch net.minecraft.world.entity.SpawnPlacementTypes.binpatch
    Checksum: 1183db5d Exists: true
  Reading patch net.minecraft.world.entity.SpawnPlacements$Data.binpatch
    Checksum: e6db17d7 Exists: true
  Reading patch net.minecraft.world.entity.SpawnPlacements$SpawnPredicate.binpatch
    Checksum: be0c12e9 Exists: true
  Reading patch net.minecraft.world.entity.SpawnPlacements.binpatch
    Checksum: edf8ac1d Exists: true
  Reading patch net.minecraft.world.entity.TamableAnimal$TamableAnimalPanicGoal.binpatch
    Checksum: 63ca8024 Exists: true
  Reading patch net.minecraft.world.entity.TamableAnimal.binpatch
    Checksum: 2b6c6813 Exists: true
  Reading patch net.minecraft.world.entity.ai.Brain$1.binpatch
    Checksum: f0629357 Exists: true
  Reading patch net.minecraft.world.entity.ai.Brain$MemoryValue.binpatch
    Checksum: 3132dcd0 Exists: true
  Reading patch net.minecraft.world.entity.ai.Brain$Provider.binpatch
    Checksum: 92b12135 Exists: true
  Reading patch net.minecraft.world.entity.ai.Brain.binpatch
    Checksum: 1c69d383 Exists: true
  Reading patch net.minecraft.world.entity.ai.attributes.AttributeSupplier$Builder.binpatch
    Checksum: 17f139b Exists: true
  Reading patch net.minecraft.world.entity.ai.attributes.AttributeSupplier.binpatch
    Checksum: 38311abb Exists: true
  Reading patch net.minecraft.world.entity.ai.attributes.DefaultAttributes.binpatch
    Checksum: d48d7da6 Exists: true
  Reading patch net.minecraft.world.entity.ai.behavior.CrossbowAttack$CrossbowState.binpatch
    Checksum: a64478a3 Exists: true
  Reading patch net.minecraft.world.entity.ai.behavior.CrossbowAttack.binpatch
    Checksum: 4018b816 Exists: true
  Reading patch net.minecraft.world.entity.ai.behavior.HarvestFarmland.binpatch
    Checksum: 9a16c584 Exists: true
  Reading patch net.minecraft.world.entity.ai.behavior.StartAttacking.binpatch
    Checksum: 26f672a7 Exists: true
  Reading patch net.minecraft.world.entity.ai.behavior.Swim.binpatch
    Checksum: 61e47c38 Exists: true
  Reading patch net.minecraft.world.entity.ai.goal.BreakDoorGoal.binpatch
    Checksum: 99cdf361 Exists: true
  Reading patch net.minecraft.world.entity.ai.goal.EatBlockGoal.binpatch
    Checksum: ec826709 Exists: true
  Reading patch net.minecraft.world.entity.ai.goal.FloatGoal.binpatch
    Checksum: 9799b359 Exists: true
  Reading patch net.minecraft.world.entity.ai.goal.MeleeAttackGoal.binpatch
    Checksum: ccfd3395 Exists: true
  Reading patch net.minecraft.world.entity.ai.goal.RangedBowAttackGoal.binpatch
    Checksum: cd4c5e47 Exists: true
  Reading patch net.minecraft.world.entity.ai.goal.RangedCrossbowAttackGoal$CrossbowState.binpatch
    Checksum: e2ec8b9b Exists: true
  Reading patch net.minecraft.world.entity.ai.goal.RangedCrossbowAttackGoal.binpatch
    Checksum: 3d27a609 Exists: true
  Reading patch net.minecraft.world.entity.ai.goal.RemoveBlockGoal.binpatch
    Checksum: f83e8fdd Exists: true
  Reading patch net.minecraft.world.entity.ai.goal.RunAroundLikeCrazyGoal.binpatch
    Checksum: d786f517 Exists: true
  Reading patch net.minecraft.world.entity.ai.goal.target.HurtByTargetGoal.binpatch
    Checksum: 7badd268 Exists: true
  Reading patch net.minecraft.world.entity.ai.navigation.PathNavigation.binpatch
    Checksum: 49c73d89 Exists: true
  Reading patch net.minecraft.world.entity.ai.navigation.WallClimberNavigation.binpatch
    Checksum: c66b72e1 Exists: true
  Reading patch net.minecraft.world.entity.ai.village.VillageSiege$State.binpatch
    Checksum: d87e50a5 Exists: true
  Reading patch net.minecraft.world.entity.ai.village.VillageSiege.binpatch
    Checksum: 5470439c Exists: true
  Reading patch net.minecraft.world.entity.ai.village.poi.PoiTypes.binpatch
    Checksum: 503c3f19 Exists: true
  Reading patch net.minecraft.world.entity.animal.Animal.binpatch
    Checksum: 6aae9b18 Exists: true
  Reading patch net.minecraft.world.entity.animal.Bee$1.binpatch
    Checksum: cc82e55c Exists: true
  Reading patch net.minecraft.world.entity.animal.Bee$BaseBeeGoal.binpatch
    Checksum: 9f40bd66 Exists: true
  Reading patch net.minecraft.world.entity.animal.Bee$BeeAttackGoal.binpatch
    Checksum: 2a9904f0 Exists: true
  Reading patch net.minecraft.world.entity.animal.Bee$BeeBecomeAngryTargetGoal.binpatch
    Checksum: e89c7af0 Exists: true
  Reading patch net.minecraft.world.entity.animal.Bee$BeeEnterHiveGoal.binpatch
    Checksum: c97ec16f Exists: true
  Reading patch net.minecraft.world.entity.animal.Bee$BeeGoToHiveGoal.binpatch
    Checksum: 63d20be9 Exists: true
  Reading patch net.minecraft.world.entity.animal.Bee$BeeGoToKnownFlowerGoal.binpatch
    Checksum: 8e4f8e30 Exists: true
  Reading patch net.minecraft.world.entity.animal.Bee$BeeGrowCropGoal.binpatch
    Checksum: c1c49140 Exists: true
  Reading patch net.minecraft.world.entity.animal.Bee$BeeHurtByOtherGoal.binpatch
    Checksum: f9e57057 Exists: true
  Reading patch net.minecraft.world.entity.animal.Bee$BeeLocateHiveGoal.binpatch
    Checksum: 8d0b06a Exists: true
  Reading patch net.minecraft.world.entity.animal.Bee$BeeLookControl.binpatch
    Checksum: d0d91db4 Exists: true
  Reading patch net.minecraft.world.entity.animal.Bee$BeePollinateGoal.binpatch
    Checksum: 81158537 Exists: true
  Reading patch net.minecraft.world.entity.animal.Bee$BeeWanderGoal.binpatch
    Checksum: 648058d7 Exists: true
  Reading patch net.minecraft.world.entity.animal.Bee.binpatch
    Checksum: cfbaf2b9 Exists: true
  Reading patch net.minecraft.world.entity.animal.Cat$CatAvoidEntityGoal.binpatch
    Checksum: 30c77895 Exists: true
  Reading patch net.minecraft.world.entity.animal.Cat$CatRelaxOnOwnerGoal.binpatch
    Checksum: e5184911 Exists: true
  Reading patch net.minecraft.world.entity.animal.Cat$CatTemptGoal.binpatch
    Checksum: e4b1fd9f Exists: true
  Reading patch net.minecraft.world.entity.animal.Cat.binpatch
    Checksum: 191e3814 Exists: true
  Reading patch net.minecraft.world.entity.animal.Fox$DefendTrustedTargetGoal.binpatch
    Checksum: 949bfc13 Exists: true
  Reading patch net.minecraft.world.entity.animal.Fox$FaceplantGoal.binpatch
    Checksum: 6940331c Exists: true
  Reading patch net.minecraft.world.entity.animal.Fox$FoxAlertableEntitiesSelector.binpatch
    Checksum: fed9c41a Exists: true
  Reading patch net.minecraft.world.entity.animal.Fox$FoxBehaviorGoal.binpatch
    Checksum: 2b288960 Exists: true
  Reading patch net.minecraft.world.entity.animal.Fox$FoxBreedGoal.binpatch
    Checksum: 612bc5f5 Exists: true
  Reading patch net.minecraft.world.entity.animal.Fox$FoxEatBerriesGoal.binpatch
    Checksum: 7b03bfff Exists: true
  Reading patch net.minecraft.world.entity.animal.Fox$FoxFloatGoal.binpatch
    Checksum: 634ff997 Exists: true
  Reading patch net.minecraft.world.entity.animal.Fox$FoxFollowParentGoal.binpatch
    Checksum: 5461001f Exists: true
  Reading patch net.minecraft.world.entity.animal.Fox$FoxGroupData.binpatch
    Checksum: ebcecc9f Exists: true
  Reading patch net.minecraft.world.entity.animal.Fox$FoxLookAtPlayerGoal.binpatch
    Checksum: eed5d33 Exists: true
  Reading patch net.minecraft.world.entity.animal.Fox$FoxLookControl.binpatch
    Checksum: b1f1e653 Exists: true
  Reading patch net.minecraft.world.entity.animal.Fox$FoxMeleeAttackGoal.binpatch
    Checksum: 1a8fa4ac Exists: true
  Reading patch net.minecraft.world.entity.animal.Fox$FoxMoveControl.binpatch
    Checksum: 6f2fc2cd Exists: true
  Reading patch net.minecraft.world.entity.animal.Fox$FoxPanicGoal.binpatch
    Checksum: 8d2ac60a Exists: true
  Reading patch net.minecraft.world.entity.animal.Fox$FoxPounceGoal.binpatch
    Checksum: 3398de6 Exists: true
  Reading patch net.minecraft.world.entity.animal.Fox$FoxSearchForItemsGoal.binpatch
    Checksum: 3fb93281 Exists: true
  Reading patch net.minecraft.world.entity.animal.Fox$FoxStrollThroughVillageGoal.binpatch
    Checksum: a182374 Exists: true
  Reading patch net.minecraft.world.entity.animal.Fox$PerchAndSearchGoal.binpatch
    Checksum: 8b509e34 Exists: true
  Reading patch net.minecraft.world.entity.animal.Fox$SeekShelterGoal.binpatch
    Checksum: 53d4af27 Exists: true
  Reading patch net.minecraft.world.entity.animal.Fox$SleepGoal.binpatch
    Checksum: 3dae962d Exists: true
  Reading patch net.minecraft.world.entity.animal.Fox$StalkPreyGoal.binpatch
    Checksum: 937ec448 Exists: true
  Reading patch net.minecraft.world.entity.animal.Fox$Type.binpatch
    Checksum: c968920d Exists: true
  Reading patch net.minecraft.world.entity.animal.Fox.binpatch
    Checksum: 9404b93e Exists: true
  Reading patch net.minecraft.world.entity.animal.MushroomCow$MushroomType.binpatch
    Checksum: f83578fa Exists: true
  Reading patch net.minecraft.world.entity.animal.MushroomCow.binpatch
    Checksum: 20ab0ba7 Exists: true
  Reading patch net.minecraft.world.entity.animal.Ocelot$OcelotAvoidEntityGoal.binpatch
    Checksum: 8054849d Exists: true
  Reading patch net.minecraft.world.entity.animal.Ocelot$OcelotTemptGoal.binpatch
    Checksum: f924955 Exists: true
  Reading patch net.minecraft.world.entity.animal.Ocelot.binpatch
    Checksum: aeb81a85 Exists: true
  Reading patch net.minecraft.world.entity.animal.Parrot$1.binpatch
    Checksum: 65ef1ddf Exists: true
  Reading patch net.minecraft.world.entity.animal.Parrot$ParrotWanderGoal.binpatch
    Checksum: be98f5b7 Exists: true
  Reading patch net.minecraft.world.entity.animal.Parrot$Variant.binpatch
    Checksum: 44d0ea6a Exists: true
  Reading patch net.minecraft.world.entity.animal.Parrot.binpatch
    Checksum: f4aa57ee Exists: true
  Reading patch net.minecraft.world.entity.animal.Pig.binpatch
    Checksum: 451c118c Exists: true
  Reading patch net.minecraft.world.entity.animal.Rabbit$RabbitAvoidEntityGoal.binpatch
    Checksum: c5d8a384 Exists: true
  Reading patch net.minecraft.world.entity.animal.Rabbit$RabbitGroupData.binpatch
    Checksum: fab6dece Exists: true
  Reading patch net.minecraft.world.entity.animal.Rabbit$RabbitJumpControl.binpatch
    Checksum: 7dd2ecfe Exists: true
  Reading patch net.minecraft.world.entity.animal.Rabbit$RabbitMoveControl.binpatch
    Checksum: bc7a6014 Exists: true
  Reading patch net.minecraft.world.entity.animal.Rabbit$RabbitPanicGoal.binpatch
    Checksum: 748dcaa2 Exists: true
  Reading patch net.minecraft.world.entity.animal.Rabbit$RaidGardenGoal.binpatch
    Checksum: 5a71562d Exists: true
  Reading patch net.minecraft.world.entity.animal.Rabbit$Variant.binpatch
    Checksum: 5a8c4e7 Exists: true
  Reading patch net.minecraft.world.entity.animal.Rabbit.binpatch
    Checksum: 231e1d05 Exists: true
  Reading patch net.minecraft.world.entity.animal.Sheep$1.binpatch
    Checksum: 10aa147c Exists: true
  Reading patch net.minecraft.world.entity.animal.Sheep.binpatch
    Checksum: 912381b4 Exists: true
  Reading patch net.minecraft.world.entity.animal.SnowGolem.binpatch
    Checksum: 239d239c Exists: true
  Reading patch net.minecraft.world.entity.animal.Wolf$WolfAvoidEntityGoal.binpatch
    Checksum: 37b14074 Exists: true
  Reading patch net.minecraft.world.entity.animal.Wolf$WolfPackData.binpatch
    Checksum: a3f1f808 Exists: true
  Reading patch net.minecraft.world.entity.animal.Wolf.binpatch
    Checksum: 4b08e24f Exists: true
  Reading patch net.minecraft.world.entity.animal.allay.Allay$JukeboxListener.binpatch
    Checksum: 598ddd56 Exists: true
  Reading patch net.minecraft.world.entity.animal.allay.Allay$VibrationUser.binpatch
    Checksum: abfd6336 Exists: true
  Reading patch net.minecraft.world.entity.animal.allay.Allay.binpatch
    Checksum: b46da566 Exists: true
  Reading patch net.minecraft.world.entity.animal.camel.Camel$CamelBodyRotationControl.binpatch
    Checksum: 390f8e1 Exists: true
  Reading patch net.minecraft.world.entity.animal.camel.Camel$CamelLookControl.binpatch
    Checksum: c22bd715 Exists: true
  Reading patch net.minecraft.world.entity.animal.camel.Camel$CamelMoveControl.binpatch
    Checksum: ff6b2aa4 Exists: true
  Reading patch net.minecraft.world.entity.animal.camel.Camel.binpatch
    Checksum: 63c5d9a3 Exists: true
  Reading patch net.minecraft.world.entity.animal.frog.Tadpole.binpatch
    Checksum: 79902900 Exists: true
  Reading patch net.minecraft.world.entity.animal.horse.AbstractHorse$1.binpatch
    Checksum: 60337551 Exists: true
  Reading patch net.minecraft.world.entity.animal.horse.AbstractHorse$2.binpatch
    Checksum: 7174a179 Exists: true
  Reading patch net.minecraft.world.entity.animal.horse.AbstractHorse.binpatch
    Checksum: 66084779 Exists: true
  Reading patch net.minecraft.world.entity.animal.horse.Llama$LlamaAttackWolfGoal.binpatch
    Checksum: 7b39f0f8 Exists: true
  Reading patch net.minecraft.world.entity.animal.horse.Llama$LlamaGroupData.binpatch
    Checksum: 3024e64f Exists: true
  Reading patch net.minecraft.world.entity.animal.horse.Llama$LlamaHurtByTargetGoal.binpatch
    Checksum: 7eab040f Exists: true
  Reading patch net.minecraft.world.entity.animal.horse.Llama$Variant.binpatch
    Checksum: fd0ee654 Exists: true
  Reading patch net.minecraft.world.entity.animal.horse.Llama.binpatch
    Checksum: bbfb77fe Exists: true
  Reading patch net.minecraft.world.entity.animal.horse.SkeletonTrapGoal.binpatch
    Checksum: e5cfab14 Exists: true
  Reading patch net.minecraft.world.entity.animal.sniffer.Sniffer$State.binpatch
    Checksum: 820085f5 Exists: true
  Reading patch net.minecraft.world.entity.animal.sniffer.Sniffer.binpatch
    Checksum: e00e14c5 Exists: true
  Reading patch net.minecraft.world.entity.boss.EnderDragonPart.binpatch
    Checksum: a59c6c62 Exists: true
  Reading patch net.minecraft.world.entity.boss.enderdragon.EnderDragon.binpatch
    Checksum: 206d6217 Exists: true
  Reading patch net.minecraft.world.entity.boss.wither.WitherBoss$WitherDoNothingGoal.binpatch
    Checksum: 63fd2cbf Exists: true
  Reading patch net.minecraft.world.entity.boss.wither.WitherBoss.binpatch
    Checksum: 912fb38a Exists: true
  Reading patch net.minecraft.world.entity.decoration.ArmorStand$1.binpatch
    Checksum: a82ecc65 Exists: true
  Reading patch net.minecraft.world.entity.decoration.ArmorStand.binpatch
    Checksum: 66b4b96d Exists: true
  Reading patch net.minecraft.world.entity.decoration.HangingEntity$1.binpatch
    Checksum: 6a30c3bf Exists: true
  Reading patch net.minecraft.world.entity.decoration.HangingEntity.binpatch
    Checksum: 357b94a3 Exists: true
  Reading patch net.minecraft.world.entity.item.FallingBlockEntity.binpatch
    Checksum: 2e102de Exists: true
  Reading patch net.minecraft.world.entity.item.ItemEntity.binpatch
    Checksum: 4d8923ba Exists: true
  Reading patch net.minecraft.world.entity.monster.AbstractSkeleton$1.binpatch
    Checksum: 41080ed4 Exists: true
  Reading patch net.minecraft.world.entity.monster.AbstractSkeleton.binpatch
    Checksum: 55873d6d Exists: true
  Reading patch net.minecraft.world.entity.monster.Bogged.binpatch
    Checksum: 4194529f Exists: true
  Reading patch net.minecraft.world.entity.monster.CrossbowAttackMob.binpatch
    Checksum: 2add49ed Exists: true
  Reading patch net.minecraft.world.entity.monster.EnderMan$EndermanFreezeWhenLookedAt.binpatch
    Checksum: 205e2fdc Exists: true
  Reading patch net.minecraft.world.entity.monster.EnderMan$EndermanLeaveBlockGoal.binpatch
    Checksum: 82b96f49 Exists: true
  Reading patch net.minecraft.world.entity.monster.EnderMan$EndermanLookForPlayerGoal.binpatch
    Checksum: ea918ab3 Exists: true
  Reading patch net.minecraft.world.entity.monster.EnderMan$EndermanTakeBlockGoal.binpatch
    Checksum: d3710e11 Exists: true
  Reading patch net.minecraft.world.entity.monster.EnderMan.binpatch
    Checksum: 1117453a Exists: true
  Reading patch net.minecraft.world.entity.monster.Evoker$EvokerAttackSpellGoal.binpatch
    Checksum: 537ce53d Exists: true
  Reading patch net.minecraft.world.entity.monster.Evoker$EvokerCastingSpellGoal.binpatch
    Checksum: b72e8c67 Exists: true
  Reading patch net.minecraft.world.entity.monster.Evoker$EvokerSummonSpellGoal.binpatch
    Checksum: aebf4657 Exists: true
  Reading patch net.minecraft.world.entity.monster.Evoker$EvokerWololoSpellGoal.binpatch
    Checksum: a6cebb0c Exists: true
  Reading patch net.minecraft.world.entity.monster.Evoker.binpatch
    Checksum: 1a3d9b16 Exists: true
  Reading patch net.minecraft.world.entity.monster.Husk.binpatch
    Checksum: b6618e1d Exists: true
  Reading patch net.minecraft.world.entity.monster.Illusioner$IllusionerBlindnessSpellGoal.binpatch
    Checksum: 53150c4e Exists: true
  Reading patch net.minecraft.world.entity.monster.Illusioner$IllusionerMirrorSpellGoal.binpatch
    Checksum: 11da372a Exists: true
  Reading patch net.minecraft.world.entity.monster.Illusioner.binpatch
    Checksum: bfdb1a1f Exists: true
  Reading patch net.minecraft.world.entity.monster.MagmaCube.binpatch
    Checksum: 575867a Exists: true
  Reading patch net.minecraft.world.entity.monster.Monster.binpatch
    Checksum: a91493d1 Exists: true
  Reading patch net.minecraft.world.entity.monster.Pillager.binpatch
    Checksum: d0ab837d Exists: true
  Reading patch net.minecraft.world.entity.monster.Ravager.binpatch
    Checksum: 1e0ee8aa Exists: true
  Reading patch net.minecraft.world.entity.monster.Shulker$ShulkerAttackGoal.binpatch
    Checksum: faef9bd6 Exists: true
  Reading patch net.minecraft.world.entity.monster.Shulker$ShulkerBodyRotationControl.binpatch
    Checksum: d766b5b3 Exists: true
  Reading patch net.minecraft.world.entity.monster.Shulker$ShulkerDefenseAttackGoal.binpatch
    Checksum: 8e84e67e Exists: true
  Reading patch net.minecraft.world.entity.monster.Shulker$ShulkerLookControl.binpatch
    Checksum: 644a9936 Exists: true
  Reading patch net.minecraft.world.entity.monster.Shulker$ShulkerNearestAttackGoal.binpatch
    Checksum: 1045bca Exists: true
  Reading patch net.minecraft.world.entity.monster.Shulker$ShulkerPeekGoal.binpatch
    Checksum: fa3584a7 Exists: true
  Reading patch net.minecraft.world.entity.monster.Shulker.binpatch
    Checksum: 210a0549 Exists: true
  Reading patch net.minecraft.world.entity.monster.Silverfish$SilverfishMergeWithStoneGoal.binpatch
    Checksum: 3eab57e7 Exists: true
  Reading patch net.minecraft.world.entity.monster.Silverfish$SilverfishWakeUpFriendsGoal.binpatch
    Checksum: 17642ef6 Exists: true
  Reading patch net.minecraft.world.entity.monster.Silverfish.binpatch
    Checksum: 988d2063 Exists: true
  Reading patch net.minecraft.world.entity.monster.Skeleton.binpatch
    Checksum: 3ec21dae Exists: true
  Reading patch net.minecraft.world.entity.monster.Slime$SlimeAttackGoal.binpatch
    Checksum: 213f165c Exists: true
  Reading patch net.minecraft.world.entity.monster.Slime$SlimeFloatGoal.binpatch
    Checksum: e5740a33 Exists: true
  Reading patch net.minecraft.world.entity.monster.Slime$SlimeKeepOnJumpingGoal.binpatch
    Checksum: 13226e7c Exists: true
  Reading patch net.minecraft.world.entity.monster.Slime$SlimeMoveControl.binpatch
    Checksum: d6187215 Exists: true
  Reading patch net.minecraft.world.entity.monster.Slime$SlimeRandomDirectionGoal.binpatch
    Checksum: 7a10fdcc Exists: true
  Reading patch net.minecraft.world.entity.monster.Slime.binpatch
    Checksum: 58740be3 Exists: true
  Reading patch net.minecraft.world.entity.monster.Spider$SpiderAttackGoal.binpatch
    Checksum: c27c4170 Exists: true
  Reading patch net.minecraft.world.entity.monster.Spider$SpiderEffectsGroupData.binpatch
    Checksum: f1f93b13 Exists: true
  Reading patch net.minecraft.world.entity.monster.Spider$SpiderTargetGoal.binpatch
    Checksum: 4e4f7d2d Exists: true
  Reading patch net.minecraft.world.entity.monster.Spider.binpatch
    Checksum: 90cc7573 Exists: true
  Reading patch net.minecraft.world.entity.monster.Zombie$ZombieAttackTurtleEggGoal.binpatch
    Checksum: 3549916b Exists: true
  Reading patch net.minecraft.world.entity.monster.Zombie$ZombieGroupData.binpatch
    Checksum: 3a439452 Exists: true
  Reading patch net.minecraft.world.entity.monster.Zombie.binpatch
    Checksum: a9ff72d9 Exists: true
  Reading patch net.minecraft.world.entity.monster.ZombieVillager.binpatch
    Checksum: 77941024 Exists: true
  Reading patch net.minecraft.world.entity.monster.hoglin.Hoglin.binpatch
    Checksum: 2c4d407b Exists: true
  Reading patch net.minecraft.world.entity.monster.piglin.AbstractPiglin.binpatch
    Checksum: 48dbbf99 Exists: true
  Reading patch net.minecraft.world.entity.monster.piglin.Piglin.binpatch
    Checksum: f99cce44 Exists: true
  Reading patch net.minecraft.world.entity.monster.piglin.PiglinAi.binpatch
    Checksum: 7d16b649 Exists: true
  Reading patch net.minecraft.world.entity.monster.piglin.StopHoldingItemIfNoLongerAdmiring.binpatch
    Checksum: 8ccd6912 Exists: true
  Reading patch net.minecraft.world.entity.npc.AbstractVillager.binpatch
    Checksum: fbb271b7 Exists: true
  Reading patch net.minecraft.world.entity.npc.CatSpawner.binpatch
    Checksum: 6457e8c8 Exists: true
  Reading patch net.minecraft.world.entity.npc.Villager.binpatch
    Checksum: 313186d Exists: true
  Reading patch net.minecraft.world.entity.player.Inventory.binpatch
    Checksum: e7ce26e2 Exists: true
  Reading patch net.minecraft.world.entity.player.Player$1.binpatch
    Checksum: b0c8356c Exists: true
  Reading patch net.minecraft.world.entity.player.Player$2.binpatch
    Checksum: ca1d9fac Exists: true
  Reading patch net.minecraft.world.entity.player.Player$BedSleepingProblem.binpatch
    Checksum: e2e65899 Exists: true
  Reading patch net.minecraft.world.entity.player.Player.binpatch
    Checksum: 18831612 Exists: true
  Reading patch net.minecraft.world.entity.projectile.AbstractArrow$1.binpatch
    Checksum: 0 Exists: false
  Reading patch net.minecraft.world.entity.projectile.AbstractArrow$Pickup.binpatch
    Checksum: 160284d7 Exists: true
  Reading patch net.minecraft.world.entity.projectile.AbstractArrow.binpatch
    Checksum: 80ec20a7 Exists: true
  Reading patch net.minecraft.world.entity.projectile.AbstractHurtingProjectile.binpatch
    Checksum: ecb0f937 Exists: true
  Reading patch net.minecraft.world.entity.projectile.FireworkRocketEntity.binpatch
    Checksum: 75ad3fa8 Exists: true
  Reading patch net.minecraft.world.entity.projectile.FishingHook$FishHookState.binpatch
    Checksum: e6ba624f Exists: true
  Reading patch net.minecraft.world.entity.projectile.FishingHook$OpenWaterType.binpatch
    Checksum: fba565db Exists: true
  Reading patch net.minecraft.world.entity.projectile.FishingHook.binpatch
    Checksum: 737c3857 Exists: true
  Reading patch net.minecraft.world.entity.projectile.LargeFireball.binpatch
    Checksum: ec38e2f5 Exists: true
  Reading patch net.minecraft.world.entity.projectile.LlamaSpit.binpatch
    Checksum: 64f995f5 Exists: true
  Reading patch net.minecraft.world.entity.projectile.Projectile.binpatch
    Checksum: 556b7d3 Exists: true
  Reading patch net.minecraft.world.entity.projectile.ProjectileUtil.binpatch
    Checksum: 4ce2c492 Exists: true
  Reading patch net.minecraft.world.entity.projectile.ShulkerBullet.binpatch
    Checksum: dc419db7 Exists: true
  Reading patch net.minecraft.world.entity.projectile.SmallFireball.binpatch
    Checksum: 7504a21e Exists: true
  Reading patch net.minecraft.world.entity.projectile.ThrowableProjectile.binpatch
    Checksum: f6527464 Exists: true
  Reading patch net.minecraft.world.entity.projectile.ThrownEnderpearl.binpatch
    Checksum: 32638ce4 Exists: true
  Reading patch net.minecraft.world.entity.projectile.WitherSkull.binpatch
    Checksum: a3a6009 Exists: true
  Reading patch net.minecraft.world.entity.raid.Raid$1.binpatch
    Checksum: 1f319f8c Exists: true
  Reading patch net.minecraft.world.entity.raid.Raid$RaidStatus.binpatch
    Checksum: aceaac7 Exists: true
  Reading patch net.minecraft.world.entity.raid.Raid$RaiderType.binpatch
    Checksum: 5a5916d3 Exists: true
  Reading patch net.minecraft.world.entity.raid.Raid.binpatch
    Checksum: e707066b Exists: true
  Reading patch net.minecraft.world.entity.vehicle.AbstractMinecart$1.binpatch
    Checksum: be54e479 Exists: true
  Reading patch net.minecraft.world.entity.vehicle.AbstractMinecart$Type.binpatch
    Checksum: 892e7473 Exists: true
  Reading patch net.minecraft.world.entity.vehicle.AbstractMinecart.binpatch
    Checksum: ae49c255 Exists: true
  Reading patch net.minecraft.world.entity.vehicle.AbstractMinecartContainer.binpatch
    Checksum: 86baa35a Exists: true
  Reading patch net.minecraft.world.entity.vehicle.Boat$Status.binpatch
    Checksum: 1c0b4bce Exists: true
  Reading patch net.minecraft.world.entity.vehicle.Boat$Type.binpatch
    Checksum: d151ef1d Exists: true
  Reading patch net.minecraft.world.entity.vehicle.Boat.binpatch
    Checksum: aeafd663 Exists: true
  Reading patch net.minecraft.world.entity.vehicle.ChestBoat$1.binpatch
    Checksum: bbb6f2d8 Exists: true
  Reading patch net.minecraft.world.entity.vehicle.ChestBoat.binpatch
    Checksum: 63d3c060 Exists: true
  Reading patch net.minecraft.world.entity.vehicle.ContainerEntity$1.binpatch
    Checksum: 8fe3bf2 Exists: true
  Reading patch net.minecraft.world.entity.vehicle.ContainerEntity.binpatch
    Checksum: 4be0220b Exists: true
  Reading patch net.minecraft.world.entity.vehicle.Minecart.binpatch
    Checksum: 583ec9f6 Exists: true
  Reading patch net.minecraft.world.entity.vehicle.MinecartCommandBlock$MinecartCommandBase.binpatch
    Checksum: 2c9cb9e0 Exists: true
  Reading patch net.minecraft.world.entity.vehicle.MinecartCommandBlock.binpatch
    Checksum: 492b66d2 Exists: true
  Reading patch net.minecraft.world.entity.vehicle.MinecartFurnace.binpatch
    Checksum: 10b0dc31 Exists: true
  Reading patch net.minecraft.world.entity.vehicle.MinecartSpawner$1.binpatch
    Checksum: 952f1400 Exists: true
  Reading patch net.minecraft.world.entity.vehicle.MinecartSpawner.binpatch
    Checksum: 385fe1ee Exists: true
  Reading patch net.minecraft.world.inventory.AbstractContainerMenu$1.binpatch
    Checksum: 22a923b8 Exists: true
  Reading patch net.minecraft.world.inventory.AbstractContainerMenu.binpatch
    Checksum: 86b72821 Exists: true
  Reading patch net.minecraft.world.inventory.AbstractFurnaceMenu.binpatch
    Checksum: f277bff4 Exists: true
  Reading patch net.minecraft.world.inventory.AnvilMenu.binpatch
    Checksum: d81e9214 Exists: true
  Reading patch net.minecraft.world.inventory.ArmorSlot.binpatch
    Checksum: 743a4abc Exists: true
  Reading patch net.minecraft.world.inventory.BeaconMenu$1.binpatch
    Checksum: 85b9fe88 Exists: true
  Reading patch net.minecraft.world.inventory.BeaconMenu$PaymentSlot.binpatch
    Checksum: 5ea11f83 Exists: true
  Reading patch net.minecraft.world.inventory.BeaconMenu.binpatch
    Checksum: 766ecca9 Exists: true
  Reading patch net.minecraft.world.inventory.BrewingStandMenu$FuelSlot.binpatch
    Checksum: 8be20846 Exists: true
  Reading patch net.minecraft.world.inventory.BrewingStandMenu$IngredientsSlot.binpatch
    Checksum: 77ae20fc Exists: true
  Reading patch net.minecraft.world.inventory.BrewingStandMenu$PotionSlot.binpatch
    Checksum: 9f2cdac2 Exists: true
  Reading patch net.minecraft.world.inventory.BrewingStandMenu.binpatch
    Checksum: fec8789c Exists: true
  Reading patch net.minecraft.world.inventory.EnchantmentMenu$1.binpatch
    Checksum: 9c68c885 Exists: true
  Reading patch net.minecraft.world.inventory.EnchantmentMenu$2.binpatch
    Checksum: 71f00626 Exists: true
  Reading patch net.minecraft.world.inventory.EnchantmentMenu$3.binpatch
    Checksum: c45cf68f Exists: true
  Reading patch net.minecraft.world.inventory.EnchantmentMenu.binpatch
    Checksum: 5dc66084 Exists: true
  Reading patch net.minecraft.world.inventory.FurnaceResultSlot.binpatch
    Checksum: 452a55e4 Exists: true
  Reading patch net.minecraft.world.inventory.GrindstoneMenu$1.binpatch
    Checksum: c0d6c65e Exists: true
  Reading patch net.minecraft.world.inventory.GrindstoneMenu$2.binpatch
    Checksum: 64425728 Exists: true
  Reading patch net.minecraft.world.inventory.GrindstoneMenu$3.binpatch
    Checksum: 73d35736 Exists: true
  Reading patch net.minecraft.world.inventory.GrindstoneMenu$4.binpatch
    Checksum: 76bef62c Exists: true
  Reading patch net.minecraft.world.inventory.GrindstoneMenu.binpatch
    Checksum: afc7a381 Exists: true
  Reading patch net.minecraft.world.inventory.MenuType$MenuSupplier.binpatch
    Checksum: a6dba151 Exists: true
  Reading patch net.minecraft.world.inventory.MenuType.binpatch
    Checksum: a73a7052 Exists: true
  Reading patch net.minecraft.world.inventory.RecipeBookMenu.binpatch
    Checksum: fa24bb02 Exists: true
  Reading patch net.minecraft.world.inventory.RecipeBookType.binpatch
    Checksum: a6d01cba Exists: true
  Reading patch net.minecraft.world.inventory.ResultSlot.binpatch
    Checksum: 51a6406b Exists: true
  Reading patch net.minecraft.world.inventory.Slot.binpatch
    Checksum: f97fd2fb Exists: true
  Reading patch net.minecraft.world.item.AnimalArmorItem$BodyType.binpatch
    Checksum: 1dbbec11 Exists: true
  Reading patch net.minecraft.world.item.AnimalArmorItem.binpatch
    Checksum: dd611d54 Exists: true
  Reading patch net.minecraft.world.item.ArmorMaterial$Layer.binpatch
    Checksum: ff9db026 Exists: true
  Reading patch net.minecraft.world.item.ArmorMaterial.binpatch
    Checksum: a314ba6d Exists: true
  Reading patch net.minecraft.world.item.ArrowItem.binpatch
    Checksum: 4356b942 Exists: true
  Reading patch net.minecraft.world.item.AxeItem.binpatch
    Checksum: d4c02443 Exists: true
  Reading patch net.minecraft.world.item.BlockItem.binpatch
    Checksum: 33e665ff Exists: true
  Reading patch net.minecraft.world.item.BoneMealItem$1.binpatch
    Checksum: a8fd15f Exists: true
  Reading patch net.minecraft.world.item.BoneMealItem.binpatch
    Checksum: eb58e756 Exists: true
  Reading patch net.minecraft.world.item.BowItem.binpatch
    Checksum: 70ed7e93 Exists: true
  Reading patch net.minecraft.world.item.BucketItem.binpatch
    Checksum: e87a10cf Exists: true
  Reading patch net.minecraft.world.item.BundleItem.binpatch
    Checksum: 3d2d6a6c Exists: true
  Reading patch net.minecraft.world.item.ChorusFruitItem.binpatch
    Checksum: bb0bd021 Exists: true
  Reading patch net.minecraft.world.item.CreativeModeTab$Builder.binpatch
    Checksum: e5cfb812 Exists: true
  Reading patch net.minecraft.world.item.CreativeModeTab$DisplayItemsGenerator.binpatch
    Checksum: 4e26ca08 Exists: true
  Reading patch net.minecraft.world.item.CreativeModeTab$ItemDisplayBuilder.binpatch
    Checksum: 8a146f64 Exists: true
  Reading patch net.minecraft.world.item.CreativeModeTab$ItemDisplayParameters.binpatch
    Checksum: 67d7971f Exists: true
  Reading patch net.minecraft.world.item.CreativeModeTab$Output.binpatch
    Checksum: 652217b8 Exists: true
  Reading patch net.minecraft.world.item.CreativeModeTab$Row.binpatch
    Checksum: cc8e2404 Exists: true
  Reading patch net.minecraft.world.item.CreativeModeTab$TabVisibility.binpatch
    Checksum: bdbe5071 Exists: true
  Reading patch net.minecraft.world.item.CreativeModeTab$Type.binpatch
    Checksum: d9da3ae9 Exists: true
  Reading patch net.minecraft.world.item.CreativeModeTab.binpatch
    Checksum: 4e46278f Exists: true
  Reading patch net.minecraft.world.item.CrossbowItem$ChargingSounds.binpatch
    Checksum: a2395306 Exists: true
  Reading patch net.minecraft.world.item.CrossbowItem.binpatch
    Checksum: ce91293d Exists: true
  Reading patch net.minecraft.world.item.DispensibleContainerItem.binpatch
    Checksum: cfe1f8a Exists: true
  Reading patch net.minecraft.world.item.DyeColor.binpatch
    Checksum: 7eac5681 Exists: true
  Reading patch net.minecraft.world.item.ElytraItem.binpatch
    Checksum: 2b6b1b95 Exists: true
  Reading patch net.minecraft.world.item.FishingRodItem.binpatch
    Checksum: 19e9e65a Exists: true
  Reading patch net.minecraft.world.item.HoeItem.binpatch
    Checksum: 11c9256b Exists: true
  Reading patch net.minecraft.world.item.Item$Properties.binpatch
    Checksum: ecc861fd Exists: true
  Reading patch net.minecraft.world.item.Item$TooltipContext$1.binpatch
    Checksum: 1dd111e1 Exists: true
  Reading patch net.minecraft.world.item.Item$TooltipContext$2.binpatch
    Checksum: 9d318b32 Exists: true
  Reading patch net.minecraft.world.item.Item$TooltipContext$3.binpatch
    Checksum: 95f55efa Exists: true
  Reading patch net.minecraft.world.item.Item$TooltipContext.binpatch
    Checksum: c7c0c17b Exists: true
  Reading patch net.minecraft.world.item.Item.binpatch
    Checksum: c1a89f77 Exists: true
  Reading patch net.minecraft.world.item.ItemDisplayContext.binpatch
    Checksum: 171ffd6f Exists: true
  Reading patch net.minecraft.world.item.ItemStack$1.binpatch
    Checksum: d47232a1 Exists: true
  Reading patch net.minecraft.world.item.ItemStack$2.binpatch
    Checksum: a05cc8d9 Exists: true
  Reading patch net.minecraft.world.item.ItemStack$3.binpatch
    Checksum: b2e58fac Exists: true
  Reading patch net.minecraft.world.item.ItemStack$4.binpatch
    Checksum: bf92a269 Exists: true
  Reading patch net.minecraft.world.item.ItemStack.binpatch
    Checksum: d6195919 Exists: true
  Reading patch net.minecraft.world.item.Items$1.binpatch
    Checksum: 0 Exists: false
  Reading patch net.minecraft.world.item.Items.binpatch
    Checksum: e8d9744a Exists: true
  Reading patch net.minecraft.world.item.MinecartItem$1.binpatch
    Checksum: d0712c3 Exists: true
  Reading patch net.minecraft.world.item.MinecartItem.binpatch
    Checksum: d7cc81c9 Exists: true
  Reading patch net.minecraft.world.item.MobBucketItem.binpatch
    Checksum: b4faf18b Exists: true
  Reading patch net.minecraft.world.item.PickaxeItem.binpatch
    Checksum: a53eef8f Exists: true
  Reading patch net.minecraft.world.item.ProjectileWeaponItem.binpatch
    Checksum: ad2b0abf Exists: true
  Reading patch net.minecraft.world.item.ShearsItem.binpatch
    Checksum: 2d36595f Exists: true
  Reading patch net.minecraft.world.item.ShieldItem.binpatch
    Checksum: 30d3b8f0 Exists: true
  Reading patch net.minecraft.world.item.ShovelItem.binpatch
    Checksum: b2c259ba Exists: true
  Reading patch net.minecraft.world.item.SpawnEggItem.binpatch
    Checksum: 4fa22690 Exists: true
  Reading patch net.minecraft.world.item.StandingAndWallBlockItem.binpatch
    Checksum: 67791136 Exists: true
  Reading patch net.minecraft.world.item.SwordItem.binpatch
    Checksum: 18a0a425 Exists: true
  Reading patch net.minecraft.world.item.UseAnim.binpatch
    Checksum: f802d3c Exists: true
  Reading patch net.minecraft.world.item.alchemy.PotionBrewing$Builder.binpatch
    Checksum: 4e987c46 Exists: true
  Reading patch net.minecraft.world.item.alchemy.PotionBrewing$Mix.binpatch
    Checksum: fe656d87 Exists: true
  Reading patch net.minecraft.world.item.alchemy.PotionBrewing.binpatch
    Checksum: 6831a99b Exists: true
  Reading patch net.minecraft.world.item.crafting.BannerDuplicateRecipe.binpatch
    Checksum: 81e52d07 Exists: true
  Reading patch net.minecraft.world.item.crafting.BookCloningRecipe.binpatch
    Checksum: 28a22cde Exists: true
  Reading patch net.minecraft.world.item.crafting.Ingredient$ItemValue.binpatch
    Checksum: ac73a848 Exists: true
  Reading patch net.minecraft.world.item.crafting.Ingredient$TagValue.binpatch
    Checksum: da0445d9 Exists: true
  Reading patch net.minecraft.world.item.crafting.Ingredient$Value.binpatch
    Checksum: a5d8714c Exists: true
  Reading patch net.minecraft.world.item.crafting.Ingredient.binpatch
    Checksum: b3c5a8b Exists: true
  Reading patch net.minecraft.world.item.crafting.Recipe.binpatch
    Checksum: 8b507b1b Exists: true
  Reading patch net.minecraft.world.item.crafting.RecipeManager$1.binpatch
    Checksum: cfa1fa94 Exists: true
  Reading patch net.minecraft.world.item.crafting.RecipeManager$CachedCheck.binpatch
    Checksum: 7b1bcd80 Exists: true
  Reading patch net.minecraft.world.item.crafting.RecipeManager.binpatch
    Checksum: a96a8af3 Exists: true
  Reading patch net.minecraft.world.item.crafting.RecipeType$1.binpatch
    Checksum: 8a98c561 Exists: true
  Reading patch net.minecraft.world.item.crafting.RecipeType$2.binpatch
    Checksum: 0 Exists: false
  Reading patch net.minecraft.world.item.crafting.RecipeType.binpatch
    Checksum: c947bc09 Exists: true
  Reading patch net.minecraft.world.item.crafting.ShapedRecipe$Serializer.binpatch
    Checksum: 4a61e90a Exists: true
  Reading patch net.minecraft.world.item.crafting.ShapedRecipe.binpatch
    Checksum: 7b99ff66 Exists: true
  Reading patch net.minecraft.world.item.crafting.ShapedRecipePattern$Data.binpatch
    Checksum: ffb22b69 Exists: true
  Reading patch net.minecraft.world.item.crafting.ShapedRecipePattern.binpatch
    Checksum: 8d810619 Exists: true
  Reading patch net.minecraft.world.item.crafting.ShapelessRecipe$Serializer.binpatch
    Checksum: 5d8ee145 Exists: true
  Reading patch net.minecraft.world.item.crafting.ShapelessRecipe.binpatch
    Checksum: 75ae9e60 Exists: true
  Reading patch net.minecraft.world.item.crafting.ShulkerBoxColoring.binpatch
    Checksum: 7386be55 Exists: true
  Reading patch net.minecraft.world.item.crafting.SmithingTransformRecipe$Serializer.binpatch
    Checksum: 9786651b Exists: true
  Reading patch net.minecraft.world.item.crafting.SmithingTransformRecipe.binpatch
    Checksum: e0a0caaa Exists: true
  Reading patch net.minecraft.world.item.crafting.SmithingTrimRecipe$Serializer.binpatch
    Checksum: ef5b9c05 Exists: true
  Reading patch net.minecraft.world.item.crafting.SmithingTrimRecipe.binpatch
    Checksum: 1f195f83 Exists: true
  Reading patch net.minecraft.world.item.enchantment.EnchantmentHelper$EnchantmentInSlotVisitor.binpatch
    Checksum: 55fdd0e5 Exists: true
  Reading patch net.minecraft.world.item.enchantment.EnchantmentHelper$EnchantmentVisitor.binpatch
    Checksum: a0709fcd Exists: true
  Reading patch net.minecraft.world.item.enchantment.EnchantmentHelper.binpatch
    Checksum: 4c3adf12 Exists: true
  Reading patch net.minecraft.world.item.enchantment.effects.ReplaceDisk.binpatch
    Checksum: 23fd6329 Exists: true
  Reading patch net.minecraft.world.level.BaseSpawner.binpatch
    Checksum: 12404bae Exists: true
  Reading patch net.minecraft.world.level.BlockAndTintGetter.binpatch
    Checksum: 9e168cc7 Exists: true
  Reading patch net.minecraft.world.level.BlockGetter.binpatch
    Checksum: 4998c0dc Exists: true
  Reading patch net.minecraft.world.level.DataPackConfig.binpatch
    Checksum: 614926e4 Exists: true
  Reading patch net.minecraft.world.level.Explosion$BlockInteraction.binpatch
    Checksum: 18854fe1 Exists: true
  Reading patch net.minecraft.world.level.Explosion.binpatch
    Checksum: 7618724d Exists: true
  Reading patch net.minecraft.world.level.ExplosionDamageCalculator.binpatch
    Checksum: c9605ec7 Exists: true
  Reading patch net.minecraft.world.level.ForcedChunksSavedData.binpatch
    Checksum: d78549c0 Exists: true
  Reading patch net.minecraft.world.level.Level$1.binpatch
    Checksum: 3a252749 Exists: true
  Reading patch net.minecraft.world.level.Level$ExplosionInteraction.binpatch
    Checksum: 48bbb5f2 Exists: true
  Reading patch net.minecraft.world.level.Level.binpatch
    Checksum: 596c6fed Exists: true
  Reading patch net.minecraft.world.level.LevelReader.binpatch
    Checksum: 115d1076 Exists: true
  Reading patch net.minecraft.world.level.LevelSettings.binpatch
    Checksum: 6b663de5 Exists: true
  Reading patch net.minecraft.world.level.NaturalSpawner$AfterSpawnCallback.binpatch
    Checksum: fe1682d4 Exists: true
  Reading patch net.minecraft.world.level.NaturalSpawner$ChunkGetter.binpatch
    Checksum: 40dd8d05 Exists: true
  Reading patch net.minecraft.world.level.NaturalSpawner$SpawnPredicate.binpatch
    Checksum: 605fbeb9 Exists: true
  Reading patch net.minecraft.world.level.NaturalSpawner$SpawnState.binpatch
    Checksum: 58ab1660 Exists: true
  Reading patch net.minecraft.world.level.NaturalSpawner.binpatch
    Checksum: ca38ce27 Exists: true
  Reading patch net.minecraft.world.level.SignalGetter.binpatch
    Checksum: b582bb6d Exists: true
  Reading patch net.minecraft.world.level.biome.Biome$1.binpatch
    Checksum: c60d1d04 Exists: true
  Reading patch net.minecraft.world.level.biome.Biome$BiomeBuilder.binpatch
    Checksum: 846c5965 Exists: true
  Reading patch net.minecraft.world.level.biome.Biome$ClimateSettings.binpatch
    Checksum: 56c76311 Exists: true
  Reading patch net.minecraft.world.level.biome.Biome$Precipitation.binpatch
    Checksum: 1a7398a1 Exists: true
  Reading patch net.minecraft.world.level.biome.Biome$TemperatureModifier$1.binpatch
    Checksum: 3ba9c9ff Exists: true
  Reading patch net.minecraft.world.level.biome.Biome$TemperatureModifier$2.binpatch
    Checksum: b4bf5912 Exists: true
  Reading patch net.minecraft.world.level.biome.Biome$TemperatureModifier.binpatch
    Checksum: 5859f199 Exists: true
  Reading patch net.minecraft.world.level.biome.Biome.binpatch
    Checksum: 17987c80 Exists: true
  Reading patch net.minecraft.world.level.biome.BiomeGenerationSettings$Builder.binpatch
    Checksum: 101c5bf2 Exists: true
  Reading patch net.minecraft.world.level.biome.BiomeGenerationSettings$PlainBuilder.binpatch
    Checksum: 19becf05 Exists: true
  Reading patch net.minecraft.world.level.biome.BiomeGenerationSettings.binpatch
    Checksum: 81ed4d0b Exists: true
  Reading patch net.minecraft.world.level.biome.BiomeSpecialEffects$Builder.binpatch
    Checksum: 60de6288 Exists: true
  Reading patch net.minecraft.world.level.biome.BiomeSpecialEffects$GrassColorModifier$1.binpatch
    Checksum: e614d1cf Exists: true
  Reading patch net.minecraft.world.level.biome.BiomeSpecialEffects$GrassColorModifier$2.binpatch
    Checksum: f60ad744 Exists: true
  Reading patch net.minecraft.world.level.biome.BiomeSpecialEffects$GrassColorModifier$3.binpatch
    Checksum: 5f743382 Exists: true
  Reading patch net.minecraft.world.level.biome.BiomeSpecialEffects$GrassColorModifier$ColorModifier.binpatch
    Checksum: 0 Exists: false
  Reading patch net.minecraft.world.level.biome.BiomeSpecialEffects$GrassColorModifier.binpatch
    Checksum: 86ff5364 Exists: true
  Reading patch net.minecraft.world.level.biome.BiomeSpecialEffects.binpatch
    Checksum: a71a45c Exists: true
  Reading patch net.minecraft.world.level.biome.MobSpawnSettings$Builder.binpatch
    Checksum: 83ab63d9 Exists: true
  Reading patch net.minecraft.world.level.biome.MobSpawnSettings$MobSpawnCost.binpatch
    Checksum: e37faeb7 Exists: true
  Reading patch net.minecraft.world.level.biome.MobSpawnSettings$SpawnerData.binpatch
    Checksum: 9a49af9 Exists: true
  Reading patch net.minecraft.world.level.biome.MobSpawnSettings.binpatch
    Checksum: 7ad13045 Exists: true
  Reading patch net.minecraft.world.level.block.BambooSaplingBlock.binpatch
    Checksum: c19096c3 Exists: true
  Reading patch net.minecraft.world.level.block.BambooStalkBlock.binpatch
    Checksum: da090e31 Exists: true
  Reading patch net.minecraft.world.level.block.BaseFireBlock.binpatch
    Checksum: 33b1d199 Exists: true
  Reading patch net.minecraft.world.level.block.BaseRailBlock$1.binpatch
    Checksum: 29c8dd11 Exists: true
  Reading patch net.minecraft.world.level.block.BaseRailBlock.binpatch
    Checksum: 85f585e5 Exists: true
  Reading patch net.minecraft.world.level.block.BeehiveBlock.binpatch
    Checksum: 2dafa3a2 Exists: true
  Reading patch net.minecraft.world.level.block.Block$1.binpatch
    Checksum: ca6f9502 Exists: true
  Reading patch net.minecraft.world.level.block.Block$2.binpatch
    Checksum: a4c0d337 Exists: true
  Reading patch net.minecraft.world.level.block.Block$BlockStatePairKey.binpatch
    Checksum: 3ce92b96 Exists: true
  Reading patch net.minecraft.world.level.block.Block.binpatch
    Checksum: 856e4028 Exists: true
  Reading patch net.minecraft.world.level.block.Blocks.binpatch
    Checksum: 7776b939 Exists: true
  Reading patch net.minecraft.world.level.block.BucketPickup.binpatch
    Checksum: f1cfb0e2 Exists: true
  Reading patch net.minecraft.world.level.block.BushBlock.binpatch
    Checksum: e40fdd4 Exists: true
  Reading patch net.minecraft.world.level.block.CactusBlock.binpatch
    Checksum: f5f724e6 Exists: true
  Reading patch net.minecraft.world.level.block.CampfireBlock.binpatch
    Checksum: ddf4aff1 Exists: true
  Reading patch net.minecraft.world.level.block.ChestBlock$1.binpatch
    Checksum: a6508f6f Exists: true
  Reading patch net.minecraft.world.level.block.ChestBlock$2$1.binpatch
    Checksum: a82de21 Exists: true
  Reading patch net.minecraft.world.level.block.ChestBlock$2.binpatch
    Checksum: 1e370294 Exists: true
  Reading patch net.minecraft.world.level.block.ChestBlock$3.binpatch
    Checksum: 8a6678c1 Exists: true
  Reading patch net.minecraft.world.level.block.ChestBlock$4.binpatch
    Checksum: 37cbaadd Exists: true
  Reading patch net.minecraft.world.level.block.ChestBlock.binpatch
    Checksum: 6f6e8f75 Exists: true
  Reading patch net.minecraft.world.level.block.ChorusFlowerBlock.binpatch
    Checksum: 1a904472 Exists: true
  Reading patch net.minecraft.world.level.block.CocoaBlock$1.binpatch
    Checksum: c1beaa8d Exists: true
  Reading patch net.minecraft.world.level.block.CocoaBlock.binpatch
    Checksum: 124894a2 Exists: true
  Reading patch net.minecraft.world.level.block.ComparatorBlock.binpatch
    Checksum: dc506fd Exists: true
  Reading patch net.minecraft.world.level.block.ConcretePowderBlock.binpatch
    Checksum: 8bbf9717 Exists: true
  Reading patch net.minecraft.world.level.block.CoralBlock.binpatch
    Checksum: e11a65a5 Exists: true
  Reading patch net.minecraft.world.level.block.CropBlock.binpatch
    Checksum: 73f0c906 Exists: true
  Reading patch net.minecraft.world.level.block.DeadBushBlock.binpatch
    Checksum: da948591 Exists: true
  Reading patch net.minecraft.world.level.block.DetectorRailBlock$1.binpatch
    Checksum: 3655cf57 Exists: true
  Reading patch net.minecraft.world.level.block.DetectorRailBlock.binpatch
    Checksum: ae6f6dda Exists: true
  Reading patch net.minecraft.world.level.block.DiodeBlock.binpatch
    Checksum: d22c5e80 Exists: true
  Reading patch net.minecraft.world.level.block.DoublePlantBlock.binpatch
    Checksum: 518130d2 Exists: true
  Reading patch net.minecraft.world.level.block.DropExperienceBlock.binpatch
    Checksum: fdf24af0 Exists: true
  Reading patch net.minecraft.world.level.block.DropperBlock.binpatch
    Checksum: d41ca492 Exists: true
  Reading patch net.minecraft.world.level.block.EnchantingTableBlock.binpatch
    Checksum: f2862b68 Exists: true
  Reading patch net.minecraft.world.level.block.FarmBlock.binpatch
    Checksum: 1d49bd7 Exists: true
  Reading patch net.minecraft.world.level.block.FenceGateBlock$1.binpatch
    Checksum: cafbc963 Exists: true
  Reading patch net.minecraft.world.level.block.FenceGateBlock.binpatch
    Checksum: efee0eb3 Exists: true
  Reading patch net.minecraft.world.level.block.FireBlock.binpatch
    Checksum: 4454215b Exists: true
  Reading patch net.minecraft.world.level.block.FlowerPotBlock.binpatch
    Checksum: dd8262c1 Exists: true
  Reading patch net.minecraft.world.level.block.FungusBlock.binpatch
    Checksum: 9dfd4c10 Exists: true
  Reading patch net.minecraft.world.level.block.GrowingPlantHeadBlock.binpatch
    Checksum: a831fdcb Exists: true
  Reading patch net.minecraft.world.level.block.LeavesBlock.binpatch
    Checksum: 6c132fc7 Exists: true
  Reading patch net.minecraft.world.level.block.LiquidBlock.binpatch
    Checksum: 6288b1dc Exists: true
  Reading patch net.minecraft.world.level.block.MushroomBlock.binpatch
    Checksum: 4436c700 Exists: true
  Reading patch net.minecraft.world.level.block.NetherWartBlock.binpatch
    Checksum: 6e6198ca Exists: true
  Reading patch net.minecraft.world.level.block.NoteBlock.binpatch
    Checksum: 4bfcac32 Exists: true
  Reading patch net.minecraft.world.level.block.PowderSnowBlock.binpatch
    Checksum: 4b7f8a18 Exists: true
  Reading patch net.minecraft.world.level.block.PoweredRailBlock$1.binpatch
    Checksum: 10f9ce4b Exists: true
  Reading patch net.minecraft.world.level.block.PoweredRailBlock.binpatch
    Checksum: f582b9eb Exists: true
  Reading patch net.minecraft.world.level.block.PumpkinBlock.binpatch
    Checksum: 419eb43b Exists: true
  Reading patch net.minecraft.world.level.block.RailState$1.binpatch
    Checksum: 87d7120a Exists: true
  Reading patch net.minecraft.world.level.block.RailState.binpatch
    Checksum: 43fd39ec Exists: true
  Reading patch net.minecraft.world.level.block.RedStoneOreBlock.binpatch
    Checksum: e978364a Exists: true
  Reading patch net.minecraft.world.level.block.RedStoneWireBlock$1.binpatch
    Checksum: d0ba7902 Exists: true
  Reading patch net.minecraft.world.level.block.RedStoneWireBlock.binpatch
    Checksum: 3dfdaf7a Exists: true
  Reading patch net.minecraft.world.level.block.SaplingBlock.binpatch
    Checksum: 3b0ed7e3 Exists: true
  Reading patch net.minecraft.world.level.block.SculkCatalystBlock.binpatch
    Checksum: 6af7046 Exists: true
  Reading patch net.minecraft.world.level.block.SculkSensorBlock.binpatch
    Checksum: 7cc17c62 Exists: true
  Reading patch net.minecraft.world.level.block.SculkShriekerBlock.binpatch
    Checksum: 90c0c6b7 Exists: true
  Reading patch net.minecraft.world.level.block.SeagrassBlock.binpatch
    Checksum: 3f5bd51e Exists: true
  Reading patch net.minecraft.world.level.block.SoundType.binpatch
    Checksum: eaecd3f6 Exists: true
  Reading patch net.minecraft.world.level.block.SpawnerBlock.binpatch
    Checksum: 314d85de Exists: true
  Reading patch net.minecraft.world.level.block.SpongeBlock.binpatch
    Checksum: 1c7b372e Exists: true
  Reading patch net.minecraft.world.level.block.SpreadingSnowyDirtBlock.binpatch
    Checksum: 63ea6244 Exists: true
  Reading patch net.minecraft.world.level.block.StemBlock.binpatch
    Checksum: 9966c905 Exists: true
  Reading patch net.minecraft.world.level.block.SugarCaneBlock.binpatch
    Checksum: 7589b5f8 Exists: true
  Reading patch net.minecraft.world.level.block.SweetBerryBushBlock.binpatch
    Checksum: bba1287c Exists: true
  Reading patch net.minecraft.world.level.block.TallGrassBlock.binpatch
    Checksum: 60af7089 Exists: true
  Reading patch net.minecraft.world.level.block.TntBlock.binpatch
    Checksum: 4fe5914d Exists: true
  Reading patch net.minecraft.world.level.block.TrapDoorBlock$1.binpatch
    Checksum: 7c85137d Exists: true
  Reading patch net.minecraft.world.level.block.TrapDoorBlock.binpatch
    Checksum: c43e05f7 Exists: true
  Reading patch net.minecraft.world.level.block.TripWireBlock$1.binpatch
    Checksum: 38f808a9 Exists: true
  Reading patch net.minecraft.world.level.block.TripWireBlock.binpatch
    Checksum: 780a6a1f Exists: true
  Reading patch net.minecraft.world.level.block.TripWireHookBlock$1.binpatch
    Checksum: ca9bb324 Exists: true
  Reading patch net.minecraft.world.level.block.TripWireHookBlock.binpatch
    Checksum: e7b872c1 Exists: true
  Reading patch net.minecraft.world.level.block.TurtleEggBlock.binpatch
    Checksum: 44354467 Exists: true
  Reading patch net.minecraft.world.level.block.VineBlock$1.binpatch
    Checksum: c71a02db Exists: true
  Reading patch net.minecraft.world.level.block.VineBlock.binpatch
    Checksum: d25c03a6 Exists: true
  Reading patch net.minecraft.world.level.block.WebBlock.binpatch
    Checksum: 32c05d3a Exists: true
  Reading patch net.minecraft.world.level.block.entity.AbstractFurnaceBlockEntity$1.binpatch
    Checksum: cdf731d0 Exists: true
  Reading patch net.minecraft.world.level.block.entity.AbstractFurnaceBlockEntity$2.binpatch
    Checksum: 0 Exists: false
  Reading patch net.minecraft.world.level.block.entity.AbstractFurnaceBlockEntity.binpatch
    Checksum: aabb7c8d Exists: true
  Reading patch net.minecraft.world.level.block.entity.BaseContainerBlockEntity.binpatch
    Checksum: c624512c Exists: true
  Reading patch net.minecraft.world.level.block.entity.BeaconBlockEntity$1.binpatch
    Checksum: b4d70010 Exists: true
  Reading patch net.minecraft.world.level.block.entity.BeaconBlockEntity$BeaconBeamSection.binpatch
    Checksum: a94bacb1 Exists: true
  Reading patch net.minecraft.world.level.block.entity.BeaconBlockEntity.binpatch
    Checksum: ef1bbfdd Exists: true
  Reading patch net.minecraft.world.level.block.entity.BlockEntity$1.binpatch
    Checksum: 784d2d Exists: true
  Reading patch net.minecraft.world.level.block.entity.BlockEntity$ComponentHelper.binpatch
    Checksum: 8a3a34b5 Exists: true
  Reading patch net.minecraft.world.level.block.entity.BlockEntity$DataComponentInput.binpatch
    Checksum: d1afe58e Exists: true
  Reading patch net.minecraft.world.level.block.entity.BlockEntity.binpatch
    Checksum: 7a678a42 Exists: true
  Reading patch net.minecraft.world.level.block.entity.BrewingStandBlockEntity$1.binpatch
    Checksum: 8b20117f Exists: true
  Reading patch net.minecraft.world.level.block.entity.BrewingStandBlockEntity$2.binpatch
    Checksum: 0 Exists: false
  Reading patch net.minecraft.world.level.block.entity.BrewingStandBlockEntity.binpatch
    Checksum: 187cdfee Exists: true
  Reading patch net.minecraft.world.level.block.entity.ChestBlockEntity$1.binpatch
    Checksum: b21b98ee Exists: true
  Reading patch net.minecraft.world.level.block.entity.ChestBlockEntity.binpatch
    Checksum: e4887baf Exists: true
  Reading patch net.minecraft.world.level.block.entity.ChiseledBookShelfBlockEntity.binpatch
    Checksum: 88c20294 Exists: true
  Reading patch net.minecraft.world.level.block.entity.ConduitBlockEntity.binpatch
    Checksum: 9fe8ae51 Exists: true
  Reading patch net.minecraft.world.level.block.entity.HangingSignBlockEntity.binpatch
    Checksum: f5d35880 Exists: true
  Reading patch net.minecraft.world.level.block.entity.HopperBlockEntity.binpatch
    Checksum: be1890cb Exists: true
  Reading patch net.minecraft.world.level.block.entity.ShulkerBoxBlockEntity$AnimationStatus.binpatch
    Checksum: 352d92cb Exists: true
  Reading patch net.minecraft.world.level.block.entity.ShulkerBoxBlockEntity.binpatch
    Checksum: 9be930a2 Exists: true
  Reading patch net.minecraft.world.level.block.entity.SignBlockEntity.binpatch
    Checksum: 91d25610 Exists: true
  Reading patch net.minecraft.world.level.block.entity.SpawnerBlockEntity$1.binpatch
    Checksum: 725a1fef Exists: true
  Reading patch net.minecraft.world.level.block.entity.SpawnerBlockEntity.binpatch
    Checksum: 928defe0 Exists: true
  Reading patch net.minecraft.world.level.block.grower.TreeGrower.binpatch
    Checksum: 83ddb18d Exists: true
  Reading patch net.minecraft.world.level.block.piston.PistonBaseBlock$1.binpatch
    Checksum: ec2a2250 Exists: true
  Reading patch net.minecraft.world.level.block.piston.PistonBaseBlock.binpatch
    Checksum: a20a9973 Exists: true
  Reading patch net.minecraft.world.level.block.piston.PistonMovingBlockEntity$1.binpatch
    Checksum: 6ad6187e Exists: true
  Reading patch net.minecraft.world.level.block.piston.PistonMovingBlockEntity.binpatch
    Checksum: 884a2cd6 Exists: true
  Reading patch net.minecraft.world.level.block.piston.PistonStructureResolver.binpatch
    Checksum: efc8f0f5 Exists: true
  Reading patch net.minecraft.world.level.block.state.BlockBehaviour$1.binpatch
    Checksum: 68dcceea Exists: true
  Reading patch net.minecraft.world.level.block.state.BlockBehaviour$BlockStateBase$Cache.binpatch
    Checksum: 50c98c07 Exists: true
  Reading patch net.minecraft.world.level.block.state.BlockBehaviour$BlockStateBase.binpatch
    Checksum: 80245e88 Exists: true
  Reading patch net.minecraft.world.level.block.state.BlockBehaviour$OffsetFunction.binpatch
    Checksum: 63438d83 Exists: true
  Reading patch net.minecraft.world.level.block.state.BlockBehaviour$OffsetType.binpatch
    Checksum: c6b66125 Exists: true
  Reading patch net.minecraft.world.level.block.state.BlockBehaviour$Properties.binpatch
    Checksum: be12b6e7 Exists: true
  Reading patch net.minecraft.world.level.block.state.BlockBehaviour$StateArgumentPredicate.binpatch
    Checksum: ea97cfae Exists: true
  Reading patch net.minecraft.world.level.block.state.BlockBehaviour$StatePredicate.binpatch
    Checksum: 49581c9 Exists: true
  Reading patch net.minecraft.world.level.block.state.BlockBehaviour.binpatch
    Checksum: 76d0e759 Exists: true
  Reading patch net.minecraft.world.level.block.state.BlockState.binpatch
    Checksum: 46b25e80 Exists: true
  Reading patch net.minecraft.world.level.chunk.ChunkAccess$TicksToSave.binpatch
    Checksum: b721d2cf Exists: true
  Reading patch net.minecraft.world.level.chunk.ChunkAccess.binpatch
    Checksum: a87e637f Exists: true
  Reading patch net.minecraft.world.level.chunk.ChunkGenerator.binpatch
    Checksum: 8e12744 Exists: true
  Reading patch net.minecraft.world.level.chunk.ImposterProtoChunk.binpatch
    Checksum: 6a73714d Exists: true
  Reading patch net.minecraft.world.level.chunk.LevelChunk$1.binpatch
    Checksum: 2807c171 Exists: true
  Reading patch net.minecraft.world.level.chunk.LevelChunk$BoundTickingBlockEntity.binpatch
    Checksum: 756a76f Exists: true
  Reading patch net.minecraft.world.level.chunk.LevelChunk$EntityCreationType.binpatch
    Checksum: c71b5b2a Exists: true
  Reading patch net.minecraft.world.level.chunk.LevelChunk$PostLoadProcessor.binpatch
    Checksum: 9dc57574 Exists: true
  Reading patch net.minecraft.world.level.chunk.LevelChunk$RebindableTickingBlockEntityWrapper.binpatch
    Checksum: f086f63e Exists: true
  Reading patch net.minecraft.world.level.chunk.LevelChunk.binpatch
    Checksum: e5805da9 Exists: true
  Reading patch net.minecraft.world.level.chunk.status.ChunkStatusTasks.binpatch
    Checksum: b4ca71a8 Exists: true
  Reading patch net.minecraft.world.level.chunk.storage.ChunkSerializer$ChunkReadException.binpatch
    Checksum: 255da1b4 Exists: true
  Reading patch net.minecraft.world.level.chunk.storage.ChunkSerializer.binpatch
    Checksum: c8867fc2 Exists: true
  Reading patch net.minecraft.world.level.chunk.storage.EntityStorage.binpatch
    Checksum: ce99f069 Exists: true
  Reading patch net.minecraft.world.level.dimension.end.EndDragonFight$Data.binpatch
    Checksum: 96b291b8 Exists: true
  Reading patch net.minecraft.world.level.dimension.end.EndDragonFight.binpatch
    Checksum: 2cc97b7d Exists: true
  Reading patch net.minecraft.world.level.entity.PersistentEntitySectionManager$Callback.binpatch
    Checksum: 64479168 Exists: true
  Reading patch net.minecraft.world.level.entity.PersistentEntitySectionManager$ChunkLoadStatus.binpatch
    Checksum: 8c549672 Exists: true
  Reading patch net.minecraft.world.level.entity.PersistentEntitySectionManager.binpatch
    Checksum: f05a70cb Exists: true
  Reading patch net.minecraft.world.level.entity.TransientEntitySectionManager$Callback.binpatch
    Checksum: 7614ddb3 Exists: true
  Reading patch net.minecraft.world.level.entity.TransientEntitySectionManager.binpatch
    Checksum: 88179e51 Exists: true
  Reading patch net.minecraft.world.level.levelgen.Beardifier$1.binpatch
    Checksum: 3139e2f5 Exists: true
  Reading patch net.minecraft.world.level.levelgen.Beardifier$Rigid.binpatch
    Checksum: 76b354da Exists: true
  Reading patch net.minecraft.world.level.levelgen.Beardifier.binpatch
    Checksum: 29d8bf71 Exists: true
  Reading patch net.minecraft.world.level.levelgen.DebugLevelSource.binpatch
    Checksum: 6ec93242 Exists: true
  Reading patch net.minecraft.world.level.levelgen.PhantomSpawner.binpatch
    Checksum: edf1ef79 Exists: true
  Reading patch net.minecraft.world.level.levelgen.WorldDimensions$1Entry.binpatch
    Checksum: f20c52f1 Exists: true
  Reading patch net.minecraft.world.level.levelgen.WorldDimensions$Complete.binpatch
    Checksum: 4b12925c Exists: true
  Reading patch net.minecraft.world.level.levelgen.WorldDimensions.binpatch
    Checksum: b480ef69 Exists: true
  Reading patch net.minecraft.world.level.levelgen.feature.Feature.binpatch
    Checksum: 7762f0a5 Exists: true
  Reading patch net.minecraft.world.level.levelgen.feature.MonsterRoomFeature.binpatch
    Checksum: c775e167 Exists: true
  Reading patch net.minecraft.world.level.levelgen.feature.treedecorators.AlterGroundDecorator.binpatch
    Checksum: d1aaae36 Exists: true
  Reading patch net.minecraft.world.level.levelgen.feature.trunkplacers.TrunkPlacer.binpatch
    Checksum: a68bed8f Exists: true
  Reading patch net.minecraft.world.level.levelgen.structure.Structure$GenerationContext.binpatch
    Checksum: c0899819 Exists: true
  Reading patch net.minecraft.world.level.levelgen.structure.Structure$GenerationStub.binpatch
    Checksum: f2dd8228 Exists: true
  Reading patch net.minecraft.world.level.levelgen.structure.Structure$StructureSettings$Builder.binpatch
    Checksum: 531b6297 Exists: true
  Reading patch net.minecraft.world.level.levelgen.structure.Structure$StructureSettings.binpatch
    Checksum: c6729f8 Exists: true
  Reading patch net.minecraft.world.level.levelgen.structure.Structure.binpatch
    Checksum: cf82246c Exists: true
  Reading patch net.minecraft.world.level.levelgen.structure.StructurePiece$1.binpatch
    Checksum: 59edbb76 Exists: true
  Reading patch net.minecraft.world.level.levelgen.structure.StructurePiece$BlockSelector.binpatch
    Checksum: d2dcffbd Exists: true
  Reading patch net.minecraft.world.level.levelgen.structure.StructurePiece.binpatch
    Checksum: 6de2e13a Exists: true
  Reading patch net.minecraft.world.level.levelgen.structure.StructureStart.binpatch
    Checksum: ec5873c3 Exists: true
  Reading patch net.minecraft.world.level.levelgen.structure.templatesystem.StructureProcessor.binpatch
    Checksum: 50e2e566 Exists: true
  Reading patch net.minecraft.world.level.levelgen.structure.templatesystem.StructureTemplate$1.binpatch
    Checksum: dc12db4 Exists: true
  Reading patch net.minecraft.world.level.levelgen.structure.templatesystem.StructureTemplate$Palette.binpatch
    Checksum: a292b916 Exists: true
  Reading patch net.minecraft.world.level.levelgen.structure.templatesystem.StructureTemplate$SimplePalette.binpatch
    Checksum: 83aa5ef5 Exists: true
  Reading patch net.minecraft.world.level.levelgen.structure.templatesystem.StructureTemplate$StructureBlockInfo.binpatch
    Checksum: 39b3cfbd Exists: true
  Reading patch net.minecraft.world.level.levelgen.structure.templatesystem.StructureTemplate$StructureEntityInfo.binpatch
    Checksum: 574309dc Exists: true
  Reading patch net.minecraft.world.level.levelgen.structure.templatesystem.StructureTemplate.binpatch
    Checksum: b074e46d Exists: true
  Reading patch net.minecraft.world.level.lighting.BlockLightEngine.binpatch
    Checksum: fa84452e Exists: true
  Reading patch net.minecraft.world.level.lighting.LightEngine$QueueEntry.binpatch
    Checksum: 62d2fc1e Exists: true
  Reading patch net.minecraft.world.level.lighting.LightEngine.binpatch
    Checksum: 31310086 Exists: true
  Reading patch net.minecraft.world.level.material.FlowingFluid$1.binpatch
    Checksum: b462ebec Exists: true
  Reading patch net.minecraft.world.level.material.FlowingFluid.binpatch
    Checksum: 8ff915f Exists: true
  Reading patch net.minecraft.world.level.material.Fluid.binpatch
    Checksum: 480cda34 Exists: true
  Reading patch net.minecraft.world.level.material.FluidState.binpatch
    Checksum: ee354562 Exists: true
  Reading patch net.minecraft.world.level.material.LavaFluid$Flowing.binpatch
    Checksum: 31434f24 Exists: true
  Reading patch net.minecraft.world.level.material.LavaFluid$Source.binpatch
    Checksum: d8d3ad26 Exists: true
  Reading patch net.minecraft.world.level.material.LavaFluid.binpatch
    Checksum: fd8d2e9e Exists: true
  Reading patch net.minecraft.world.level.pathfinder.PathType.binpatch
    Checksum: 5ad14102 Exists: true
  Reading patch net.minecraft.world.level.pathfinder.WalkNodeEvaluator$1.binpatch
    Checksum: 68f5086e Exists: true
  Reading patch net.minecraft.world.level.pathfinder.WalkNodeEvaluator.binpatch
    Checksum: 267b71bf Exists: true
  Reading patch net.minecraft.world.level.portal.PortalShape.binpatch
    Checksum: 70cbd56b Exists: true
  Reading patch net.minecraft.world.level.storage.DimensionDataStorage.binpatch
    Checksum: 7178f37c Exists: true
  Reading patch net.minecraft.world.level.storage.LevelStorageSource$LevelCandidates.binpatch
    Checksum: 62f9c522 Exists: true
  Reading patch net.minecraft.world.level.storage.LevelStorageSource$LevelDirectory.binpatch
    Checksum: 9913462b Exists: true
  Reading patch net.minecraft.world.level.storage.LevelStorageSource$LevelStorageAccess$1.binpatch
    Checksum: dc1b1fba Exists: true
  Reading patch net.minecraft.world.level.storage.LevelStorageSource$LevelStorageAccess$2.binpatch
    Checksum: edfa28a2 Exists: true
  Reading patch net.minecraft.world.level.storage.LevelStorageSource$LevelStorageAccess.binpatch
    Checksum: 2fe645af Exists: true
  Reading patch net.minecraft.world.level.storage.LevelStorageSource.binpatch
    Checksum: dfac8b7d Exists: true
  Reading patch net.minecraft.world.level.storage.LevelSummary$BackupStatus.binpatch
    Checksum: d1b8bb3c Exists: true
  Reading patch net.minecraft.world.level.storage.LevelSummary$CorruptedLevelSummary.binpatch
    Checksum: cd7ae12a Exists: true
  Reading patch net.minecraft.world.level.storage.LevelSummary$SymlinkLevelSummary.binpatch
    Checksum: f5ad7f7 Exists: true
  Reading patch net.minecraft.world.level.storage.LevelSummary.binpatch
    Checksum: 7aa566e4 Exists: true
  Reading patch net.minecraft.world.level.storage.PlayerDataStorage.binpatch
    Checksum: 7d6cee0f Exists: true
  Reading patch net.minecraft.world.level.storage.PrimaryLevelData$SpecialWorldProperty.binpatch
    Checksum: b05f8f72 Exists: true
  Reading patch net.minecraft.world.level.storage.PrimaryLevelData.binpatch
    Checksum: ea7f4cc1 Exists: true
  Reading patch net.minecraft.world.level.storage.loot.LootContext$Builder.binpatch
    Checksum: efad0212 Exists: true
  Reading patch net.minecraft.world.level.storage.loot.LootContext$EntityTarget.binpatch
    Checksum: 32f9c10d Exists: true
  Reading patch net.minecraft.world.level.storage.loot.LootContext$VisitedEntry.binpatch
    Checksum: f3637692 Exists: true
  Reading patch net.minecraft.world.level.storage.loot.LootContext.binpatch
    Checksum: e7c7dfa4 Exists: true
  Reading patch net.minecraft.world.level.storage.loot.LootDataType$Validator.binpatch
    Checksum: 17decc39 Exists: true
  Reading patch net.minecraft.world.level.storage.loot.LootDataType.binpatch
    Checksum: b2d5d383 Exists: true
  Reading patch net.minecraft.world.level.storage.loot.LootParams$Builder.binpatch
    Checksum: 7b806783 Exists: true
  Reading patch net.minecraft.world.level.storage.loot.LootParams$DynamicDrop.binpatch
    Checksum: 1d938e86 Exists: true
  Reading patch net.minecraft.world.level.storage.loot.LootParams.binpatch
    Checksum: 91273c33 Exists: true
  Reading patch net.minecraft.world.level.storage.loot.LootPool$Builder.binpatch
    Checksum: 7a8c71a Exists: true
  Reading patch net.minecraft.world.level.storage.loot.LootPool.binpatch
    Checksum: ebb66d33 Exists: true
  Reading patch net.minecraft.world.level.storage.loot.LootTable$Builder.binpatch
    Checksum: ff9dbd3f Exists: true
  Reading patch net.minecraft.world.level.storage.loot.LootTable.binpatch
    Checksum: 8efe7388 Exists: true
  Reading patch net.minecraft.world.level.storage.loot.functions.EnchantedCountIncreaseFunction$Builder.binpatch
    Checksum: f5d4c2e4 Exists: true
  Reading patch net.minecraft.world.level.storage.loot.functions.EnchantedCountIncreaseFunction.binpatch
    Checksum: 12f4827b Exists: true
  Reading patch net.minecraft.world.level.storage.loot.functions.SetAttributesFunction$Builder.binpatch
    Checksum: 30a3e7dc Exists: true
  Reading patch net.minecraft.world.level.storage.loot.functions.SetAttributesFunction$Modifier.binpatch
    Checksum: 8143cc5c Exists: true
  Reading patch net.minecraft.world.level.storage.loot.functions.SetAttributesFunction$ModifierBuilder.binpatch
    Checksum: 19aa5fb Exists: true
  Reading patch net.minecraft.world.level.storage.loot.functions.SetAttributesFunction.binpatch
    Checksum: b8392d4d Exists: true
  Reading patch net.minecraft.world.level.storage.loot.functions.SmeltItemFunction.binpatch
    Checksum: cc202c9f Exists: true
  Reading patch net.minecraft.world.level.storage.loot.parameters.LootContextParamSets.binpatch
    Checksum: a3751b35 Exists: true
  Reading patch net.minecraft.world.level.storage.loot.predicates.LootItemRandomChanceWithEnchantedBonusCondition.binpatch
    Checksum: cc685578 Exists: true
  Reading patch net.minecraft.world.level.storage.loot.providers.nbt.ContextNbtProvider$1.binpatch
    Checksum: b2f47a21 Exists: true
  Reading patch net.minecraft.world.level.storage.loot.providers.nbt.ContextNbtProvider$2.binpatch
    Checksum: 4fbdbe19 Exists: true
  Reading patch net.minecraft.world.level.storage.loot.providers.nbt.ContextNbtProvider$Getter.binpatch
    Checksum: ee70c908 Exists: true
  Reading patch net.minecraft.world.level.storage.loot.providers.nbt.ContextNbtProvider.binpatch
    Checksum: 2fe3a7eb Exists: true
Processing: C:\Users\<USER>\.gradle\caches\forge_gradle\mcp_repo\net\minecraft\joined\1.21.1-20240808.132146\joined-1.21.1-20240808.132146-srg.jar
  Patching com/mojang/blaze3d/pipeline/RenderTarget 1/1
  Patching com/mojang/blaze3d/platform/GlStateManager$BlendState 1/1
  Patching com/mojang/blaze3d/platform/GlStateManager$BooleanState 1/1
  Patching com/mojang/blaze3d/platform/GlStateManager$ColorLogicState 1/1
  Patching com/mojang/blaze3d/platform/GlStateManager$ColorMask 1/1
  Patching com/mojang/blaze3d/platform/GlStateManager$CullState 1/1
  Patching com/mojang/blaze3d/platform/GlStateManager$DepthState 1/1
  Patching com/mojang/blaze3d/platform/GlStateManager$DestFactor 1/1
  Patching com/mojang/blaze3d/platform/GlStateManager$LogicOp 1/1
  Patching com/mojang/blaze3d/platform/GlStateManager$PolygonOffsetState 1/1
  Patching com/mojang/blaze3d/platform/GlStateManager$ScissorState 1/1
  Patching com/mojang/blaze3d/platform/GlStateManager$SourceFactor 1/1
  Patching com/mojang/blaze3d/platform/GlStateManager$StencilFunc 1/1
  Patching com/mojang/blaze3d/platform/GlStateManager$StencilState 1/1
  Patching com/mojang/blaze3d/platform/GlStateManager$TextureState 1/1
  Patching com/mojang/blaze3d/platform/GlStateManager$Viewport 1/1
  Patching com/mojang/blaze3d/platform/GlStateManager 1/1
  Patching com/mojang/blaze3d/platform/Window$WindowInitFailed 1/1
  Patching com/mojang/blaze3d/platform/Window 1/1
  Patching com/mojang/blaze3d/vertex/PoseStack$Pose 1/1
  Patching com/mojang/blaze3d/vertex/PoseStack 1/1
  Patching com/mojang/blaze3d/vertex/SheetedDecalTextureGenerator 1/1
  Patching com/mojang/blaze3d/vertex/VertexConsumer 1/1
  Patching com/mojang/blaze3d/vertex/VertexFormat$Builder 1/1
  Patching com/mojang/blaze3d/vertex/VertexFormat$IndexType 1/1
  Patching com/mojang/blaze3d/vertex/VertexFormat$Mode 1/1
  Patching com/mojang/blaze3d/vertex/VertexFormat 1/1
  Patching com/mojang/math/Transformation 1/1
  Patching com/mojang/realmsclient/gui/screens/RealmsGenericErrorScreen$ErrorMessage 1/1
  Patching com/mojang/realmsclient/gui/screens/RealmsGenericErrorScreen 1/1
  Patching net/minecraft/CrashReport 1/1
  Patching net/minecraft/CrashReportCategory$Entry 1/1
  Patching net/minecraft/CrashReportCategory 1/1
  Patching net/minecraft/SharedConstants 1/1
  Patching net/minecraft/Util$1 1/1
  Patching net/minecraft/Util$10 1/1
  Patching net/minecraft/Util$11 1/1
  Patching net/minecraft/Util$2 1/1
  Patching net/minecraft/Util$5 1/1
  Patching net/minecraft/Util$6 1/1
  Patching net/minecraft/Util$7 1/1
  Patching net/minecraft/Util$8 1/1
  Patching net/minecraft/Util$9 1/1
  Patching net/minecraft/Util$OS$1 1/1
  Patching net/minecraft/Util$OS$2 1/1
  Patching net/minecraft/Util$OS 1/1
  Patching net/minecraft/Util 1/1
  Patching net/minecraft/advancements/Advancement$Builder 1/1
  Patching net/minecraft/advancements/Advancement 1/1
  Patching net/minecraft/advancements/AdvancementRewards$Builder 1/1
  Patching net/minecraft/advancements/AdvancementRewards 1/1
  Patching net/minecraft/client/Camera$NearPlane 1/1
  Patching net/minecraft/client/Camera 1/1
  Patching net/minecraft/client/ClientBrandRetriever 1/1
  Patching net/minecraft/client/ClientRecipeBook$1 1/1
  Patching net/minecraft/client/ClientRecipeBook 1/1
  Patching net/minecraft/client/DeltaTracker$DefaultValue 1/1
  Patching net/minecraft/client/DeltaTracker$Timer 1/1
  Patching net/minecraft/client/DeltaTracker 1/1
  Patching net/minecraft/client/KeyMapping 1/1
  Patching net/minecraft/client/KeyboardHandler$1 1/1
  Patching net/minecraft/client/KeyboardHandler 1/1
  Patching net/minecraft/client/Minecraft$1 1/1
  Patching net/minecraft/client/Minecraft$ChatStatus$1 1/1
  Patching net/minecraft/client/Minecraft$ChatStatus$2 1/1
  Patching net/minecraft/client/Minecraft$ChatStatus$3 1/1
  Patching net/minecraft/client/Minecraft$ChatStatus$4 1/1
  Patching net/minecraft/client/Minecraft$ChatStatus 1/1
  Patching net/minecraft/client/Minecraft$GameLoadCookie 1/1
  Patching net/minecraft/client/Minecraft 1/1
  Patching net/minecraft/client/MouseHandler 1/1
  Patching net/minecraft/client/Options$1 1/1
  Patching net/minecraft/client/Options$2 1/1
  Patching net/minecraft/client/Options$3 1/1
  Patching net/minecraft/client/Options$4 1/1
  Patching net/minecraft/client/Options$5 1/1
  Patching net/minecraft/client/Options$FieldAccess 1/1
  Patching net/minecraft/client/Options$OptionAccess 1/1
  Patching net/minecraft/client/Options 1/1
  Patching net/minecraft/client/RecipeBookCategories$1 1/1
  Patching net/minecraft/client/RecipeBookCategories 1/1
  Patching net/minecraft/client/Screenshot 1/1
  Patching net/minecraft/client/ToggleKeyMapping 1/1
  Patching net/minecraft/client/color/block/BlockColors 1/1
  Patching net/minecraft/client/color/item/ItemColors 1/1
  Patching net/minecraft/client/gui/Font$DisplayMode 1/1
  Patching net/minecraft/client/gui/Font$StringRenderOutput 1/1
  Patching net/minecraft/client/gui/Font 1/1
  Patching net/minecraft/client/gui/Gui$1DisplayEntry 1/1
  Patching net/minecraft/client/gui/Gui$HeartType 1/1
  Patching net/minecraft/client/gui/Gui 1/1
  Patching net/minecraft/client/gui/GuiGraphics$ScissorStack 1/1
  Patching net/minecraft/client/gui/GuiGraphics 1/1
  Patching net/minecraft/client/gui/components/AbstractButton 1/1
  Patching net/minecraft/client/gui/components/AbstractWidget 1/1
  Patching net/minecraft/client/gui/components/BossHealthOverlay$1 1/1
  Patching net/minecraft/client/gui/components/BossHealthOverlay 1/1
  Patching net/minecraft/client/gui/components/Button$Builder 1/1
  Patching net/minecraft/client/gui/components/Button$CreateNarration 1/1
  Patching net/minecraft/client/gui/components/Button$OnPress 1/1
  Patching net/minecraft/client/gui/components/Button 1/1
  Patching net/minecraft/client/gui/components/DebugScreenOverlay$1 1/1
  Patching net/minecraft/client/gui/components/DebugScreenOverlay$AllocationRateCalculator 1/1
  Patching net/minecraft/client/gui/components/DebugScreenOverlay 1/1
  Patching net/minecraft/client/gui/components/toasts/ToastComponent$ToastInstance 1/1
  Patching net/minecraft/client/gui/components/toasts/ToastComponent 1/1
  Patching net/minecraft/client/gui/screens/ChatScreen$1 1/1
  Patching net/minecraft/client/gui/screens/ChatScreen 1/1
  Patching net/minecraft/client/gui/screens/ConnectScreen$1 1/1
  Patching net/minecraft/client/gui/screens/ConnectScreen$2 1/1
  Patching net/minecraft/client/gui/screens/ConnectScreen 1/1
  Patching net/minecraft/client/gui/screens/LoadingOverlay$LogoTexture 1/1
  Patching net/minecraft/client/gui/screens/LoadingOverlay 1/1
  Patching net/minecraft/client/gui/screens/MenuScreens$ScreenConstructor 1/1
  Patching net/minecraft/client/gui/screens/MenuScreens 1/1
  Patching net/minecraft/client/gui/screens/PauseScreen$FeedbackSubScreen 1/1
  Patching net/minecraft/client/gui/screens/PauseScreen 1/1
  Patching net/minecraft/client/gui/screens/Screen$DeferredTooltipRendering 1/1
  Patching net/minecraft/client/gui/screens/Screen$NarratableSearchResult 1/1
  Patching net/minecraft/client/gui/screens/Screen 1/1
  Patching net/minecraft/client/gui/screens/TitleScreen 1/1
  Patching net/minecraft/client/gui/screens/advancements/AdvancementTab 1/1
  Patching net/minecraft/client/gui/screens/advancements/AdvancementTabType$Sprites 1/1
  Patching net/minecraft/client/gui/screens/advancements/AdvancementTabType 1/1
  Patching net/minecraft/client/gui/screens/advancements/AdvancementsScreen 1/1
  Patching net/minecraft/client/gui/screens/inventory/AbstractContainerScreen 1/1
  Patching net/minecraft/client/gui/screens/inventory/CreativeModeInventoryScreen$CustomCreativeSlot 1/1
  Patching net/minecraft/client/gui/screens/inventory/CreativeModeInventoryScreen$ItemPickerMenu 1/1
  Patching net/minecraft/client/gui/screens/inventory/CreativeModeInventoryScreen$SlotWrapper 1/1
  Patching net/minecraft/client/gui/screens/inventory/CreativeModeInventoryScreen 1/1
  Patching net/minecraft/client/gui/screens/inventory/EffectRenderingInventoryScreen 1/1
  Patching net/minecraft/client/gui/screens/inventory/EnchantmentScreen 1/1
  Patching net/minecraft/client/gui/screens/inventory/HangingSignEditScreen 1/1
  Patching net/minecraft/client/gui/screens/inventory/MerchantScreen$TradeOfferButton 1/1
  Patching net/minecraft/client/gui/screens/inventory/MerchantScreen 1/1
  Patching net/minecraft/client/gui/screens/inventory/tooltip/ClientTooltipComponent 1/1
  Patching net/minecraft/client/gui/screens/inventory/tooltip/TooltipRenderUtil 1/1
  Patching net/minecraft/client/gui/screens/multiplayer/ServerSelectionList$1 1/1
  Patching net/minecraft/client/gui/screens/multiplayer/ServerSelectionList$Entry 1/1
  Patching net/minecraft/client/gui/screens/multiplayer/ServerSelectionList$LANHeader 1/1
  Patching net/minecraft/client/gui/screens/multiplayer/ServerSelectionList$NetworkServerEntry 1/1
  Patching net/minecraft/client/gui/screens/multiplayer/ServerSelectionList$OnlineServerEntry 1/1
  Patching net/minecraft/client/gui/screens/multiplayer/ServerSelectionList 1/1
  Patching net/minecraft/client/gui/screens/options/controls/KeyBindsList$CategoryEntry$1 1/1
  Patching net/minecraft/client/gui/screens/options/controls/KeyBindsList$CategoryEntry 1/1
  Patching net/minecraft/client/gui/screens/options/controls/KeyBindsList$Entry 1/1
  Patching net/minecraft/client/gui/screens/options/controls/KeyBindsList$KeyEntry 1/1
  Patching net/minecraft/client/gui/screens/options/controls/KeyBindsList 1/1
  Patching net/minecraft/client/gui/screens/options/controls/KeyBindsScreen 1/1
  Patching net/minecraft/client/gui/screens/packs/PackSelectionModel$Entry 1/1
  Patching net/minecraft/client/gui/screens/packs/PackSelectionModel$EntryBase 1/1
  Patching net/minecraft/client/gui/screens/packs/PackSelectionModel$SelectedPackEntry 1/1
  Patching net/minecraft/client/gui/screens/packs/PackSelectionModel$UnselectedPackEntry 1/1
  Patching net/minecraft/client/gui/screens/packs/PackSelectionModel 1/1
  Patching net/minecraft/client/gui/screens/packs/PackSelectionScreen$1 1/1
  Patching net/minecraft/client/gui/screens/packs/PackSelectionScreen$Watcher 1/1
  Patching net/minecraft/client/gui/screens/packs/PackSelectionScreen 1/1
  Patching net/minecraft/client/gui/screens/recipebook/RecipeBookComponent 1/1
  Patching net/minecraft/client/gui/screens/worldselection/CreateWorldScreen$DataPackReloadCookie 1/1
  Patching net/minecraft/client/gui/screens/worldselection/CreateWorldScreen$GameTab 1/1
  Patching net/minecraft/client/gui/screens/worldselection/CreateWorldScreen$MoreTab 1/1
  Patching net/minecraft/client/gui/screens/worldselection/CreateWorldScreen$WorldTab$1 1/1
  Patching net/minecraft/client/gui/screens/worldselection/CreateWorldScreen$WorldTab$2 1/1
  Patching net/minecraft/client/gui/screens/worldselection/CreateWorldScreen$WorldTab 1/1
  Patching net/minecraft/client/gui/screens/worldselection/CreateWorldScreen 1/1
  Patching net/minecraft/client/gui/screens/worldselection/PresetEditor 1/1
  Patching net/minecraft/client/gui/screens/worldselection/WorldCreationContext$DimensionsUpdater 1/1
  Patching net/minecraft/client/gui/screens/worldselection/WorldCreationContext$OptionsModifier 1/1
  Patching net/minecraft/client/gui/screens/worldselection/WorldCreationContext 1/1
  Patching net/minecraft/client/gui/screens/worldselection/WorldCreationUiState$SelectedGameMode 1/1
  Patching net/minecraft/client/gui/screens/worldselection/WorldCreationUiState$WorldTypeEntry 1/1
  Patching net/minecraft/client/gui/screens/worldselection/WorldCreationUiState 1/1
  Patching net/minecraft/client/gui/screens/worldselection/WorldOpenFlows$1Data 1/1
  Patching net/minecraft/client/gui/screens/worldselection/WorldOpenFlows 1/1
  Patching net/minecraft/client/gui/screens/worldselection/WorldSelectionList$Entry 1/1
  Patching net/minecraft/client/gui/screens/worldselection/WorldSelectionList$LoadingHeader 1/1
  Patching net/minecraft/client/gui/screens/worldselection/WorldSelectionList$WorldListEntry 1/1
  Patching net/minecraft/client/gui/screens/worldselection/WorldSelectionList 1/1
  Patching net/minecraft/client/main/Main$1 1/1
  Patching net/minecraft/client/main/Main$2 1/1
  Patching net/minecraft/client/main/Main 1/1
  Patching net/minecraft/client/model/HumanoidModel$ArmPose 1/1
  Patching net/minecraft/client/model/HumanoidModel 1/1
  Patching net/minecraft/client/model/geom/LayerDefinitions 1/1
  Patching net/minecraft/client/model/geom/ModelLayers 1/1
  Patching net/minecraft/client/multiplayer/AccountProfileKeyPairManager 1/1
  Patching net/minecraft/client/multiplayer/ClientChunkCache$Storage 1/1
  Patching net/minecraft/client/multiplayer/ClientChunkCache 1/1
  Patching net/minecraft/client/multiplayer/ClientCommonPacketListenerImpl$DeferredPacket 1/1
  Patching net/minecraft/client/multiplayer/ClientCommonPacketListenerImpl$PackConfirmScreen$PendingRequest 1/1
  Patching net/minecraft/client/multiplayer/ClientCommonPacketListenerImpl$PackConfirmScreen 1/1
  Patching net/minecraft/client/multiplayer/ClientCommonPacketListenerImpl 1/1
  Patching net/minecraft/client/multiplayer/ClientConfigurationPacketListenerImpl 1/1
  Patching net/minecraft/client/multiplayer/ClientHandshakePacketListenerImpl$State 1/1
  Patching net/minecraft/client/multiplayer/ClientHandshakePacketListenerImpl 1/1
  Patching net/minecraft/client/multiplayer/ClientLevel$1 1/1
  Patching net/minecraft/client/multiplayer/ClientLevel$ClientLevelData 1/1
  Patching net/minecraft/client/multiplayer/ClientLevel$EntityCallbacks 1/1
  Patching net/minecraft/client/multiplayer/ClientLevel 1/1
  Patching net/minecraft/client/multiplayer/ClientPacketListener$1 1/1
  Patching net/minecraft/client/multiplayer/ClientPacketListener 1/1
  Patching net/minecraft/client/multiplayer/MultiPlayerGameMode 1/1
  Patching net/minecraft/client/multiplayer/PlayerInfo 1/1
  Patching net/minecraft/client/multiplayer/ServerData$ServerPackStatus 1/1
  Patching net/minecraft/client/multiplayer/ServerData$State 1/1
  Patching net/minecraft/client/multiplayer/ServerData$Type 1/1
  Patching net/minecraft/client/multiplayer/ServerData 1/1
  Patching net/minecraft/client/multiplayer/ServerStatusPinger$1 1/1
  Patching net/minecraft/client/multiplayer/ServerStatusPinger$2 1/1
  Patching net/minecraft/client/multiplayer/ServerStatusPinger 1/1
  Patching net/minecraft/client/multiplayer/SessionSearchTrees$Key 1/1
  Patching net/minecraft/client/multiplayer/SessionSearchTrees 1/1
  Patching net/minecraft/client/multiplayer/chat/ChatListener$Message 1/1
  Patching net/minecraft/client/multiplayer/chat/ChatListener 1/1
  Patching net/minecraft/client/multiplayer/resolver/AddressCheck$1 1/1
  Patching net/minecraft/client/multiplayer/resolver/AddressCheck 1/1
  Patching net/minecraft/client/particle/BreakingItemParticle$CobwebProvider 1/1
  Patching net/minecraft/client/particle/BreakingItemParticle$Provider 1/1
  Patching net/minecraft/client/particle/BreakingItemParticle$SlimeProvider 1/1
  Patching net/minecraft/client/particle/BreakingItemParticle$SnowballProvider 1/1
  Patching net/minecraft/client/particle/BreakingItemParticle 1/1
  Patching net/minecraft/client/particle/FireworkParticles$1 1/1
  Patching net/minecraft/client/particle/FireworkParticles$FlashProvider 1/1
  Patching net/minecraft/client/particle/FireworkParticles$OverlayParticle 1/1
  Patching net/minecraft/client/particle/FireworkParticles$SparkParticle 1/1
  Patching net/minecraft/client/particle/FireworkParticles$SparkProvider 1/1
  Patching net/minecraft/client/particle/FireworkParticles$Starter 1/1
  Patching net/minecraft/client/particle/FireworkParticles 1/1
  Patching net/minecraft/client/particle/FlyTowardsPositionParticle$EnchantProvider 1/1
  Patching net/minecraft/client/particle/FlyTowardsPositionParticle$NautilusProvider 1/1
  Patching net/minecraft/client/particle/FlyTowardsPositionParticle$VaultConnectionProvider 1/1
  Patching net/minecraft/client/particle/FlyTowardsPositionParticle 1/1
  Patching net/minecraft/client/particle/Particle$LifetimeAlpha 1/1
  Patching net/minecraft/client/particle/Particle 1/1
  Patching net/minecraft/client/particle/ParticleEngine$1ParticleDefinition 1/1
  Patching net/minecraft/client/particle/ParticleEngine$MutableSpriteSet 1/1
  Patching net/minecraft/client/particle/ParticleEngine$SpriteParticleRegistration 1/1
  Patching net/minecraft/client/particle/ParticleEngine 1/1
  Patching net/minecraft/client/particle/PortalParticle$Provider 1/1
  Patching net/minecraft/client/particle/PortalParticle 1/1
  Patching net/minecraft/client/particle/ReversePortalParticle$ReversePortalProvider 1/1
  Patching net/minecraft/client/particle/ReversePortalParticle 1/1
  Patching net/minecraft/client/particle/TerrainParticle$DustPillarProvider 1/1
  Patching net/minecraft/client/particle/TerrainParticle$Provider 1/1
  Patching net/minecraft/client/particle/TerrainParticle 1/1
  Patching net/minecraft/client/particle/VibrationSignalParticle$Provider 1/1
  Patching net/minecraft/client/particle/VibrationSignalParticle 1/1
  Patching net/minecraft/client/player/AbstractClientPlayer 1/1
  Patching net/minecraft/client/player/LocalPlayer 1/1
  Patching net/minecraft/client/player/RemotePlayer 1/1
  Patching net/minecraft/client/renderer/DimensionSpecialEffects$EndEffects 1/1
  Patching net/minecraft/client/renderer/DimensionSpecialEffects$NetherEffects 1/1
  Patching net/minecraft/client/renderer/DimensionSpecialEffects$OverworldEffects 1/1
  Patching net/minecraft/client/renderer/DimensionSpecialEffects$SkyType 1/1
  Patching net/minecraft/client/renderer/DimensionSpecialEffects 1/1
  Patching net/minecraft/client/renderer/EffectInstance 1/1
  Patching net/minecraft/client/renderer/FogRenderer$BlindnessFogFunction 1/1
  Patching net/minecraft/client/renderer/FogRenderer$DarknessFogFunction 1/1
  Patching net/minecraft/client/renderer/FogRenderer$FogData 1/1
  Patching net/minecraft/client/renderer/FogRenderer$FogMode 1/1
  Patching net/minecraft/client/renderer/FogRenderer$MobEffectFogFunction 1/1
  Patching net/minecraft/client/renderer/FogRenderer 1/1
  Patching net/minecraft/client/renderer/GameRenderer$1 1/1
  Patching net/minecraft/client/renderer/GameRenderer$ResourceCache 1/1
  Patching net/minecraft/client/renderer/GameRenderer 1/1
  Patching net/minecraft/client/renderer/ItemBlockRenderTypes 1/1
  Patching net/minecraft/client/renderer/ItemInHandRenderer$1 1/1
  Patching net/minecraft/client/renderer/ItemInHandRenderer$HandRenderSelection 1/1
  Patching net/minecraft/client/renderer/ItemInHandRenderer 1/1
  Patching net/minecraft/client/renderer/LevelRenderer$1 1/1
  Patching net/minecraft/client/renderer/LevelRenderer$TransparencyShaderException 1/1
  Patching net/minecraft/client/renderer/LevelRenderer 1/1
  Patching net/minecraft/client/renderer/LightTexture 1/1
  Patching net/minecraft/client/renderer/PostChain 1/1
  Patching net/minecraft/client/renderer/RenderType$CompositeRenderType 1/1
  Patching net/minecraft/client/renderer/RenderType$CompositeState$CompositeStateBuilder 1/1
  Patching net/minecraft/client/renderer/RenderType$CompositeState 1/1
  Patching net/minecraft/client/renderer/RenderType$OutlineProperty 1/1
  Patching net/minecraft/client/renderer/RenderType 1/1
  Patching net/minecraft/client/renderer/ScreenEffectRenderer 1/1
  Patching net/minecraft/client/renderer/ShaderInstance$1 1/1
  Patching net/minecraft/client/renderer/ShaderInstance 1/1
  Patching net/minecraft/client/renderer/Sheets$1 1/1
  Patching net/minecraft/client/renderer/Sheets 1/1
  Patching net/minecraft/client/renderer/SpriteCoordinateExpander 1/1
  Patching net/minecraft/client/renderer/block/BlockModelShaper 1/1
  Patching net/minecraft/client/renderer/block/BlockRenderDispatcher$1 1/1
  Patching net/minecraft/client/renderer/block/BlockRenderDispatcher 1/1
  Patching net/minecraft/client/renderer/block/LiquidBlockRenderer$1 1/1
  Patching net/minecraft/client/renderer/block/LiquidBlockRenderer 1/1
  Patching net/minecraft/client/renderer/block/ModelBlockRenderer$1 1/1
  Patching net/minecraft/client/renderer/block/ModelBlockRenderer$AdjacencyInfo 1/1
  Patching net/minecraft/client/renderer/block/ModelBlockRenderer$AmbientOcclusionFace 1/1
  Patching net/minecraft/client/renderer/block/ModelBlockRenderer$AmbientVertexRemap 1/1
  Patching net/minecraft/client/renderer/block/ModelBlockRenderer$Cache$1 1/1
  Patching net/minecraft/client/renderer/block/ModelBlockRenderer$Cache$2 1/1
  Patching net/minecraft/client/renderer/block/ModelBlockRenderer$Cache 1/1
  Patching net/minecraft/client/renderer/block/ModelBlockRenderer$SizeInfo 1/1
  Patching net/minecraft/client/renderer/block/ModelBlockRenderer 1/1
  Patching net/minecraft/client/renderer/block/model/BakedQuad 1/1
  Patching net/minecraft/client/renderer/block/model/BlockElementFace$Deserializer 1/1
  Patching net/minecraft/client/renderer/block/model/BlockElementFace 1/1
  Patching net/minecraft/client/renderer/block/model/BlockModel$Deserializer 1/1
  Patching net/minecraft/client/renderer/block/model/BlockModel$GuiLight 1/1
  Patching net/minecraft/client/renderer/block/model/BlockModel$LoopException 1/1
  Patching net/minecraft/client/renderer/block/model/BlockModel 1/1
  Patching net/minecraft/client/renderer/block/model/FaceBakery$1 1/1
  Patching net/minecraft/client/renderer/block/model/FaceBakery 1/1
  Patching net/minecraft/client/renderer/block/model/ItemModelGenerator$Span 1/1
  Patching net/minecraft/client/renderer/block/model/ItemModelGenerator$SpanFacing 1/1
  Patching net/minecraft/client/renderer/block/model/ItemModelGenerator 1/1
  Patching net/minecraft/client/renderer/block/model/ItemOverrides$BakedOverride 1/1
  Patching net/minecraft/client/renderer/block/model/ItemOverrides$PropertyMatcher 1/1
  Patching net/minecraft/client/renderer/block/model/ItemOverrides 1/1
  Patching net/minecraft/client/renderer/block/model/ItemTransform$Deserializer 1/1
  Patching net/minecraft/client/renderer/block/model/ItemTransform 1/1
  Patching net/minecraft/client/renderer/block/model/ItemTransforms$1 1/1
  Patching net/minecraft/client/renderer/block/model/ItemTransforms$Deserializer 1/1
  Patching net/minecraft/client/renderer/block/model/ItemTransforms 1/1
  Patching net/minecraft/client/renderer/block/model/MultiVariant$Deserializer 1/1
  Patching net/minecraft/client/renderer/block/model/MultiVariant 1/1
  Patching net/minecraft/client/renderer/blockentity/BlockEntityRenderers 1/1
  Patching net/minecraft/client/renderer/blockentity/ChestRenderer 1/1
  Patching net/minecraft/client/renderer/blockentity/PistonHeadRenderer 1/1
  Patching net/minecraft/client/renderer/blockentity/SkullBlockRenderer 1/1
  Patching net/minecraft/client/renderer/chunk/RenderChunkRegion 1/1
  Patching net/minecraft/client/renderer/chunk/SectionCompiler$Results 1/1
  Patching net/minecraft/client/renderer/chunk/SectionCompiler 1/1
  Patching net/minecraft/client/renderer/culling/Frustum 1/1
  Patching net/minecraft/client/renderer/entity/BoatRenderer 1/1
  Patching net/minecraft/client/renderer/entity/EntityRenderDispatcher 1/1
  Patching net/minecraft/client/renderer/entity/EntityRenderer 1/1
  Patching net/minecraft/client/renderer/entity/FallingBlockRenderer 1/1
  Patching net/minecraft/client/renderer/entity/FishingHookRenderer 1/1
  Patching net/minecraft/client/renderer/entity/ItemFrameRenderer 1/1
  Patching net/minecraft/client/renderer/entity/ItemRenderer 1/1
  Patching net/minecraft/client/renderer/entity/LivingEntityRenderer$1 1/1
  Patching net/minecraft/client/renderer/entity/LivingEntityRenderer 1/1
  Patching net/minecraft/client/renderer/entity/layers/ElytraLayer 1/1
  Patching net/minecraft/client/renderer/entity/layers/HumanoidArmorLayer$1 1/1
  Patching net/minecraft/client/renderer/entity/layers/HumanoidArmorLayer 1/1
  Patching net/minecraft/client/renderer/entity/player/PlayerRenderer 1/1
  Patching net/minecraft/client/renderer/item/ItemProperties$1 1/1
  Patching net/minecraft/client/renderer/item/ItemProperties 1/1
  Patching net/minecraft/client/renderer/texture/AbstractTexture 1/1
  Patching net/minecraft/client/renderer/texture/MipmapGenerator 1/1
  Patching net/minecraft/client/renderer/texture/SpriteContents$AnimatedTexture 1/1
  Patching net/minecraft/client/renderer/texture/SpriteContents$FrameInfo 1/1
  Patching net/minecraft/client/renderer/texture/SpriteContents$InterpolationData 1/1
  Patching net/minecraft/client/renderer/texture/SpriteContents$Ticker 1/1
  Patching net/minecraft/client/renderer/texture/SpriteContents 1/1
  Patching net/minecraft/client/renderer/texture/SpriteLoader$Preparations 1/1
  Patching net/minecraft/client/renderer/texture/SpriteLoader 1/1
  Patching net/minecraft/client/renderer/texture/Stitcher$Entry 1/1
  Patching net/minecraft/client/renderer/texture/Stitcher$Holder 1/1
  Patching net/minecraft/client/renderer/texture/Stitcher$Region 1/1
  Patching net/minecraft/client/renderer/texture/Stitcher$SpriteLoader 1/1
  Patching net/minecraft/client/renderer/texture/Stitcher 1/1
  Patching net/minecraft/client/renderer/texture/TextureAtlas 1/1
  Patching net/minecraft/client/renderer/texture/TextureAtlasSprite$1 1/1
  Patching net/minecraft/client/renderer/texture/TextureAtlasSprite$Ticker 1/1
  Patching net/minecraft/client/renderer/texture/TextureAtlasSprite 1/1
  Patching net/minecraft/client/renderer/texture/atlas/SpriteResourceLoader 1/1
  Patching net/minecraft/client/resources/language/ClientLanguage 1/1
  Patching net/minecraft/client/resources/language/I18n 1/1
  Patching net/minecraft/client/resources/language/LanguageManager 1/1
  Patching net/minecraft/client/resources/model/BakedModel 1/1
  Patching net/minecraft/client/resources/model/ModelBaker 1/1
  Patching net/minecraft/client/resources/model/ModelBakery$BakedCacheKey 1/1
  Patching net/minecraft/client/resources/model/ModelBakery$ModelBakerImpl 1/1
  Patching net/minecraft/client/resources/model/ModelBakery$TextureGetter 1/1
  Patching net/minecraft/client/resources/model/ModelBakery 1/1
  Patching net/minecraft/client/resources/model/ModelManager$ReloadState 1/1
  Patching net/minecraft/client/resources/model/ModelManager 1/1
  Patching net/minecraft/client/resources/model/MultiPartBakedModel$Builder 1/1
  Patching net/minecraft/client/resources/model/MultiPartBakedModel 1/1
  Patching net/minecraft/client/resources/model/SimpleBakedModel$Builder 1/1
  Patching net/minecraft/client/resources/model/SimpleBakedModel 1/1
  Patching net/minecraft/client/resources/model/WeightedBakedModel$Builder 1/1
  Patching net/minecraft/client/resources/model/WeightedBakedModel 1/1
  Patching net/minecraft/client/resources/sounds/SoundInstance$Attenuation 1/1
  Patching net/minecraft/client/resources/sounds/SoundInstance 1/1
  Patching net/minecraft/client/server/IntegratedServer 1/1
  Patching net/minecraft/client/server/LanServerDetection$LanServerDetector 1/1
  Patching net/minecraft/client/server/LanServerDetection$LanServerList 1/1
  Patching net/minecraft/client/server/LanServerDetection 1/1
  Patching net/minecraft/client/server/LanServerPinger 1/1
  Patching net/minecraft/client/sounds/SoundEngine$DeviceCheckState 1/1
  Patching net/minecraft/client/sounds/SoundEngine 1/1
  Patching net/minecraft/commands/CommandSourceStack 1/1
  Patching net/minecraft/commands/Commands$1$1 1/1
  Patching net/minecraft/commands/Commands$1 1/1
  Patching net/minecraft/commands/Commands$CommandSelection 1/1
  Patching net/minecraft/commands/Commands$ParseFunction 1/1
  Patching net/minecraft/commands/Commands 1/1
  Patching net/minecraft/commands/arguments/EntityArgument$Info$Template 1/1
  Patching net/minecraft/commands/arguments/EntityArgument$Info 1/1
  Patching net/minecraft/commands/arguments/EntityArgument 1/1
  Patching net/minecraft/commands/arguments/MessageArgument$Message 1/1
  Patching net/minecraft/commands/arguments/MessageArgument$Part 1/1
  Patching net/minecraft/commands/arguments/MessageArgument 1/1
  Patching net/minecraft/commands/arguments/ObjectiveArgument 1/1
  Patching net/minecraft/commands/arguments/ResourceLocationArgument 1/1
  Patching net/minecraft/commands/arguments/TeamArgument 1/1
  Patching net/minecraft/commands/arguments/coordinates/BlockPosArgument 1/1
  Patching net/minecraft/commands/arguments/selector/EntitySelector$1 1/1
  Patching net/minecraft/commands/arguments/selector/EntitySelector 1/1
  Patching net/minecraft/commands/arguments/selector/EntitySelectorParser 1/1
  Patching net/minecraft/commands/synchronization/ArgumentTypeInfos 1/1
  Patching net/minecraft/core/DefaultedMappedRegistry 1/1
  Patching net/minecraft/core/Holder$Direct 1/1
  Patching net/minecraft/core/Holder$Kind 1/1
  Patching net/minecraft/core/Holder$Reference$Type 1/1
  Patching net/minecraft/core/Holder$Reference 1/1
  Patching net/minecraft/core/Holder 1/1
  Patching net/minecraft/core/HolderSet$1 1/1
  Patching net/minecraft/core/HolderSet$Direct 1/1
  Patching net/minecraft/core/HolderSet$ListBacked 1/1
  Patching net/minecraft/core/HolderSet$Named 1/1
  Patching net/minecraft/core/HolderSet 1/1
  Patching net/minecraft/core/MappedRegistry$1 1/1
  Patching net/minecraft/core/MappedRegistry$2 1/1
  Patching net/minecraft/core/MappedRegistry 1/1
  Patching net/minecraft/core/RegistrySetBuilder$1 1/1
  Patching net/minecraft/core/RegistrySetBuilder$1Entry 1/1
  Patching net/minecraft/core/RegistrySetBuilder$2 1/1
  Patching net/minecraft/core/RegistrySetBuilder$3$1 1/1
  Patching net/minecraft/core/RegistrySetBuilder$3 1/1
  Patching net/minecraft/core/RegistrySetBuilder$BuildState$1 1/1
  Patching net/minecraft/core/RegistrySetBuilder$BuildState 1/1
  Patching net/minecraft/core/RegistrySetBuilder$EmptyTagLookup 1/1
  Patching net/minecraft/core/RegistrySetBuilder$EmptyTagLookupWrapper 1/1
  Patching net/minecraft/core/RegistrySetBuilder$EmptyTagRegistryLookup 1/1
  Patching net/minecraft/core/RegistrySetBuilder$LazyHolder 1/1
  Patching net/minecraft/core/RegistrySetBuilder$PatchedRegistries 1/1
  Patching net/minecraft/core/RegistrySetBuilder$RegisteredValue 1/1
  Patching net/minecraft/core/RegistrySetBuilder$RegistryBootstrap 1/1
  Patching net/minecraft/core/RegistrySetBuilder$RegistryContents 1/1
  Patching net/minecraft/core/RegistrySetBuilder$RegistryStub 1/1
  Patching net/minecraft/core/RegistrySetBuilder$UniversalLookup 1/1
  Patching net/minecraft/core/RegistrySetBuilder$UniversalOwner 1/1
  Patching net/minecraft/core/RegistrySetBuilder$ValueAndHolder 1/1
  Patching net/minecraft/core/RegistrySetBuilder 1/1
  Patching net/minecraft/core/RegistrySynchronization$PackedRegistryEntry 1/1
  Patching net/minecraft/core/RegistrySynchronization 1/1
  Patching net/minecraft/core/dispenser/BoatDispenseItemBehavior 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$1 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$10 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$11 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$12 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$13 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$14 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$15 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$16 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$17 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$18 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$2 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$3 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$4 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$5 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$6 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$7 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$8 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior$9 1/1
  Patching net/minecraft/core/dispenser/DispenseItemBehavior 1/1
  Patching net/minecraft/core/particles/BlockParticleOption 1/1
  Patching net/minecraft/core/particles/ItemParticleOption 1/1
  Patching net/minecraft/core/registries/BuiltInRegistries$RegistryBootstrap 1/1
  Patching net/minecraft/core/registries/BuiltInRegistries 1/1
  Patching net/minecraft/core/registries/Registries 1/1
  Patching net/minecraft/data/DataGenerator$PackGenerator 1/1
  Patching net/minecraft/data/DataGenerator 1/1
  Patching net/minecraft/data/HashCache$1 1/1
  Patching net/minecraft/data/HashCache$CacheUpdater 1/1
  Patching net/minecraft/data/HashCache$ProviderCache 1/1
  Patching net/minecraft/data/HashCache$ProviderCacheBuilder 1/1
  Patching net/minecraft/data/HashCache$UpdateFunction 1/1
  Patching net/minecraft/data/HashCache$UpdateResult 1/1
  Patching net/minecraft/data/HashCache 1/1
  Patching net/minecraft/data/Main 1/1
  Patching net/minecraft/data/advancements/AdvancementProvider 1/1
  Patching net/minecraft/data/loot/BlockLootSubProvider 1/1
  Patching net/minecraft/data/loot/EntityLootSubProvider 1/1
  Patching net/minecraft/data/loot/LootTableProvider$SubProviderEntry 1/1
  Patching net/minecraft/data/loot/LootTableProvider 1/1
  Patching net/minecraft/data/recipes/RecipeOutput 1/1
  Patching net/minecraft/data/recipes/RecipeProvider$1 1/1
  Patching net/minecraft/data/recipes/RecipeProvider 1/1
  Patching net/minecraft/data/registries/RegistriesDatapackGenerator 1/1
  Patching net/minecraft/data/registries/RegistryPatchGenerator 1/1
  Patching net/minecraft/data/registries/VanillaRegistries 1/1
  Patching net/minecraft/data/tags/BannerPatternTagsProvider 1/1
  Patching net/minecraft/data/tags/BiomeTagsProvider 1/1
  Patching net/minecraft/data/tags/CatVariantTagsProvider 1/1
  Patching net/minecraft/data/tags/DamageTypeTagsProvider 1/1
  Patching net/minecraft/data/tags/EntityTypeTagsProvider 1/1
  Patching net/minecraft/data/tags/FlatLevelGeneratorPresetTagsProvider 1/1
  Patching net/minecraft/data/tags/FluidTagsProvider 1/1
  Patching net/minecraft/data/tags/GameEventTagsProvider 1/1
  Patching net/minecraft/data/tags/InstrumentTagsProvider 1/1
  Patching net/minecraft/data/tags/IntrinsicHolderTagsProvider$IntrinsicTagAppender 1/1
  Patching net/minecraft/data/tags/IntrinsicHolderTagsProvider 1/1
  Patching net/minecraft/data/tags/ItemTagsProvider 1/1
  Patching net/minecraft/data/tags/PaintingVariantTagsProvider 1/1
  Patching net/minecraft/data/tags/PoiTypeTagsProvider 1/1
  Patching net/minecraft/data/tags/StructureTagsProvider 1/1
  Patching net/minecraft/data/tags/TagsProvider$1CombinedData 1/1
  Patching net/minecraft/data/tags/TagsProvider$TagAppender 1/1
  Patching net/minecraft/data/tags/TagsProvider$TagLookup 1/1
  Patching net/minecraft/data/tags/TagsProvider 1/1
  Patching net/minecraft/data/tags/WorldPresetTagsProvider 1/1
  Patching net/minecraft/data/worldgen/BootstrapContext 1/1
  Patching net/minecraft/gametest/framework/GameTestHelper$1 1/1
  Patching net/minecraft/gametest/framework/GameTestHelper$2 1/1
  Patching net/minecraft/gametest/framework/GameTestHelper 1/1
  Patching net/minecraft/gametest/framework/GameTestRegistry 1/1
  Patching net/minecraft/gametest/framework/GameTestServer$1 1/1
  Patching net/minecraft/gametest/framework/GameTestServer 1/1
  Patching net/minecraft/locale/Language$1 1/1
  Patching net/minecraft/locale/Language 1/1
  Patching net/minecraft/nbt/CompoundTag$1 1/1
  Patching net/minecraft/nbt/CompoundTag$2 1/1
  Patching net/minecraft/nbt/CompoundTag 1/1
  Patching net/minecraft/network/CompressionEncoder 1/1
  Patching net/minecraft/network/Connection$1 1/1
  Patching net/minecraft/network/Connection$2 1/1
  Patching net/minecraft/network/Connection$3 1/1
  Patching net/minecraft/network/Connection 1/1
  Patching net/minecraft/network/FriendlyByteBuf 1/1
  Patching net/minecraft/network/RegistryFriendlyByteBuf 1/1
  Patching net/minecraft/network/chat/contents/TranslatableContents 1/1
  Patching net/minecraft/network/protocol/common/ClientboundCustomPayloadPacket 1/1
  Patching net/minecraft/network/protocol/common/ServerboundCustomPayloadPacket 1/1
  Patching net/minecraft/network/protocol/common/custom/CustomPacketPayload$1 1/1
  Patching net/minecraft/network/protocol/common/custom/CustomPacketPayload$FallbackProvider 1/1
  Patching net/minecraft/network/protocol/common/custom/CustomPacketPayload$Type 1/1
  Patching net/minecraft/network/protocol/common/custom/CustomPacketPayload$TypeAndCodec 1/1
  Patching net/minecraft/network/protocol/common/custom/CustomPacketPayload 1/1
  Patching net/minecraft/network/protocol/login/custom/DiscardedQueryAnswerPayload 1/1
  Patching net/minecraft/network/protocol/login/custom/DiscardedQueryPayload 1/1
  Patching net/minecraft/network/protocol/status/ClientboundStatusResponsePacket 1/1
  Patching net/minecraft/network/protocol/status/ServerStatus$Favicon 1/1
  Patching net/minecraft/network/protocol/status/ServerStatus$Players 1/1
  Patching net/minecraft/network/protocol/status/ServerStatus$Version 1/1
  Patching net/minecraft/network/protocol/status/ServerStatus 1/1
  Patching net/minecraft/network/syncher/EntityDataSerializers$1 1/1
  Patching net/minecraft/network/syncher/EntityDataSerializers$2 1/1
  Patching net/minecraft/network/syncher/EntityDataSerializers$3 1/1
  Patching net/minecraft/network/syncher/EntityDataSerializers$4 1/1
  Patching net/minecraft/network/syncher/EntityDataSerializers 1/1
  Patching net/minecraft/network/syncher/SynchedEntityData$Builder 1/1
  Patching net/minecraft/network/syncher/SynchedEntityData$DataItem 1/1
  Patching net/minecraft/network/syncher/SynchedEntityData$DataValue 1/1
  Patching net/minecraft/network/syncher/SynchedEntityData 1/1
  Patching net/minecraft/recipebook/PlaceRecipe 1/1
  Patching net/minecraft/resources/DelegatingOps 1/1
  Patching net/minecraft/resources/HolderSetCodec 1/1
  Patching net/minecraft/resources/RegistryDataLoader$1 1/1
  Patching net/minecraft/resources/RegistryDataLoader$Loader 1/1
  Patching net/minecraft/resources/RegistryDataLoader$LoadingFunction 1/1
  Patching net/minecraft/resources/RegistryDataLoader$RegistryData 1/1
  Patching net/minecraft/resources/RegistryDataLoader 1/1
  Patching net/minecraft/resources/RegistryOps$HolderLookupAdapter 1/1
  Patching net/minecraft/resources/RegistryOps$RegistryInfo 1/1
  Patching net/minecraft/resources/RegistryOps$RegistryInfoLookup 1/1
  Patching net/minecraft/resources/RegistryOps 1/1
  Patching net/minecraft/resources/ResourceKey$InternKey 1/1
  Patching net/minecraft/resources/ResourceKey 1/1
  Patching net/minecraft/resources/ResourceLocation$Serializer 1/1
  Patching net/minecraft/resources/ResourceLocation 1/1
  Patching net/minecraft/server/Bootstrap$1 1/1
  Patching net/minecraft/server/Bootstrap 1/1
  Patching net/minecraft/server/Eula 1/1
  Patching net/minecraft/server/Main$1 1/1
  Patching net/minecraft/server/Main 1/1
  Patching net/minecraft/server/MinecraftServer$1 1/1
  Patching net/minecraft/server/MinecraftServer$ReloadableResources 1/1
  Patching net/minecraft/server/MinecraftServer$ServerResourcePackInfo 1/1
  Patching net/minecraft/server/MinecraftServer$TimeProfiler$1 1/1
  Patching net/minecraft/server/MinecraftServer$TimeProfiler 1/1
  Patching net/minecraft/server/MinecraftServer 1/1
  Patching net/minecraft/server/PlayerAdvancements$Data 1/1
  Patching net/minecraft/server/PlayerAdvancements 1/1
  Patching net/minecraft/server/ReloadableServerResources$ConfigurableRegistryLookup$1 1/1
  Patching net/minecraft/server/ReloadableServerResources$ConfigurableRegistryLookup 1/1
  Patching net/minecraft/server/ReloadableServerResources$MissingTagAccessPolicy 1/1
  Patching net/minecraft/server/ReloadableServerResources 1/1
  Patching net/minecraft/server/ServerAdvancementManager 1/1
  Patching net/minecraft/server/advancements/AdvancementVisibilityEvaluator$Output 1/1
  Patching net/minecraft/server/advancements/AdvancementVisibilityEvaluator$VisibilityRule 1/1
  Patching net/minecraft/server/advancements/AdvancementVisibilityEvaluator 1/1
  Patching net/minecraft/server/commands/SpreadPlayersCommand$Position 1/1
  Patching net/minecraft/server/commands/SpreadPlayersCommand 1/1
  Patching net/minecraft/server/commands/TeleportCommand$LookAt 1/1
  Patching net/minecraft/server/commands/TeleportCommand$LookAtEntity 1/1
  Patching net/minecraft/server/commands/TeleportCommand$LookAtPosition 1/1
  Patching net/minecraft/server/commands/TeleportCommand 1/1
  Patching net/minecraft/server/dedicated/DedicatedServer$1 1/1
  Patching net/minecraft/server/dedicated/DedicatedServer 1/1
  Patching net/minecraft/server/dedicated/ServerWatchdog$1 1/1
  Patching net/minecraft/server/dedicated/ServerWatchdog 1/1
  Patching net/minecraft/server/dedicated/Settings$MutableValue 1/1
  Patching net/minecraft/server/dedicated/Settings 1/1
  Patching net/minecraft/server/gui/MinecraftServerGui$1 1/1
  Patching net/minecraft/server/gui/MinecraftServerGui$2 1/1
  Patching net/minecraft/server/gui/MinecraftServerGui 1/1
  Patching net/minecraft/server/level/ChunkMap$DistanceManager 1/1
  Patching net/minecraft/server/level/ChunkMap$TrackedEntity 1/1
  Patching net/minecraft/server/level/ChunkMap 1/1
  Patching net/minecraft/server/level/DistanceManager$ChunkTicketTracker 1/1
  Patching net/minecraft/server/level/DistanceManager$FixedPlayerDistanceChunkTracker 1/1
  Patching net/minecraft/server/level/DistanceManager$PlayerTicketTracker 1/1
  Patching net/minecraft/server/level/DistanceManager 1/1
  Patching net/minecraft/server/level/GenerationChunkHolder 1/1
  Patching net/minecraft/server/level/ServerChunkCache$ChunkAndHolder 1/1
  Patching net/minecraft/server/level/ServerChunkCache$MainThreadExecutor 1/1
  Patching net/minecraft/server/level/ServerChunkCache 1/1
  Patching net/minecraft/server/level/ServerEntity 1/1
  Patching net/minecraft/server/level/ServerLevel$EntityCallbacks 1/1
  Patching net/minecraft/server/level/ServerLevel 1/1
  Patching net/minecraft/server/level/ServerPlayer$1 1/1
  Patching net/minecraft/server/level/ServerPlayer$2 1/1
  Patching net/minecraft/server/level/ServerPlayer$RespawnPosAngle 1/1
  Patching net/minecraft/server/level/ServerPlayer 1/1
  Patching net/minecraft/server/level/ServerPlayerGameMode 1/1
  Patching net/minecraft/server/level/Ticket 1/1
  Patching net/minecraft/server/level/WorldGenRegion 1/1
  Patching net/minecraft/server/network/ConfigurationTask$Type 1/1
  Patching net/minecraft/server/network/ConfigurationTask 1/1
  Patching net/minecraft/server/network/MemoryServerHandshakePacketListenerImpl 1/1
  Patching net/minecraft/server/network/PlayerChunkSender 1/1
  Patching net/minecraft/server/network/ServerCommonPacketListenerImpl 1/1
  Patching net/minecraft/server/network/ServerConfigurationPacketListenerImpl 1/1
  Patching net/minecraft/server/network/ServerConnectionListener$1 1/1
  Patching net/minecraft/server/network/ServerConnectionListener$2 1/1
  Patching net/minecraft/server/network/ServerConnectionListener$LatencySimulator$DelayedMessage 1/1
  Patching net/minecraft/server/network/ServerConnectionListener$LatencySimulator 1/1
  Patching net/minecraft/server/network/ServerConnectionListener 1/1
  Patching net/minecraft/server/network/ServerGamePacketListenerImpl$1 1/1
  Patching net/minecraft/server/network/ServerGamePacketListenerImpl$2 1/1
  Patching net/minecraft/server/network/ServerGamePacketListenerImpl$EntityInteraction 1/1
  Patching net/minecraft/server/network/ServerGamePacketListenerImpl 1/1
  Patching net/minecraft/server/network/ServerHandshakePacketListenerImpl$1 1/1
  Patching net/minecraft/server/network/ServerHandshakePacketListenerImpl 1/1
  Patching net/minecraft/server/network/ServerLoginPacketListenerImpl$1 1/1
  Patching net/minecraft/server/network/ServerLoginPacketListenerImpl$State 1/1
  Patching net/minecraft/server/network/ServerLoginPacketListenerImpl 1/1
  Patching net/minecraft/server/network/ServerStatusPacketListenerImpl 1/1
  Patching net/minecraft/server/packs/AbstractPackResources 1/1
  Patching net/minecraft/server/packs/PackResources$ResourceOutput 1/1
  Patching net/minecraft/server/packs/PackResources 1/1
  Patching net/minecraft/server/packs/repository/Pack$Metadata 1/1
  Patching net/minecraft/server/packs/repository/Pack$Position 1/1
  Patching net/minecraft/server/packs/repository/Pack$ResourcesSupplier 1/1
  Patching net/minecraft/server/packs/repository/Pack 1/1
  Patching net/minecraft/server/packs/repository/PackDetector 1/1
  Patching net/minecraft/server/packs/repository/PackRepository 1/1
  Patching net/minecraft/server/packs/repository/ServerPacksSource 1/1
  Patching net/minecraft/server/packs/resources/FallbackResourceManager$1ResourceWithSourceAndIndex 1/1
  Patching net/minecraft/server/packs/resources/FallbackResourceManager$EntryStack 1/1
  Patching net/minecraft/server/packs/resources/FallbackResourceManager$LeakedResourceWarningInputStream 1/1
  Patching net/minecraft/server/packs/resources/FallbackResourceManager$PackEntry 1/1
  Patching net/minecraft/server/packs/resources/FallbackResourceManager$ResourceWithSource 1/1
  Patching net/minecraft/server/packs/resources/FallbackResourceManager 1/1
  Patching net/minecraft/server/packs/resources/ReloadableResourceManager 1/1
  Patching net/minecraft/server/packs/resources/SimpleJsonResourceReloadListener 1/1
  Patching net/minecraft/server/players/PlayerList$1 1/1
  Patching net/minecraft/server/players/PlayerList 1/1
  Patching net/minecraft/server/rcon/RconConsoleSource 1/1
  Patching net/minecraft/server/rcon/thread/RconClient 1/1
  Patching net/minecraft/stats/RecipeBookSettings$TypeSettings 1/1
  Patching net/minecraft/stats/RecipeBookSettings 1/1
  Patching net/minecraft/tags/BannerPatternTags 1/1
  Patching net/minecraft/tags/BiomeTags 1/1
  Patching net/minecraft/tags/BlockTags 1/1
  Patching net/minecraft/tags/CatVariantTags 1/1
  Patching net/minecraft/tags/DamageTypeTags 1/1
  Patching net/minecraft/tags/EnchantmentTags 1/1
  Patching net/minecraft/tags/EntityTypeTags 1/1
  Patching net/minecraft/tags/FlatLevelGeneratorPresetTags 1/1
  Patching net/minecraft/tags/FluidTags 1/1
  Patching net/minecraft/tags/GameEventTags 1/1
  Patching net/minecraft/tags/InstrumentTags 1/1
  Patching net/minecraft/tags/ItemTags 1/1
  Patching net/minecraft/tags/PaintingVariantTags 1/1
  Patching net/minecraft/tags/PoiTypeTags 1/1
  Patching net/minecraft/tags/StructureTags 1/1
  Patching net/minecraft/tags/TagBuilder 1/1
  Patching net/minecraft/tags/TagEntry$Lookup 1/1
  Patching net/minecraft/tags/TagEntry 1/1
  Patching net/minecraft/tags/TagFile 1/1
  Patching net/minecraft/tags/TagKey 1/1
  Patching net/minecraft/tags/TagLoader$1 1/1
  Patching net/minecraft/tags/TagLoader$EntryWithSource 1/1
  Patching net/minecraft/tags/TagLoader$SortingEntry 1/1
  Patching net/minecraft/tags/TagLoader 1/1
  Patching net/minecraft/tags/WorldPresetTags 1/1
  Patching net/minecraft/util/SpawnUtil$Strategy 1/1
  Patching net/minecraft/util/SpawnUtil 1/1
  Patching net/minecraft/util/datafix/fixes/StructuresBecomeConfiguredFix$Conversion 1/1
  Patching net/minecraft/util/datafix/fixes/StructuresBecomeConfiguredFix 1/1
  Patching net/minecraft/util/datafix/schemas/V2832 1/1
  Patching net/minecraft/world/effect/MobEffect$AttributeTemplate 1/1
  Patching net/minecraft/world/effect/MobEffect 1/1
  Patching net/minecraft/world/entity/Entity$1 1/1
  Patching net/minecraft/world/entity/Entity$MoveFunction 1/1
  Patching net/minecraft/world/entity/Entity$MovementEmission 1/1
  Patching net/minecraft/world/entity/Entity$RemovalReason 1/1
  Patching net/minecraft/world/entity/Entity 1/1
  Patching net/minecraft/world/entity/EntityType$1 1/1
  Patching net/minecraft/world/entity/EntityType$Builder 1/1
  Patching net/minecraft/world/entity/EntityType$EntityFactory 1/1
  Patching net/minecraft/world/entity/EntityType 1/1
  Patching net/minecraft/world/entity/ExperienceOrb 1/1
  Patching net/minecraft/world/entity/FlyingMob 1/1
  Patching net/minecraft/world/entity/LightningBolt 1/1
  Patching net/minecraft/world/entity/LivingEntity$1 1/1
  Patching net/minecraft/world/entity/LivingEntity$Fallsounds 1/1
  Patching net/minecraft/world/entity/LivingEntity 1/1
  Patching net/minecraft/world/entity/Mob$1 1/1
  Patching net/minecraft/world/entity/Mob 1/1
  Patching net/minecraft/world/entity/MobCategory 1/1
  Patching net/minecraft/world/entity/Shearable 1/1
  Patching net/minecraft/world/entity/SpawnPlacementTypes$1 1/1
  Patching net/minecraft/world/entity/SpawnPlacementTypes 1/1
  Patching net/minecraft/world/entity/SpawnPlacements$Data 1/1
  Patching net/minecraft/world/entity/SpawnPlacements$SpawnPredicate 1/1
  Patching net/minecraft/world/entity/SpawnPlacements 1/1
  Patching net/minecraft/world/entity/TamableAnimal$TamableAnimalPanicGoal 1/1
  Patching net/minecraft/world/entity/TamableAnimal 1/1
  Patching net/minecraft/world/entity/ai/Brain$1 1/1
  Patching net/minecraft/world/entity/ai/Brain$MemoryValue 1/1
  Patching net/minecraft/world/entity/ai/Brain$Provider 1/1
  Patching net/minecraft/world/entity/ai/Brain 1/1
  Patching net/minecraft/world/entity/ai/attributes/AttributeSupplier$Builder 1/1
  Patching net/minecraft/world/entity/ai/attributes/AttributeSupplier 1/1
  Patching net/minecraft/world/entity/ai/attributes/DefaultAttributes 1/1
  Patching net/minecraft/world/entity/ai/behavior/CrossbowAttack$CrossbowState 1/1
  Patching net/minecraft/world/entity/ai/behavior/CrossbowAttack 1/1
  Patching net/minecraft/world/entity/ai/behavior/HarvestFarmland 1/1
  Patching net/minecraft/world/entity/ai/behavior/StartAttacking 1/1
  Patching net/minecraft/world/entity/ai/behavior/Swim 1/1
  Patching net/minecraft/world/entity/ai/goal/BreakDoorGoal 1/1
  Patching net/minecraft/world/entity/ai/goal/EatBlockGoal 1/1
  Patching net/minecraft/world/entity/ai/goal/FloatGoal 1/1
  Patching net/minecraft/world/entity/ai/goal/MeleeAttackGoal 1/1
  Patching net/minecraft/world/entity/ai/goal/RangedBowAttackGoal 1/1
  Patching net/minecraft/world/entity/ai/goal/RangedCrossbowAttackGoal$CrossbowState 1/1
  Patching net/minecraft/world/entity/ai/goal/RangedCrossbowAttackGoal 1/1
  Patching net/minecraft/world/entity/ai/goal/RemoveBlockGoal 1/1
  Patching net/minecraft/world/entity/ai/goal/RunAroundLikeCrazyGoal 1/1
  Patching net/minecraft/world/entity/ai/goal/target/HurtByTargetGoal 1/1
  Patching net/minecraft/world/entity/ai/navigation/PathNavigation 1/1
  Patching net/minecraft/world/entity/ai/navigation/WallClimberNavigation 1/1
  Patching net/minecraft/world/entity/ai/village/VillageSiege$State 1/1
  Patching net/minecraft/world/entity/ai/village/VillageSiege 1/1
  Patching net/minecraft/world/entity/ai/village/poi/PoiTypes 1/1
  Patching net/minecraft/world/entity/animal/Animal 1/1
  Patching net/minecraft/world/entity/animal/Bee$1 1/1
  Patching net/minecraft/world/entity/animal/Bee$BaseBeeGoal 1/1
  Patching net/minecraft/world/entity/animal/Bee$BeeAttackGoal 1/1
  Patching net/minecraft/world/entity/animal/Bee$BeeBecomeAngryTargetGoal 1/1
  Patching net/minecraft/world/entity/animal/Bee$BeeEnterHiveGoal 1/1
  Patching net/minecraft/world/entity/animal/Bee$BeeGoToHiveGoal 1/1
  Patching net/minecraft/world/entity/animal/Bee$BeeGoToKnownFlowerGoal 1/1
  Patching net/minecraft/world/entity/animal/Bee$BeeGrowCropGoal 1/1
  Patching net/minecraft/world/entity/animal/Bee$BeeHurtByOtherGoal 1/1
  Patching net/minecraft/world/entity/animal/Bee$BeeLocateHiveGoal 1/1
  Patching net/minecraft/world/entity/animal/Bee$BeeLookControl 1/1
  Patching net/minecraft/world/entity/animal/Bee$BeePollinateGoal 1/1
  Patching net/minecraft/world/entity/animal/Bee$BeeWanderGoal 1/1
  Patching net/minecraft/world/entity/animal/Bee 1/1
  Patching net/minecraft/world/entity/animal/Cat$CatAvoidEntityGoal 1/1
  Patching net/minecraft/world/entity/animal/Cat$CatRelaxOnOwnerGoal 1/1
  Patching net/minecraft/world/entity/animal/Cat$CatTemptGoal 1/1
  Patching net/minecraft/world/entity/animal/Cat 1/1
  Patching net/minecraft/world/entity/animal/Fox$DefendTrustedTargetGoal 1/1
  Patching net/minecraft/world/entity/animal/Fox$FaceplantGoal 1/1
  Patching net/minecraft/world/entity/animal/Fox$FoxAlertableEntitiesSelector 1/1
  Patching net/minecraft/world/entity/animal/Fox$FoxBehaviorGoal 1/1
  Patching net/minecraft/world/entity/animal/Fox$FoxBreedGoal 1/1
  Patching net/minecraft/world/entity/animal/Fox$FoxEatBerriesGoal 1/1
  Patching net/minecraft/world/entity/animal/Fox$FoxFloatGoal 1/1
  Patching net/minecraft/world/entity/animal/Fox$FoxFollowParentGoal 1/1
  Patching net/minecraft/world/entity/animal/Fox$FoxGroupData 1/1
  Patching net/minecraft/world/entity/animal/Fox$FoxLookAtPlayerGoal 1/1
  Patching net/minecraft/world/entity/animal/Fox$FoxLookControl 1/1
  Patching net/minecraft/world/entity/animal/Fox$FoxMeleeAttackGoal 1/1
  Patching net/minecraft/world/entity/animal/Fox$FoxMoveControl 1/1
  Patching net/minecraft/world/entity/animal/Fox$FoxPanicGoal 1/1
  Patching net/minecraft/world/entity/animal/Fox$FoxPounceGoal 1/1
  Patching net/minecraft/world/entity/animal/Fox$FoxSearchForItemsGoal 1/1
  Patching net/minecraft/world/entity/animal/Fox$FoxStrollThroughVillageGoal 1/1
  Patching net/minecraft/world/entity/animal/Fox$PerchAndSearchGoal 1/1
  Patching net/minecraft/world/entity/animal/Fox$SeekShelterGoal 1/1
  Patching net/minecraft/world/entity/animal/Fox$SleepGoal 1/1
  Patching net/minecraft/world/entity/animal/Fox$StalkPreyGoal 1/1
  Patching net/minecraft/world/entity/animal/Fox$Type 1/1
  Patching net/minecraft/world/entity/animal/Fox 1/1
  Patching net/minecraft/world/entity/animal/MushroomCow$MushroomType 1/1
  Patching net/minecraft/world/entity/animal/MushroomCow 1/1
  Patching net/minecraft/world/entity/animal/Ocelot$OcelotAvoidEntityGoal 1/1
  Patching net/minecraft/world/entity/animal/Ocelot$OcelotTemptGoal 1/1
  Patching net/minecraft/world/entity/animal/Ocelot 1/1
  Patching net/minecraft/world/entity/animal/Parrot$1 1/1
  Patching net/minecraft/world/entity/animal/Parrot$ParrotWanderGoal 1/1
  Patching net/minecraft/world/entity/animal/Parrot$Variant 1/1
  Patching net/minecraft/world/entity/animal/Parrot 1/1
  Patching net/minecraft/world/entity/animal/Pig 1/1
  Patching net/minecraft/world/entity/animal/Rabbit$RabbitAvoidEntityGoal 1/1
  Patching net/minecraft/world/entity/animal/Rabbit$RabbitGroupData 1/1
  Patching net/minecraft/world/entity/animal/Rabbit$RabbitJumpControl 1/1
  Patching net/minecraft/world/entity/animal/Rabbit$RabbitMoveControl 1/1
  Patching net/minecraft/world/entity/animal/Rabbit$RabbitPanicGoal 1/1
  Patching net/minecraft/world/entity/animal/Rabbit$RaidGardenGoal 1/1
  Patching net/minecraft/world/entity/animal/Rabbit$Variant 1/1
  Patching net/minecraft/world/entity/animal/Rabbit 1/1
  Patching net/minecraft/world/entity/animal/Sheep$1 1/1
  Patching net/minecraft/world/entity/animal/Sheep 1/1
  Patching net/minecraft/world/entity/animal/SnowGolem 1/1
  Patching net/minecraft/world/entity/animal/Wolf$WolfAvoidEntityGoal 1/1
  Patching net/minecraft/world/entity/animal/Wolf$WolfPackData 1/1
  Patching net/minecraft/world/entity/animal/Wolf 1/1
  Patching net/minecraft/world/entity/animal/allay/Allay$JukeboxListener 1/1
  Patching net/minecraft/world/entity/animal/allay/Allay$VibrationUser 1/1
  Patching net/minecraft/world/entity/animal/allay/Allay 1/1
  Patching net/minecraft/world/entity/animal/camel/Camel$CamelBodyRotationControl 1/1
  Patching net/minecraft/world/entity/animal/camel/Camel$CamelLookControl 1/1
  Patching net/minecraft/world/entity/animal/camel/Camel$CamelMoveControl 1/1
  Patching net/minecraft/world/entity/animal/camel/Camel 1/1
  Patching net/minecraft/world/entity/animal/frog/Tadpole 1/1
  Patching net/minecraft/world/entity/animal/horse/AbstractHorse$1 1/1
  Patching net/minecraft/world/entity/animal/horse/AbstractHorse$2 1/1
  Patching net/minecraft/world/entity/animal/horse/AbstractHorse 1/1
  Patching net/minecraft/world/entity/animal/horse/Llama$LlamaAttackWolfGoal 1/1
  Patching net/minecraft/world/entity/animal/horse/Llama$LlamaGroupData 1/1
  Patching net/minecraft/world/entity/animal/horse/Llama$LlamaHurtByTargetGoal 1/1
  Patching net/minecraft/world/entity/animal/horse/Llama$Variant 1/1
  Patching net/minecraft/world/entity/animal/horse/Llama 1/1
  Patching net/minecraft/world/entity/animal/horse/SkeletonTrapGoal 1/1
  Patching net/minecraft/world/entity/animal/sniffer/Sniffer$State 1/1
  Patching net/minecraft/world/entity/animal/sniffer/Sniffer 1/1
  Patching net/minecraft/world/entity/boss/EnderDragonPart 1/1
  Patching net/minecraft/world/entity/boss/enderdragon/EnderDragon 1/1
  Patching net/minecraft/world/entity/boss/wither/WitherBoss$WitherDoNothingGoal 1/1
  Patching net/minecraft/world/entity/boss/wither/WitherBoss 1/1
  Patching net/minecraft/world/entity/decoration/ArmorStand$1 1/1
  Patching net/minecraft/world/entity/decoration/ArmorStand 1/1
  Patching net/minecraft/world/entity/decoration/HangingEntity$1 1/1
  Patching net/minecraft/world/entity/decoration/HangingEntity 1/1
  Patching net/minecraft/world/entity/item/FallingBlockEntity 1/1
  Patching net/minecraft/world/entity/item/ItemEntity 1/1
  Patching net/minecraft/world/entity/monster/AbstractSkeleton$1 1/1
  Patching net/minecraft/world/entity/monster/AbstractSkeleton 1/1
  Patching net/minecraft/world/entity/monster/Bogged 1/1
  Patching net/minecraft/world/entity/monster/CrossbowAttackMob 1/1
  Patching net/minecraft/world/entity/monster/EnderMan$EndermanFreezeWhenLookedAt 1/1
  Patching net/minecraft/world/entity/monster/EnderMan$EndermanLeaveBlockGoal 1/1
  Patching net/minecraft/world/entity/monster/EnderMan$EndermanLookForPlayerGoal 1/1
  Patching net/minecraft/world/entity/monster/EnderMan$EndermanTakeBlockGoal 1/1
  Patching net/minecraft/world/entity/monster/EnderMan 1/1
  Patching net/minecraft/world/entity/monster/Evoker$EvokerAttackSpellGoal 1/1
  Patching net/minecraft/world/entity/monster/Evoker$EvokerCastingSpellGoal 1/1
  Patching net/minecraft/world/entity/monster/Evoker$EvokerSummonSpellGoal 1/1
  Patching net/minecraft/world/entity/monster/Evoker$EvokerWololoSpellGoal 1/1
  Patching net/minecraft/world/entity/monster/Evoker 1/1
  Patching net/minecraft/world/entity/monster/Husk 1/1
  Patching net/minecraft/world/entity/monster/Illusioner$IllusionerBlindnessSpellGoal 1/1
  Patching net/minecraft/world/entity/monster/Illusioner$IllusionerMirrorSpellGoal 1/1
  Patching net/minecraft/world/entity/monster/Illusioner 1/1
  Patching net/minecraft/world/entity/monster/MagmaCube 1/1
  Patching net/minecraft/world/entity/monster/Monster 1/1
  Patching net/minecraft/world/entity/monster/Pillager 1/1
  Patching net/minecraft/world/entity/monster/Ravager 1/1
  Patching net/minecraft/world/entity/monster/Shulker$ShulkerAttackGoal 1/1
  Patching net/minecraft/world/entity/monster/Shulker$ShulkerBodyRotationControl 1/1
  Patching net/minecraft/world/entity/monster/Shulker$ShulkerDefenseAttackGoal 1/1
  Patching net/minecraft/world/entity/monster/Shulker$ShulkerLookControl 1/1
  Patching net/minecraft/world/entity/monster/Shulker$ShulkerNearestAttackGoal 1/1
  Patching net/minecraft/world/entity/monster/Shulker$ShulkerPeekGoal 1/1
  Patching net/minecraft/world/entity/monster/Shulker 1/1
  Patching net/minecraft/world/entity/monster/Silverfish$SilverfishMergeWithStoneGoal 1/1
  Patching net/minecraft/world/entity/monster/Silverfish$SilverfishWakeUpFriendsGoal 1/1
  Patching net/minecraft/world/entity/monster/Silverfish 1/1
  Patching net/minecraft/world/entity/monster/Skeleton 1/1
  Patching net/minecraft/world/entity/monster/Slime$SlimeAttackGoal 1/1
  Patching net/minecraft/world/entity/monster/Slime$SlimeFloatGoal 1/1
  Patching net/minecraft/world/entity/monster/Slime$SlimeKeepOnJumpingGoal 1/1
  Patching net/minecraft/world/entity/monster/Slime$SlimeMoveControl 1/1
  Patching net/minecraft/world/entity/monster/Slime$SlimeRandomDirectionGoal 1/1
  Patching net/minecraft/world/entity/monster/Slime 1/1
  Patching net/minecraft/world/entity/monster/Spider$SpiderAttackGoal 1/1
  Patching net/minecraft/world/entity/monster/Spider$SpiderEffectsGroupData 1/1
  Patching net/minecraft/world/entity/monster/Spider$SpiderTargetGoal 1/1
  Patching net/minecraft/world/entity/monster/Spider 1/1
  Patching net/minecraft/world/entity/monster/Zombie$ZombieAttackTurtleEggGoal 1/1
  Patching net/minecraft/world/entity/monster/Zombie$ZombieGroupData 1/1
  Patching net/minecraft/world/entity/monster/Zombie 1/1
  Patching net/minecraft/world/entity/monster/ZombieVillager 1/1
  Patching net/minecraft/world/entity/monster/hoglin/Hoglin 1/1
  Patching net/minecraft/world/entity/monster/piglin/AbstractPiglin 1/1
  Patching net/minecraft/world/entity/monster/piglin/Piglin 1/1
  Patching net/minecraft/world/entity/monster/piglin/PiglinAi 1/1
  Patching net/minecraft/world/entity/monster/piglin/StopHoldingItemIfNoLongerAdmiring 1/1
  Patching net/minecraft/world/entity/npc/AbstractVillager 1/1
  Patching net/minecraft/world/entity/npc/CatSpawner 1/1
  Patching net/minecraft/world/entity/npc/Villager 1/1
  Patching net/minecraft/world/entity/player/Inventory 1/1
  Patching net/minecraft/world/entity/player/Player$1 1/1
  Patching net/minecraft/world/entity/player/Player$2 1/1
  Patching net/minecraft/world/entity/player/Player$BedSleepingProblem 1/1
  Patching net/minecraft/world/entity/player/Player 1/1
  Patching net/minecraft/world/entity/projectile/AbstractArrow$Pickup 1/1
  Patching net/minecraft/world/entity/projectile/AbstractArrow 1/1
  Patching net/minecraft/world/entity/projectile/AbstractHurtingProjectile 1/1
  Patching net/minecraft/world/entity/projectile/FireworkRocketEntity 1/1
  Patching net/minecraft/world/entity/projectile/FishingHook$FishHookState 1/1
  Patching net/minecraft/world/entity/projectile/FishingHook$OpenWaterType 1/1
  Patching net/minecraft/world/entity/projectile/FishingHook 1/1
  Patching net/minecraft/world/entity/projectile/LargeFireball 1/1
  Patching net/minecraft/world/entity/projectile/LlamaSpit 1/1
  Patching net/minecraft/world/entity/projectile/Projectile 1/1
  Patching net/minecraft/world/entity/projectile/ProjectileUtil 1/1
  Patching net/minecraft/world/entity/projectile/ShulkerBullet 1/1
  Patching net/minecraft/world/entity/projectile/SmallFireball 1/1
  Patching net/minecraft/world/entity/projectile/ThrowableProjectile 1/1
  Patching net/minecraft/world/entity/projectile/ThrownEnderpearl 1/1
  Patching net/minecraft/world/entity/projectile/WitherSkull 1/1
  Patching net/minecraft/world/entity/raid/Raid$1 1/1
  Patching net/minecraft/world/entity/raid/Raid$RaidStatus 1/1
  Patching net/minecraft/world/entity/raid/Raid$RaiderType 1/1
  Patching net/minecraft/world/entity/raid/Raid 1/1
  Patching net/minecraft/world/entity/vehicle/AbstractMinecart$1 1/1
  Patching net/minecraft/world/entity/vehicle/AbstractMinecart$Type 1/1
  Patching net/minecraft/world/entity/vehicle/AbstractMinecart 1/1
  Patching net/minecraft/world/entity/vehicle/AbstractMinecartContainer 1/1
  Patching net/minecraft/world/entity/vehicle/Boat$Status 1/1
  Patching net/minecraft/world/entity/vehicle/Boat$Type 1/1
  Patching net/minecraft/world/entity/vehicle/Boat 1/1
  Patching net/minecraft/world/entity/vehicle/ChestBoat$1 1/1
  Patching net/minecraft/world/entity/vehicle/ChestBoat 1/1
  Patching net/minecraft/world/entity/vehicle/ContainerEntity$1 1/1
  Patching net/minecraft/world/entity/vehicle/ContainerEntity 1/1
  Patching net/minecraft/world/entity/vehicle/Minecart 1/1
  Patching net/minecraft/world/entity/vehicle/MinecartCommandBlock$MinecartCommandBase 1/1
  Patching net/minecraft/world/entity/vehicle/MinecartCommandBlock 1/1
  Patching net/minecraft/world/entity/vehicle/MinecartFurnace 1/1
  Patching net/minecraft/world/entity/vehicle/MinecartSpawner$1 1/1
  Patching net/minecraft/world/entity/vehicle/MinecartSpawner 1/1
  Patching net/minecraft/world/inventory/AbstractContainerMenu$1 1/1
  Patching net/minecraft/world/inventory/AbstractContainerMenu 1/1
  Patching net/minecraft/world/inventory/AbstractFurnaceMenu 1/1
  Patching net/minecraft/world/inventory/AnvilMenu 1/1
  Patching net/minecraft/world/inventory/ArmorSlot 1/1
  Patching net/minecraft/world/inventory/BeaconMenu$1 1/1
  Patching net/minecraft/world/inventory/BeaconMenu$PaymentSlot 1/1
  Patching net/minecraft/world/inventory/BeaconMenu 1/1
  Patching net/minecraft/world/inventory/BrewingStandMenu$FuelSlot 1/1
  Patching net/minecraft/world/inventory/BrewingStandMenu$IngredientsSlot 1/1
  Patching net/minecraft/world/inventory/BrewingStandMenu$PotionSlot 1/1
  Patching net/minecraft/world/inventory/BrewingStandMenu 1/1
  Patching net/minecraft/world/inventory/EnchantmentMenu$1 1/1
  Patching net/minecraft/world/inventory/EnchantmentMenu$2 1/1
  Patching net/minecraft/world/inventory/EnchantmentMenu$3 1/1
  Patching net/minecraft/world/inventory/EnchantmentMenu 1/1
  Patching net/minecraft/world/inventory/FurnaceResultSlot 1/1
  Patching net/minecraft/world/inventory/GrindstoneMenu$1 1/1
  Patching net/minecraft/world/inventory/GrindstoneMenu$2 1/1
  Patching net/minecraft/world/inventory/GrindstoneMenu$3 1/1
  Patching net/minecraft/world/inventory/GrindstoneMenu$4 1/1
  Patching net/minecraft/world/inventory/GrindstoneMenu 1/1
  Patching net/minecraft/world/inventory/MenuType$MenuSupplier 1/1
  Patching net/minecraft/world/inventory/MenuType 1/1
  Patching net/minecraft/world/inventory/RecipeBookMenu 1/1
  Patching net/minecraft/world/inventory/RecipeBookType 1/1
  Patching net/minecraft/world/inventory/ResultSlot 1/1
  Patching net/minecraft/world/inventory/Slot 1/1
  Patching net/minecraft/world/item/AnimalArmorItem$BodyType 1/1
  Patching net/minecraft/world/item/AnimalArmorItem 1/1
  Patching net/minecraft/world/item/ArmorMaterial$Layer 1/1
  Patching net/minecraft/world/item/ArmorMaterial 1/1
  Patching net/minecraft/world/item/ArrowItem 1/1
  Patching net/minecraft/world/item/AxeItem 1/1
  Patching net/minecraft/world/item/BlockItem 1/1
  Patching net/minecraft/world/item/BoneMealItem$1 1/1
  Patching net/minecraft/world/item/BoneMealItem 1/1
  Patching net/minecraft/world/item/BowItem 1/1
  Patching net/minecraft/world/item/BucketItem 1/1
  Patching net/minecraft/world/item/BundleItem 1/1
  Patching net/minecraft/world/item/ChorusFruitItem 1/1
  Patching net/minecraft/world/item/CreativeModeTab$Builder 1/1
  Patching net/minecraft/world/item/CreativeModeTab$DisplayItemsGenerator 1/1
  Patching net/minecraft/world/item/CreativeModeTab$ItemDisplayBuilder 1/1
  Patching net/minecraft/world/item/CreativeModeTab$ItemDisplayParameters 1/1
  Patching net/minecraft/world/item/CreativeModeTab$Output 1/1
  Patching net/minecraft/world/item/CreativeModeTab$Row 1/1
  Patching net/minecraft/world/item/CreativeModeTab$TabVisibility 1/1
  Patching net/minecraft/world/item/CreativeModeTab$Type 1/1
  Patching net/minecraft/world/item/CreativeModeTab 1/1
  Patching net/minecraft/world/item/CrossbowItem$ChargingSounds 1/1
  Patching net/minecraft/world/item/CrossbowItem 1/1
  Patching net/minecraft/world/item/DispensibleContainerItem 1/1
  Patching net/minecraft/world/item/DyeColor 1/1
  Patching net/minecraft/world/item/ElytraItem 1/1
  Patching net/minecraft/world/item/FishingRodItem 1/1
  Patching net/minecraft/world/item/HoeItem 1/1
  Patching net/minecraft/world/item/Item$Properties 1/1
  Patching net/minecraft/world/item/Item$TooltipContext$1 1/1
  Patching net/minecraft/world/item/Item$TooltipContext$2 1/1
  Patching net/minecraft/world/item/Item$TooltipContext$3 1/1
  Patching net/minecraft/world/item/Item$TooltipContext 1/1
  Patching net/minecraft/world/item/Item 1/1
  Patching net/minecraft/world/item/ItemDisplayContext 1/1
  Patching net/minecraft/world/item/ItemStack$1 1/1
  Patching net/minecraft/world/item/ItemStack$2 1/1
  Patching net/minecraft/world/item/ItemStack$3 1/1
  Patching net/minecraft/world/item/ItemStack$4 1/1
  Patching net/minecraft/world/item/ItemStack 1/1
  Patching net/minecraft/world/item/Items 1/1
  Patching net/minecraft/world/item/MinecartItem$1 1/1
  Patching net/minecraft/world/item/MinecartItem 1/1
  Patching net/minecraft/world/item/MobBucketItem 1/1
  Patching net/minecraft/world/item/PickaxeItem 1/1
  Patching net/minecraft/world/item/ProjectileWeaponItem 1/1
  Patching net/minecraft/world/item/ShearsItem 1/1
  Patching net/minecraft/world/item/ShieldItem 1/1
  Patching net/minecraft/world/item/ShovelItem 1/1
  Patching net/minecraft/world/item/SpawnEggItem 1/1
  Patching net/minecraft/world/item/StandingAndWallBlockItem 1/1
  Patching net/minecraft/world/item/SwordItem 1/1
  Patching net/minecraft/world/item/UseAnim 1/1
  Patching net/minecraft/world/item/alchemy/PotionBrewing$Builder 1/1
  Patching net/minecraft/world/item/alchemy/PotionBrewing$Mix 1/1
  Patching net/minecraft/world/item/alchemy/PotionBrewing 1/1
  Patching net/minecraft/world/item/crafting/BannerDuplicateRecipe 1/1
  Patching net/minecraft/world/item/crafting/BookCloningRecipe 1/1
  Patching net/minecraft/world/item/crafting/Ingredient$ItemValue 1/1
  Patching net/minecraft/world/item/crafting/Ingredient$TagValue 1/1
  Patching net/minecraft/world/item/crafting/Ingredient$Value 1/1
  Patching net/minecraft/world/item/crafting/Ingredient 1/1
  Patching net/minecraft/world/item/crafting/Recipe 1/1
  Patching net/minecraft/world/item/crafting/RecipeManager$1 1/1
  Patching net/minecraft/world/item/crafting/RecipeManager$CachedCheck 1/1
  Patching net/minecraft/world/item/crafting/RecipeManager 1/1
  Patching net/minecraft/world/item/crafting/RecipeType$1 1/1
  Patching net/minecraft/world/item/crafting/RecipeType 1/1
  Patching net/minecraft/world/item/crafting/ShapedRecipe$Serializer 1/1
  Patching net/minecraft/world/item/crafting/ShapedRecipe 1/1
  Patching net/minecraft/world/item/crafting/ShapedRecipePattern$Data 1/1
  Patching net/minecraft/world/item/crafting/ShapedRecipePattern 1/1
  Patching net/minecraft/world/item/crafting/ShapelessRecipe$Serializer 1/1
  Patching net/minecraft/world/item/crafting/ShapelessRecipe 1/1
  Patching net/minecraft/world/item/crafting/ShulkerBoxColoring 1/1
  Patching net/minecraft/world/item/crafting/SmithingTransformRecipe$Serializer 1/1
  Patching net/minecraft/world/item/crafting/SmithingTransformRecipe 1/1
  Patching net/minecraft/world/item/crafting/SmithingTrimRecipe$Serializer 1/1
  Patching net/minecraft/world/item/crafting/SmithingTrimRecipe 1/1
  Patching net/minecraft/world/item/enchantment/EnchantmentHelper$EnchantmentInSlotVisitor 1/1
  Patching net/minecraft/world/item/enchantment/EnchantmentHelper$EnchantmentVisitor 1/1
  Patching net/minecraft/world/item/enchantment/EnchantmentHelper 1/1
  Patching net/minecraft/world/item/enchantment/effects/ReplaceDisk 1/1
  Patching net/minecraft/world/level/BaseSpawner 1/1
  Patching net/minecraft/world/level/BlockAndTintGetter 1/1
  Patching net/minecraft/world/level/BlockGetter 1/1
  Patching net/minecraft/world/level/DataPackConfig 1/1
  Patching net/minecraft/world/level/Explosion$BlockInteraction 1/1
  Patching net/minecraft/world/level/Explosion 1/1
  Patching net/minecraft/world/level/ExplosionDamageCalculator 1/1
  Patching net/minecraft/world/level/ForcedChunksSavedData 1/1
  Patching net/minecraft/world/level/Level$1 1/1
  Patching net/minecraft/world/level/Level$ExplosionInteraction 1/1
  Patching net/minecraft/world/level/Level 1/1
  Patching net/minecraft/world/level/LevelReader 1/1
  Patching net/minecraft/world/level/LevelSettings 1/1
  Patching net/minecraft/world/level/NaturalSpawner$AfterSpawnCallback 1/1
  Patching net/minecraft/world/level/NaturalSpawner$ChunkGetter 1/1
  Patching net/minecraft/world/level/NaturalSpawner$SpawnPredicate 1/1
  Patching net/minecraft/world/level/NaturalSpawner$SpawnState 1/1
  Patching net/minecraft/world/level/NaturalSpawner 1/1
  Patching net/minecraft/world/level/SignalGetter 1/1
  Patching net/minecraft/world/level/biome/Biome$1 1/1
  Patching net/minecraft/world/level/biome/Biome$BiomeBuilder 1/1
  Patching net/minecraft/world/level/biome/Biome$ClimateSettings 1/1
  Patching net/minecraft/world/level/biome/Biome$Precipitation 1/1
  Patching net/minecraft/world/level/biome/Biome$TemperatureModifier$1 1/1
  Patching net/minecraft/world/level/biome/Biome$TemperatureModifier$2 1/1
  Patching net/minecraft/world/level/biome/Biome$TemperatureModifier 1/1
  Patching net/minecraft/world/level/biome/Biome 1/1
  Patching net/minecraft/world/level/biome/BiomeGenerationSettings$Builder 1/1
  Patching net/minecraft/world/level/biome/BiomeGenerationSettings$PlainBuilder 1/1
  Patching net/minecraft/world/level/biome/BiomeGenerationSettings 1/1
  Patching net/minecraft/world/level/biome/BiomeSpecialEffects$Builder 1/1
  Patching net/minecraft/world/level/biome/BiomeSpecialEffects$GrassColorModifier$1 1/1
  Patching net/minecraft/world/level/biome/BiomeSpecialEffects$GrassColorModifier$2 1/1
  Patching net/minecraft/world/level/biome/BiomeSpecialEffects$GrassColorModifier$3 1/1
  Patching net/minecraft/world/level/biome/BiomeSpecialEffects$GrassColorModifier 1/1
  Patching net/minecraft/world/level/biome/BiomeSpecialEffects 1/1
  Patching net/minecraft/world/level/biome/MobSpawnSettings$Builder 1/1
  Patching net/minecraft/world/level/biome/MobSpawnSettings$MobSpawnCost 1/1
  Patching net/minecraft/world/level/biome/MobSpawnSettings$SpawnerData 1/1
  Patching net/minecraft/world/level/biome/MobSpawnSettings 1/1
  Patching net/minecraft/world/level/block/BambooSaplingBlock 1/1
  Patching net/minecraft/world/level/block/BambooStalkBlock 1/1
  Patching net/minecraft/world/level/block/BaseFireBlock 1/1
  Patching net/minecraft/world/level/block/BaseRailBlock$1 1/1
  Patching net/minecraft/world/level/block/BaseRailBlock 1/1
  Patching net/minecraft/world/level/block/BeehiveBlock 1/1
  Patching net/minecraft/world/level/block/Block$1 1/1
  Patching net/minecraft/world/level/block/Block$2 1/1
  Patching net/minecraft/world/level/block/Block$BlockStatePairKey 1/1
  Patching net/minecraft/world/level/block/Block 1/1
  Patching net/minecraft/world/level/block/Blocks 1/1
  Patching net/minecraft/world/level/block/BucketPickup 1/1
  Patching net/minecraft/world/level/block/BushBlock 1/1
  Patching net/minecraft/world/level/block/CactusBlock 1/1
  Patching net/minecraft/world/level/block/CampfireBlock 1/1
  Patching net/minecraft/world/level/block/ChestBlock$1 1/1
  Patching net/minecraft/world/level/block/ChestBlock$2$1 1/1
  Patching net/minecraft/world/level/block/ChestBlock$2 1/1
  Patching net/minecraft/world/level/block/ChestBlock$3 1/1
  Patching net/minecraft/world/level/block/ChestBlock$4 1/1
  Patching net/minecraft/world/level/block/ChestBlock 1/1
  Patching net/minecraft/world/level/block/ChorusFlowerBlock 1/1
  Patching net/minecraft/world/level/block/CocoaBlock$1 1/1
  Patching net/minecraft/world/level/block/CocoaBlock 1/1
  Patching net/minecraft/world/level/block/ComparatorBlock 1/1
  Patching net/minecraft/world/level/block/ConcretePowderBlock 1/1
  Patching net/minecraft/world/level/block/CoralBlock 1/1
  Patching net/minecraft/world/level/block/CropBlock 1/1
  Patching net/minecraft/world/level/block/DeadBushBlock 1/1
  Patching net/minecraft/world/level/block/DetectorRailBlock$1 1/1
  Patching net/minecraft/world/level/block/DetectorRailBlock 1/1
  Patching net/minecraft/world/level/block/DiodeBlock 1/1
  Patching net/minecraft/world/level/block/DoublePlantBlock 1/1
  Patching net/minecraft/world/level/block/DropExperienceBlock 1/1
  Patching net/minecraft/world/level/block/DropperBlock 1/1
  Patching net/minecraft/world/level/block/EnchantingTableBlock 1/1
  Patching net/minecraft/world/level/block/FarmBlock 1/1
  Patching net/minecraft/world/level/block/FenceGateBlock$1 1/1
  Patching net/minecraft/world/level/block/FenceGateBlock 1/1
  Patching net/minecraft/world/level/block/FireBlock 1/1
  Patching net/minecraft/world/level/block/FlowerPotBlock 1/1
  Patching net/minecraft/world/level/block/FungusBlock 1/1
  Patching net/minecraft/world/level/block/GrowingPlantHeadBlock 1/1
  Patching net/minecraft/world/level/block/LeavesBlock 1/1
  Patching net/minecraft/world/level/block/LiquidBlock 1/1
  Patching net/minecraft/world/level/block/MushroomBlock 1/1
  Patching net/minecraft/world/level/block/NetherWartBlock 1/1
  Patching net/minecraft/world/level/block/NoteBlock 1/1
  Patching net/minecraft/world/level/block/PowderSnowBlock 1/1
  Patching net/minecraft/world/level/block/PoweredRailBlock$1 1/1
  Patching net/minecraft/world/level/block/PoweredRailBlock 1/1
  Patching net/minecraft/world/level/block/PumpkinBlock 1/1
  Patching net/minecraft/world/level/block/RailState$1 1/1
  Patching net/minecraft/world/level/block/RailState 1/1
  Patching net/minecraft/world/level/block/RedStoneOreBlock 1/1
  Patching net/minecraft/world/level/block/RedStoneWireBlock$1 1/1
  Patching net/minecraft/world/level/block/RedStoneWireBlock 1/1
  Patching net/minecraft/world/level/block/SaplingBlock 1/1
  Patching net/minecraft/world/level/block/SculkCatalystBlock 1/1
  Patching net/minecraft/world/level/block/SculkSensorBlock 1/1
  Patching net/minecraft/world/level/block/SculkShriekerBlock 1/1
  Patching net/minecraft/world/level/block/SeagrassBlock 1/1
  Patching net/minecraft/world/level/block/SoundType 1/1
  Patching net/minecraft/world/level/block/SpawnerBlock 1/1
  Patching net/minecraft/world/level/block/SpongeBlock 1/1
  Patching net/minecraft/world/level/block/SpreadingSnowyDirtBlock 1/1
  Patching net/minecraft/world/level/block/StemBlock 1/1
  Patching net/minecraft/world/level/block/SugarCaneBlock 1/1
  Patching net/minecraft/world/level/block/SweetBerryBushBlock 1/1
  Patching net/minecraft/world/level/block/TallGrassBlock 1/1
  Patching net/minecraft/world/level/block/TntBlock 1/1
  Patching net/minecraft/world/level/block/TrapDoorBlock$1 1/1
  Patching net/minecraft/world/level/block/TrapDoorBlock 1/1
  Patching net/minecraft/world/level/block/TripWireBlock$1 1/1
  Patching net/minecraft/world/level/block/TripWireBlock 1/1
  Patching net/minecraft/world/level/block/TripWireHookBlock$1 1/1
  Patching net/minecraft/world/level/block/TripWireHookBlock 1/1
  Patching net/minecraft/world/level/block/TurtleEggBlock 1/1
  Patching net/minecraft/world/level/block/VineBlock$1 1/1
  Patching net/minecraft/world/level/block/VineBlock 1/1
  Patching net/minecraft/world/level/block/WebBlock 1/1
  Patching net/minecraft/world/level/block/entity/AbstractFurnaceBlockEntity$1 1/1
  Patching net/minecraft/world/level/block/entity/AbstractFurnaceBlockEntity 1/1
  Patching net/minecraft/world/level/block/entity/BaseContainerBlockEntity 1/1
  Patching net/minecraft/world/level/block/entity/BeaconBlockEntity$1 1/1
  Patching net/minecraft/world/level/block/entity/BeaconBlockEntity$BeaconBeamSection 1/1
  Patching net/minecraft/world/level/block/entity/BeaconBlockEntity 1/1
  Patching net/minecraft/world/level/block/entity/BlockEntity$1 1/1
  Patching net/minecraft/world/level/block/entity/BlockEntity$ComponentHelper 1/1
  Patching net/minecraft/world/level/block/entity/BlockEntity$DataComponentInput 1/1
  Patching net/minecraft/world/level/block/entity/BlockEntity 1/1
  Patching net/minecraft/world/level/block/entity/BrewingStandBlockEntity$1 1/1
  Patching net/minecraft/world/level/block/entity/BrewingStandBlockEntity 1/1
  Patching net/minecraft/world/level/block/entity/ChestBlockEntity$1 1/1
  Patching net/minecraft/world/level/block/entity/ChestBlockEntity 1/1
  Patching net/minecraft/world/level/block/entity/ChiseledBookShelfBlockEntity 1/1
  Patching net/minecraft/world/level/block/entity/ConduitBlockEntity 1/1
  Patching net/minecraft/world/level/block/entity/HangingSignBlockEntity 1/1
  Patching net/minecraft/world/level/block/entity/HopperBlockEntity 1/1
  Patching net/minecraft/world/level/block/entity/ShulkerBoxBlockEntity$AnimationStatus 1/1
  Patching net/minecraft/world/level/block/entity/ShulkerBoxBlockEntity 1/1
  Patching net/minecraft/world/level/block/entity/SignBlockEntity 1/1
  Patching net/minecraft/world/level/block/entity/SpawnerBlockEntity$1 1/1
  Patching net/minecraft/world/level/block/entity/SpawnerBlockEntity 1/1
  Patching net/minecraft/world/level/block/grower/TreeGrower 1/1
  Patching net/minecraft/world/level/block/piston/PistonBaseBlock$1 1/1
  Patching net/minecraft/world/level/block/piston/PistonBaseBlock 1/1
  Patching net/minecraft/world/level/block/piston/PistonMovingBlockEntity$1 1/1
  Patching net/minecraft/world/level/block/piston/PistonMovingBlockEntity 1/1
  Patching net/minecraft/world/level/block/piston/PistonStructureResolver 1/1
  Patching net/minecraft/world/level/block/state/BlockBehaviour$1 1/1
  Patching net/minecraft/world/level/block/state/BlockBehaviour$BlockStateBase$Cache 1/1
  Patching net/minecraft/world/level/block/state/BlockBehaviour$BlockStateBase 1/1
  Patching net/minecraft/world/level/block/state/BlockBehaviour$OffsetFunction 1/1
  Patching net/minecraft/world/level/block/state/BlockBehaviour$OffsetType 1/1
  Patching net/minecraft/world/level/block/state/BlockBehaviour$Properties 1/1
  Patching net/minecraft/world/level/block/state/BlockBehaviour$StateArgumentPredicate 1/1
  Patching net/minecraft/world/level/block/state/BlockBehaviour$StatePredicate 1/1
  Patching net/minecraft/world/level/block/state/BlockBehaviour 1/1
  Patching net/minecraft/world/level/block/state/BlockState 1/1
  Patching net/minecraft/world/level/chunk/ChunkAccess$TicksToSave 1/1
  Patching net/minecraft/world/level/chunk/ChunkAccess 1/1
  Patching net/minecraft/world/level/chunk/ChunkGenerator 1/1
  Patching net/minecraft/world/level/chunk/ImposterProtoChunk 1/1
  Patching net/minecraft/world/level/chunk/LevelChunk$1 1/1
  Patching net/minecraft/world/level/chunk/LevelChunk$BoundTickingBlockEntity 1/1
  Patching net/minecraft/world/level/chunk/LevelChunk$EntityCreationType 1/1
  Patching net/minecraft/world/level/chunk/LevelChunk$PostLoadProcessor 1/1
  Patching net/minecraft/world/level/chunk/LevelChunk$RebindableTickingBlockEntityWrapper 1/1
  Patching net/minecraft/world/level/chunk/LevelChunk 1/1
  Patching net/minecraft/world/level/chunk/status/ChunkStatusTasks 1/1
  Patching net/minecraft/world/level/chunk/storage/ChunkSerializer$ChunkReadException 1/1
  Patching net/minecraft/world/level/chunk/storage/ChunkSerializer 1/1
  Patching net/minecraft/world/level/chunk/storage/EntityStorage 1/1
  Patching net/minecraft/world/level/dimension/end/EndDragonFight$Data 1/1
  Patching net/minecraft/world/level/dimension/end/EndDragonFight 1/1
  Patching net/minecraft/world/level/entity/PersistentEntitySectionManager$Callback 1/1
  Patching net/minecraft/world/level/entity/PersistentEntitySectionManager$ChunkLoadStatus 1/1
  Patching net/minecraft/world/level/entity/PersistentEntitySectionManager 1/1
  Patching net/minecraft/world/level/entity/TransientEntitySectionManager$Callback 1/1
  Patching net/minecraft/world/level/entity/TransientEntitySectionManager 1/1
  Patching net/minecraft/world/level/levelgen/Beardifier$1 1/1
  Patching net/minecraft/world/level/levelgen/Beardifier$Rigid 1/1
  Patching net/minecraft/world/level/levelgen/Beardifier 1/1
  Patching net/minecraft/world/level/levelgen/DebugLevelSource 1/1
  Patching net/minecraft/world/level/levelgen/PhantomSpawner 1/1
  Patching net/minecraft/world/level/levelgen/WorldDimensions$1Entry 1/1
  Patching net/minecraft/world/level/levelgen/WorldDimensions$Complete 1/1
  Patching net/minecraft/world/level/levelgen/WorldDimensions 1/1
  Patching net/minecraft/world/level/levelgen/feature/Feature 1/1
  Patching net/minecraft/world/level/levelgen/feature/MonsterRoomFeature 1/1
  Patching net/minecraft/world/level/levelgen/feature/treedecorators/AlterGroundDecorator 1/1
  Patching net/minecraft/world/level/levelgen/feature/trunkplacers/TrunkPlacer 1/1
  Patching net/minecraft/world/level/levelgen/structure/Structure$GenerationContext 1/1
  Patching net/minecraft/world/level/levelgen/structure/Structure$GenerationStub 1/1
  Patching net/minecraft/world/level/levelgen/structure/Structure$StructureSettings$Builder 1/1
  Patching net/minecraft/world/level/levelgen/structure/Structure$StructureSettings 1/1
  Patching net/minecraft/world/level/levelgen/structure/Structure 1/1
  Patching net/minecraft/world/level/levelgen/structure/StructurePiece$1 1/1
  Patching net/minecraft/world/level/levelgen/structure/StructurePiece$BlockSelector 1/1
  Patching net/minecraft/world/level/levelgen/structure/StructurePiece 1/1
  Patching net/minecraft/world/level/levelgen/structure/StructureStart 1/1
  Patching net/minecraft/world/level/levelgen/structure/templatesystem/StructureProcessor 1/1
  Patching net/minecraft/world/level/levelgen/structure/templatesystem/StructureTemplate$1 1/1
  Patching net/minecraft/world/level/levelgen/structure/templatesystem/StructureTemplate$Palette 1/1
  Patching net/minecraft/world/level/levelgen/structure/templatesystem/StructureTemplate$SimplePalette 1/1
  Patching net/minecraft/world/level/levelgen/structure/templatesystem/StructureTemplate$StructureBlockInfo 1/1
  Patching net/minecraft/world/level/levelgen/structure/templatesystem/StructureTemplate$StructureEntityInfo 1/1
  Patching net/minecraft/world/level/levelgen/structure/templatesystem/StructureTemplate 1/1
  Patching net/minecraft/world/level/lighting/BlockLightEngine 1/1
  Patching net/minecraft/world/level/lighting/LightEngine$QueueEntry 1/1
  Patching net/minecraft/world/level/lighting/LightEngine 1/1
  Patching net/minecraft/world/level/material/FlowingFluid$1 1/1
  Patching net/minecraft/world/level/material/FlowingFluid 1/1
  Patching net/minecraft/world/level/material/Fluid 1/1
  Patching net/minecraft/world/level/material/FluidState 1/1
  Patching net/minecraft/world/level/material/LavaFluid$Flowing 1/1
  Patching net/minecraft/world/level/material/LavaFluid$Source 1/1
  Patching net/minecraft/world/level/material/LavaFluid 1/1
  Patching net/minecraft/world/level/pathfinder/PathType 1/1
  Patching net/minecraft/world/level/pathfinder/WalkNodeEvaluator$1 1/1
  Patching net/minecraft/world/level/pathfinder/WalkNodeEvaluator 1/1
  Patching net/minecraft/world/level/portal/PortalShape 1/1
  Patching net/minecraft/world/level/storage/DimensionDataStorage 1/1
  Patching net/minecraft/world/level/storage/LevelStorageSource$LevelCandidates 1/1
  Patching net/minecraft/world/level/storage/LevelStorageSource$LevelDirectory 1/1
  Patching net/minecraft/world/level/storage/LevelStorageSource$LevelStorageAccess$1 1/1
  Patching net/minecraft/world/level/storage/LevelStorageSource$LevelStorageAccess$2 1/1
  Patching net/minecraft/world/level/storage/LevelStorageSource$LevelStorageAccess 1/1
  Patching net/minecraft/world/level/storage/LevelStorageSource 1/1
  Patching net/minecraft/world/level/storage/LevelSummary$BackupStatus 1/1
  Patching net/minecraft/world/level/storage/LevelSummary$CorruptedLevelSummary 1/1
  Patching net/minecraft/world/level/storage/LevelSummary$SymlinkLevelSummary 1/1
  Patching net/minecraft/world/level/storage/LevelSummary 1/1
  Patching net/minecraft/world/level/storage/PlayerDataStorage 1/1
  Patching net/minecraft/world/level/storage/PrimaryLevelData$SpecialWorldProperty 1/1
  Patching net/minecraft/world/level/storage/PrimaryLevelData 1/1
  Patching net/minecraft/world/level/storage/loot/LootContext$Builder 1/1
  Patching net/minecraft/world/level/storage/loot/LootContext$EntityTarget 1/1
  Patching net/minecraft/world/level/storage/loot/LootContext$VisitedEntry 1/1
  Patching net/minecraft/world/level/storage/loot/LootContext 1/1
  Patching net/minecraft/world/level/storage/loot/LootDataType$Validator 1/1
  Patching net/minecraft/world/level/storage/loot/LootDataType 1/1
  Patching net/minecraft/world/level/storage/loot/LootParams$Builder 1/1
  Patching net/minecraft/world/level/storage/loot/LootParams$DynamicDrop 1/1
  Patching net/minecraft/world/level/storage/loot/LootParams 1/1
  Patching net/minecraft/world/level/storage/loot/LootPool$Builder 1/1
  Patching net/minecraft/world/level/storage/loot/LootPool 1/1
  Patching net/minecraft/world/level/storage/loot/LootTable$Builder 1/1
  Patching net/minecraft/world/level/storage/loot/LootTable 1/1
  Patching net/minecraft/world/level/storage/loot/functions/EnchantedCountIncreaseFunction$Builder 1/1
  Patching net/minecraft/world/level/storage/loot/functions/EnchantedCountIncreaseFunction 1/1
  Patching net/minecraft/world/level/storage/loot/functions/SetAttributesFunction$Builder 1/1
  Patching net/minecraft/world/level/storage/loot/functions/SetAttributesFunction$Modifier 1/1
  Patching net/minecraft/world/level/storage/loot/functions/SetAttributesFunction$ModifierBuilder 1/1
  Patching net/minecraft/world/level/storage/loot/functions/SetAttributesFunction 1/1
  Patching net/minecraft/world/level/storage/loot/functions/SmeltItemFunction 1/1
  Patching net/minecraft/world/level/storage/loot/parameters/LootContextParamSets 1/1
  Patching net/minecraft/world/level/storage/loot/predicates/LootItemRandomChanceWithEnchantedBonusCondition 1/1
  Patching net/minecraft/world/level/storage/loot/providers/nbt/ContextNbtProvider$1 1/1
  Patching net/minecraft/world/level/storage/loot/providers/nbt/ContextNbtProvider$2 1/1
  Patching net/minecraft/world/level/storage/loot/providers/nbt/ContextNbtProvider$Getter 1/1
  Patching net/minecraft/world/level/storage/loot/providers/nbt/ContextNbtProvider 1/1
  Patching net/minecraft/Util$3 1/1
  Patching net/minecraft/Util$4 1/1
  Patching net/minecraft/nbt/CompoundTag$3 1/1
  Patching net/minecraft/world/entity/projectile/AbstractArrow$1 1/1
  Patching net/minecraft/world/item/Items$1 1/1
  Patching net/minecraft/world/item/crafting/RecipeType$2 1/1
  Patching net/minecraft/world/level/biome/BiomeSpecialEffects$GrassColorModifier$ColorModifier 1/1
  Patching net/minecraft/world/level/block/entity/AbstractFurnaceBlockEntity$2 1/1
  Patching net/minecraft/world/level/block/entity/BrewingStandBlockEntity$2 1/1
