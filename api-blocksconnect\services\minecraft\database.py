"""
Database utilities for BlocksConnect Minecraft service
Provides PostgreSQL connection and common database operations
"""

import os
import psycopg2
import psycopg2.extras
from psycopg2.pool import ThreadedConnectionPool
from contextlib import contextmanager
import logging
import json
from typing import Dict, List, Optional, Any, Union
from datetime import datetime

logger = logging.getLogger(__name__)

class DatabaseManager:
    """Database manager with connection pooling and common operations"""

    def __init__(self):
        self.pool = None
        self._initialize_pool()

    def _initialize_pool(self):
        """Initialize the connection pool"""
        database_url = os.getenv('DATABASE_URL')
        if not database_url:
            raise ValueError("DATABASE_URL environment variable is required")

        try:
            # Parse the database URL
            # Format: postgresql://user:password@host:port/database
            self.pool = ThreadedConnectionPool(
                minconn=1,
                maxconn=20,
                dsn=database_url
            )
            logger.info("Database connection pool initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize database pool: {e}")
            raise

    @contextmanager
    def get_connection(self):
        """Get a database connection from the pool"""
        if not self.pool:
            raise RuntimeError("Database pool not initialized")

        conn = None
        try:
            conn = self.pool.getconn()
            yield conn
        except Exception as e:
            if conn:
                conn.rollback()
            logger.error(f"Database operation failed: {e}")
            raise
        finally:
            if conn:
                self.pool.putconn(conn)

    @contextmanager
    def get_cursor(self, dict_cursor=True):
        """Get a database cursor with automatic connection management"""
        with self.get_connection() as conn:
            cursor_factory = psycopg2.extras.RealDictCursor if dict_cursor else None
            cursor = conn.cursor(cursor_factory=cursor_factory)
            try:
                yield cursor
                conn.commit()
            except Exception as e:
                conn.rollback()
                raise
            finally:
                cursor.close()

    def execute_query(self, query: str, params: tuple = None, fetch_one: bool = False, fetch_all: bool = True) -> Optional[Union[Dict, List[Dict]]]:
        """Execute a query and return results"""
        with self.get_cursor() as cursor:
            cursor.execute(query, params)

            if fetch_one:
                return cursor.fetchone()
            elif fetch_all:
                return cursor.fetchall()
            else:
                return None

    def execute_insert(self, query: str, params: tuple = None) -> Optional[Dict]:
        """Execute an insert query and return the inserted row"""
        with self.get_cursor() as cursor:
            cursor.execute(query + " RETURNING *", params)
            return cursor.fetchone()

    def execute_update(self, query: str, params: tuple = None) -> int:
        """Execute an update query and return the number of affected rows"""
        with self.get_cursor() as cursor:
            cursor.execute(query, params)
            return cursor.rowcount

    def execute_delete(self, query: str, params: tuple = None) -> int:
        """Execute a delete query and return the number of affected rows"""
        with self.get_cursor() as cursor:
            cursor.execute(query, params)
            return cursor.rowcount

# Global database manager instance
db_manager = DatabaseManager()

class UserManager:
    """User management operations"""

    @staticmethod
    def get_or_create_user(firebase_uid: str, email: str, display_name: str = None) -> Dict:
        """Get existing user or create new one"""
        # Try to get existing user
        user = db_manager.execute_query(
            "SELECT * FROM users WHERE firebase_uid = %s",
            (firebase_uid,),
            fetch_one=True
        )

        if user:
            # Update user info if needed
            if user['email'] != email or user['display_name'] != display_name:
                db_manager.execute_update(
                    "UPDATE users SET email = %s, display_name = %s WHERE firebase_uid = %s",
                    (email, display_name, firebase_uid)
                )
                # Fetch updated user
                user = db_manager.execute_query(
                    "SELECT * FROM users WHERE firebase_uid = %s",
                    (firebase_uid,),
                    fetch_one=True
                )
            return user

        # Create new user
        return db_manager.execute_insert(
            "INSERT INTO users (firebase_uid, email, display_name) VALUES (%s, %s, %s)",
            (firebase_uid, email, display_name)
        )

    @staticmethod
    def get_user_by_firebase_uid(firebase_uid: str) -> Optional[Dict]:
        """Get user by Firebase UID"""
        return db_manager.execute_query(
            "SELECT * FROM users WHERE firebase_uid = %s",
            (firebase_uid,),
            fetch_one=True
        )

class ServerManager:
    """Minecraft server management operations"""

    @staticmethod
    def get_user_servers(user_id: str) -> List[Dict]:
        """Get all servers owned by a user"""
        return db_manager.execute_query(
            """
            SELECT s.*, u.email as owner_email
            FROM minecraft_servers s
            JOIN users u ON s.owner_id = u.id
            WHERE s.owner_id = %s
            ORDER BY s.created_at DESC
            """,
            (user_id,)
        )

    @staticmethod
    def get_server_by_id(server_id: str, user_id: str = None) -> Optional[Dict]:
        """Get server by ID, optionally filtered by user"""
        if user_id:
            return db_manager.execute_query(
                "SELECT * FROM minecraft_servers WHERE id = %s AND owner_id = %s",
                (server_id, user_id),
                fetch_one=True
            )
        else:
            return db_manager.execute_query(
                "SELECT * FROM minecraft_servers WHERE id = %s",
                (server_id,),
                fetch_one=True
            )

    @staticmethod
    def create_server(owner_id: str, name: str, port: int, version: str,
                     server_type: str = 'vanilla', memory: str = '1G',
                     server_config: Dict = None) -> Dict:
        """Create a new Minecraft server"""
        config = server_config or {}

        # Ensure server_type is lowercase to match database constraint
        server_type = server_type.lower() if server_type else 'vanilla'

        return db_manager.execute_insert(
            """
            INSERT INTO minecraft_servers
            (owner_id, name, port, version, server_type, memory, server_config)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
            """,
            (owner_id, name, port, version, server_type, memory, json.dumps(config))
        )

    @staticmethod
    def update_server_status(server_id: str, status: str, container_id: str = None) -> int:
        """Update server status and container ID"""
        if container_id:
            return db_manager.execute_update(
                "UPDATE minecraft_servers SET status = %s, container_id = %s WHERE id = %s",
                (status, container_id, server_id)
            )
        else:
            return db_manager.execute_update(
                "UPDATE minecraft_servers SET status = %s WHERE id = %s",
                (status, server_id)
            )

    @staticmethod
    def delete_server(server_id: str, user_id: str) -> int:
        """Delete a server (only if owned by user)"""
        return db_manager.execute_delete(
            "DELETE FROM minecraft_servers WHERE id = %s AND owner_id = %s",
            (server_id, user_id)
        )

    @staticmethod
    def check_port_availability(port: int, user_id: str, exclude_server_id: str = None) -> bool:
        """Check if a port is available globally (not just for the user)"""
        # Check if port is already in use by any server globally
        query = "SELECT COUNT(*) as count FROM minecraft_servers WHERE port = %s"
        params = [port]

        if exclude_server_id:
            query += " AND id != %s"
            params.append(exclude_server_id)

        result = db_manager.execute_query(query, tuple(params), fetch_one=True)
        return result['count'] == 0

    @staticmethod
    def update_server_mod_status(server_id: str, has_mod: bool) -> int:
        """Update server mod integration status"""
        return db_manager.execute_update(
            "UPDATE minecraft_servers SET has_mod_integration = %s, mod_last_seen = CURRENT_TIMESTAMP WHERE id = %s",
            (has_mod, server_id)
        )

class LogManager:
    """Server logging operations"""

    @staticmethod
    def log_server_action(server_id: str, user_id: str, action: str, details: Dict = None):
        """Log a server action"""
        details = details or {}

        db_manager.execute_insert(
            "INSERT INTO server_logs (server_id, user_id, action, details) VALUES (%s, %s, %s, %s)",
            (server_id, user_id, action, json.dumps(details))
        )
