# Sets default memory used for gradle commands. Can be overridden by user or command line properties.
# This is required to provide enough memory for the Minecraft decompilation process.
org.gradle.jvmargs=-Xmx3G
org.gradle.daemon=false

# Mod Properties
mod_version=1.0.0
mod_group_id=com.blocksconnect
mod_id=blocksconnect
mod_name=BlocksConnect Integration
mod_license=MIT
mod_description=Integrates Minecraft servers with the BlocksConnect player management system
mod_authors=BlocksConnect Team
mod_credits=BlocksConnect Team

# Minecraft Properties
minecraft_version=1.21.1
minecraft_version_range=[1.21.1,1.22)
forge_version=52.0.17
forge_version_range=[52,)
loader_version_range=[52,)

# Mapping Properties
mapping_channel=official
mapping_version=1.21.1

# Dependency Properties
jei_version=**********
