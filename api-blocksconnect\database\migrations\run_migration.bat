@echo off
echo BlocksConnect Database Migration Runner
echo =====================================
echo.

REM Check if Python is available
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.7+ and try again
    echo.
    pause
    exit /b 1
)

echo Running migration script...
echo.

REM Run the Python migration script
python run_migration.py

echo.
echo Migration process completed.
pause
