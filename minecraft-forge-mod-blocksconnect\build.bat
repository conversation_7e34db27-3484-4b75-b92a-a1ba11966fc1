@echo off
echo Building BlocksConnect Forge Mod...
echo.

echo Cleaning previous builds...
call gradlew clean

echo.
echo Building mod...
call gradlew build

echo.
if exist "build\libs\blocksconnect-forge-mod-1.0.0.jar" (
    echo Build successful!
    echo Mod file: build\libs\blocksconnect-forge-mod-1.0.0.jar
) else (
    echo Build failed!
    exit /b 1
)

echo.
echo To install the mod:
echo 1. Copy build\libs\blocksconnect-forge-mod-1.0.0.jar to your server's mods folder
echo 2. Start your server to generate the configuration file
echo 3. Edit config\blocksconnect.json with your server details
echo 4. Restart your server
echo.
pause
