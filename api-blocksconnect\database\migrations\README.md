# Database Migration: Mod Integration Columns

This migration adds support for tracking mod/plugin integration status in the BlocksConnect system.

## What This Migration Does

Adds the following columns to the `minecraft_servers` table:
- `has_mod_integration` (BOOLEAN, default FALSE) - Tracks if a server has the BlocksConnect mod installed
- `mod_last_seen` (TIMESTAMP WITH TIME ZONE, nullable) - Records when the mod last communicated with the API

Also creates indexes for better query performance.

## Migration Methods

Choose the method that best fits your setup:

### Method 1: Python Script (Recommended)

**Requirements**: Python 3.7+, psycopg2

```bash
# Install dependencies if needed
pip install psycopg2-binary

# Run the migration
cd api-blocksconnect/database/migrations
python run_migration.py
```

**Windows users**:
```cmd
cd api-blocksconnect\database\migrations
run_migration.bat
```

The script will:
- ✅ Test database connection
- ✅ Check if tables exist
- ✅ Verify existing columns
- ✅ Run migration safely
- ✅ Verify results

### Method 2: Docker (For Docker Deployments)

**Requirements**: Docker, docker-compose

```bash
cd api-blocksconnect/database/migrations
chmod +x run_migration_docker.sh
./run_migration_docker.sh
```

This method:
- ✅ Starts PostgreSQL container if needed
- ✅ Runs migration inside Docker
- ✅ Verifies results

### Method 3: Direct SQL (Manual)

**Requirements**: PostgreSQL client (psql)

```bash
# Connect to your database
psql -h localhost -U blocksconnect -d blocksconnect

# Run the migration
\i api-blocksconnect/database/migrations/add_mod_integration_columns.sql
```

**Or using environment variables**:
```bash
# If you have DATABASE_URL set
psql $DATABASE_URL -f api-blocksconnect/database/migrations/add_mod_integration_columns.sql
```

### Method 4: pgAdmin or Database GUI

1. Open your PostgreSQL database in pgAdmin or your preferred GUI
2. Open the SQL query tool
3. Copy and paste the contents of `add_mod_integration_columns.sql`
4. Execute the script

## Verification

After running the migration, verify it worked:

```sql
-- Check if columns were added
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'minecraft_servers' 
AND column_name IN ('has_mod_integration', 'mod_last_seen')
ORDER BY column_name;

-- Check if indexes were created
SELECT indexname 
FROM pg_indexes 
WHERE tablename = 'minecraft_servers' 
AND indexname LIKE '%mod%';

-- Check sample data
SELECT id, name, has_mod_integration, mod_last_seen 
FROM minecraft_servers 
LIMIT 5;
```

Expected results:
- ✅ Two new columns: `has_mod_integration` and `mod_last_seen`
- ✅ Two new indexes: `idx_minecraft_servers_mod_integration` and `idx_minecraft_servers_mod_last_seen`
- ✅ All existing servers have `has_mod_integration = false`

## Troubleshooting

### Common Issues

#### 1. "relation 'minecraft_servers' does not exist"
**Solution**: The database hasn't been initialized yet.
```bash
# Initialize the database first
cd api-blocksconnect
docker-compose up -d postgres
# Wait for initialization, then run migration
```

#### 2. "permission denied for table minecraft_servers"
**Solution**: User doesn't have ALTER permissions.
```bash
# Connect as superuser and grant permissions
psql -h localhost -U postgres -d blocksconnect
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO blocksconnect;
```

#### 3. "column 'has_mod_integration' already exists"
**Solution**: Migration already ran. This is safe to ignore.

#### 4. Connection refused
**Solution**: Check database connection details.
```bash
# Test connection
psql -h localhost -U blocksconnect -d blocksconnect -c "SELECT version();"
```

### Environment Variables

The Python script looks for these environment variables:
- `DATABASE_URL` - Full PostgreSQL connection string
- `POSTGRES_USER` - Database username (default: blocksconnect)
- `POSTGRES_PASSWORD` - Database password
- `POSTGRES_HOST` - Database host (default: localhost)
- `POSTGRES_PORT` - Database port (default: 5432)
- `POSTGRES_DB` - Database name (default: blocksconnect)

### Manual Rollback (if needed)

If you need to undo this migration:

```sql
-- Remove the columns (THIS WILL DELETE DATA!)
ALTER TABLE minecraft_servers DROP COLUMN IF EXISTS has_mod_integration;
ALTER TABLE minecraft_servers DROP COLUMN IF EXISTS mod_last_seen;

-- Remove the indexes
DROP INDEX IF EXISTS idx_minecraft_servers_mod_integration;
DROP INDEX IF EXISTS idx_minecraft_servers_mod_last_seen;
```

⚠️ **Warning**: Rolling back will permanently delete any mod integration data!

## What Happens After Migration

Once the migration is complete:

1. **API Changes**: The API will start tracking mod integration status
2. **Web Panel**: Server listings will show mod integration status
3. **Enhanced Features**: Servers with mods will get enhanced statistics
4. **Automatic Detection**: When a mod connects, `has_mod_integration` becomes `true`

## Support

If you encounter issues:

1. **Check the logs**: Look for error messages in the migration output
2. **Verify prerequisites**: Ensure PostgreSQL is running and accessible
3. **Test connection**: Use `psql` to manually connect to the database
4. **Check permissions**: Ensure the database user has ALTER privileges
5. **Review the SQL**: The migration script is safe to run multiple times

For additional help, check the main project documentation or create an issue in the repository.
