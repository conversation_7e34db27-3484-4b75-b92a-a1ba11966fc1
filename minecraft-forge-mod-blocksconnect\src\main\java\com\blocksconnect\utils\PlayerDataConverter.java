package com.blocksconnect.utils;

import com.blocksconnect.api.models.PlayerData;
import net.minecraft.server.level.ServerPlayer;
import net.minecraft.world.level.GameType;
import net.minecraft.world.phys.Vec3;

/**
 * Utility class for converting Minecraft player data to BlocksConnect API models
 * Adapted for Forge's Minecraft mappings and classes
 */
public class PlayerDataConverter {
    
    /**
     * Convert ServerPlayer to PlayerData
     */
    public static PlayerData fromServerPlayer(ServerPlayer player) {
        PlayerData playerData = new PlayerData();
        
        // Basic player information
        playerData.setUuid(player.getStringUUID());
        playerData.setUsername(player.getName().getString());
        playerData.setDisplayName(player.getDisplayName().getString());
        
        // Network information
        if (player.connection != null && player.connection.getConnection() != null) {
            String address = player.connection.getConnection().getRemoteAddress().toString();
            // Extract IP address from the address string (format: /IP:PORT)
            if (address.startsWith("/")) {
                address = address.substring(1);
                int colonIndex = address.indexOf(':');
                if (colonIndex > 0) {
                    address = address.substring(0, colonIndex);
                }
            }
            playerData.setIpAddress(address);
        }
        
        // Game state
        playerData.setOnline(true);
        playerData.setGameMode(getGameModeId(player.gameMode.getGameModeForPlayer()));
        
        // Player stats
        playerData.setHealth(player.getHealth());
        playerData.setFoodLevel(player.getFoodData().getFoodLevel());
        playerData.setExperienceLevel(player.experienceLevel);
        playerData.setExperienceProgress(player.experienceProgress);
        
        // Position and rotation
        Vec3 pos = player.position();
        playerData.setPositionX(pos.x);
        playerData.setPositionY(pos.y);
        playerData.setPositionZ(pos.z);
        playerData.setYaw(player.getYRot());
        playerData.setPitch(player.getXRot());
        
        // Dimension
        playerData.setDimension(player.level().dimension().location().toString());
        
        return playerData;
    }
    
    /**
     * Update existing PlayerData with current ServerPlayer data
     */
    public static void updateFromServerPlayer(PlayerData playerData, ServerPlayer player) {
        // Update dynamic data that can change during gameplay
        playerData.setDisplayName(player.getDisplayName().getString());
        playerData.setGameMode(getGameModeId(player.gameMode.getGameModeForPlayer()));
        playerData.setHealth(player.getHealth());
        playerData.setFoodLevel(player.getFoodData().getFoodLevel());
        playerData.setExperienceLevel(player.experienceLevel);
        playerData.setExperienceProgress(player.experienceProgress);
        
        // Position and rotation
        Vec3 pos = player.position();
        playerData.setPositionX(pos.x);
        playerData.setPositionY(pos.y);
        playerData.setPositionZ(pos.z);
        playerData.setYaw(player.getYRot());
        playerData.setPitch(player.getXRot());
        
        // Dimension
        playerData.setDimension(player.level().dimension().location().toString());
        
        // Update last seen timestamp
        playerData.setLastSeen(java.time.Instant.now());
    }
    
    /**
     * Convert GameType to integer ID
     */
    private static int getGameModeId(GameType gameType) {
        return switch (gameType) {
            case SURVIVAL -> 0;
            case CREATIVE -> 1;
            case ADVENTURE -> 2;
            case SPECTATOR -> 3;
        };
    }
    
    /**
     * Convert integer ID to GameType
     */
    public static GameType getGameTypeFromId(int id) {
        return switch (id) {
            case 0 -> GameType.SURVIVAL;
            case 1 -> GameType.CREATIVE;
            case 2 -> GameType.ADVENTURE;
            case 3 -> GameType.SPECTATOR;
            default -> GameType.SURVIVAL;
        };
    }
}
