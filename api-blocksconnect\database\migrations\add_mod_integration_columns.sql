-- Migration: Add mod integration tracking columns to minecraft_servers table
-- This allows tracking which servers have mod/plugin integration for enhanced statistics
-- Database: PostgreSQL

-- Check if columns already exist before adding them
DO $$
BEGIN
    -- Add has_mod_integration column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'minecraft_servers'
        AND column_name = 'has_mod_integration'
    ) THEN
        ALTER TABLE minecraft_servers
        ADD COLUMN has_mod_integration BOOLEAN DEFAULT FALSE;

        RAISE NOTICE 'Added has_mod_integration column to minecraft_servers table';
    ELSE
        RAISE NOTICE 'Column has_mod_integration already exists in minecraft_servers table';
    END IF;

    -- Add mod_last_seen column if it doesn't exist
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'minecraft_servers'
        AND column_name = 'mod_last_seen'
    ) THEN
        ALTER TABLE minecraft_servers
        ADD COLUMN mod_last_seen TIMESTAMP WITH TIME ZONE NULL;

        RAISE NOTICE 'Added mod_last_seen column to minecraft_servers table';
    ELSE
        RAISE NOTICE 'Column mod_last_seen already exists in minecraft_servers table';
    END IF;
END $$;

-- Add indexes for better performance (only if they don't exist)
CREATE INDEX IF NOT EXISTS idx_minecraft_servers_mod_integration
ON minecraft_servers(has_mod_integration);

CREATE INDEX IF NOT EXISTS idx_minecraft_servers_mod_last_seen
ON minecraft_servers(mod_last_seen);

-- Update existing servers to have default values
UPDATE minecraft_servers
SET has_mod_integration = FALSE
WHERE has_mod_integration IS NULL;

-- Add comments to document the purpose
COMMENT ON COLUMN minecraft_servers.has_mod_integration IS 'Indicates if server has BlocksConnect mod/plugin installed for enhanced statistics';
COMMENT ON COLUMN minecraft_servers.mod_last_seen IS 'Timestamp of last communication from mod/plugin';

-- Verify the migration
DO $$
BEGIN
    RAISE NOTICE 'Migration completed successfully!';
    RAISE NOTICE 'Added columns: has_mod_integration, mod_last_seen';
    RAISE NOTICE 'Added indexes: idx_minecraft_servers_mod_integration, idx_minecraft_servers_mod_last_seen';
END $$;
