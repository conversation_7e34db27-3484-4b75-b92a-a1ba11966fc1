{"version": 3, "sources": [], "sections": [{"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/BlocksConnect/web-panel-blocksconnect/src/lib/firebase.ts"], "sourcesContent": ["// Firebase configuration and initialization\r\nimport { initializeApp, getApps, FirebaseApp } from 'firebase/app';\r\nimport {\r\n  getAuth,\r\n  signInWithEmailAndPassword,\r\n  createUserWithEmailAndPassword,\r\n  signInWithPopup,\r\n  GoogleAuthProvider,\r\n  signOut,\r\n  onAuthStateChanged,\r\n  User,\r\n  updateProfile,\r\n  updateEmail,\r\n  updatePassword,\r\n  Auth\r\n} from 'firebase/auth';\r\n\r\n// Only initialize Firebase on the client side\r\nconst firebaseConfig = {\r\n  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,\r\n  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,\r\n  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,\r\n  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,\r\n  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID\r\n};\r\n\r\n// Initialize Firebase only in browser environment\r\nlet app: FirebaseApp | null = null;\r\nlet auth: Auth | null = null;\r\nlet googleProvider: GoogleAuthProvider | null = null;\r\n\r\n// Only initialize Firebase in browser environment\r\nif (typeof window !== 'undefined') {\r\n  try {\r\n    // Validate required config values\r\n    const requiredFields = ['apiKey', 'authDomain', 'projectId', 'appId'];\r\n    const missingFields = requiredFields.filter(field => !firebaseConfig[field as keyof typeof firebaseConfig]);\r\n\r\n    if (missingFields.length > 0) {\r\n      console.error('Missing Firebase configuration fields:', missingFields);\r\n      throw new Error(`Missing Firebase configuration: ${missingFields.join(', ')}`);\r\n    }\r\n\r\n    app = getApps().length === 0 ? initializeApp(firebaseConfig) : getApps()[0];\r\n    auth = getAuth(app);\r\n\r\n    // Configure Google Auth Provider\r\n    googleProvider = new GoogleAuthProvider();\r\n    googleProvider.setCustomParameters({\r\n      prompt: 'select_account'\r\n    });\r\n\r\n    console.log('Firebase initialized successfully');\r\n  } catch (error) {\r\n    console.error('Firebase initialization error:', error);\r\n    // Don't throw here to prevent app crash, but log the error\r\n  }\r\n}\r\n\r\n// Auth functions\r\nexport const signInWithEmail = async (email: string, password: string) => {\r\n  if (!auth) throw new Error('Auth is not initialized');\r\n\r\n  try {\r\n    console.log('Attempting email/password sign-in for:', email);\r\n    const result = await signInWithEmailAndPassword(auth, email, password);\r\n    console.log('Email sign-in successful:', result.user?.email);\r\n    return { user: result.user, error: null };\r\n  } catch (error: any) {\r\n    console.error('Email sign-in error:', error);\r\n    console.error('Error code:', error.code);\r\n\r\n    // Provide more specific error messages\r\n    let userFriendlyMessage = error.message;\r\n    if (error.code === 'auth/user-not-found') {\r\n      userFriendlyMessage = 'No account found with this email address.';\r\n    } else if (error.code === 'auth/wrong-password') {\r\n      userFriendlyMessage = 'Incorrect password. Please try again.';\r\n    } else if (error.code === 'auth/invalid-email') {\r\n      userFriendlyMessage = 'Invalid email address format.';\r\n    } else if (error.code === 'auth/too-many-requests') {\r\n      userFriendlyMessage = 'Too many failed attempts. Please try again later.';\r\n    }\r\n\r\n    return { user: null, error: userFriendlyMessage };\r\n  }\r\n};\r\n\r\nexport const signUpWithEmail = async (email: string, password: string, name: string) => {\r\n  if (!auth) throw new Error('Auth is not initialized');\r\n\r\n  try {\r\n    const result = await createUserWithEmailAndPassword(auth, email, password);\r\n    // Set the displayName after account creation\r\n    if (auth.currentUser && name) {\r\n      await updateProfile(auth.currentUser, { displayName: name });\r\n    }\r\n    return { user: result.user, error: null };\r\n  } catch (error: any) {\r\n    return { user: null, error: error.message };\r\n  }\r\n};\r\n\r\nexport const signInWithGoogle = async () => {\r\n  if (!auth || !googleProvider) throw new Error('Auth is not initialized');\r\n\r\n  try {\r\n    console.log('Attempting Google sign-in...');\r\n    const result = await signInWithPopup(auth, googleProvider);\r\n    console.log('Google sign-in successful:', result.user?.email);\r\n    return { user: result.user, error: null };\r\n  } catch (error: any) {\r\n    console.error('Google sign-in error:', error);\r\n    console.error('Error code:', error.code);\r\n    console.error('Error message:', error.message);\r\n\r\n    // Provide more specific error messages\r\n    let userFriendlyMessage = error.message;\r\n    if (error.code === 'auth/popup-blocked') {\r\n      userFriendlyMessage = 'Popup was blocked. Please allow popups for this site and try again.';\r\n    } else if (error.code === 'auth/popup-closed-by-user') {\r\n      userFriendlyMessage = 'Sign-in was cancelled. Please try again.';\r\n    } else if (error.code === 'auth/unauthorized-domain') {\r\n      userFriendlyMessage = 'This domain is not authorized for Google sign-in. Please contact support.';\r\n    }\r\n\r\n    return { user: null, error: userFriendlyMessage };\r\n  }\r\n};\r\n\r\nexport const logOut = async () => {\r\n  if (!auth) throw new Error('Auth is not initialized');\r\n\r\n  try {\r\n    await signOut(auth);\r\n    return { error: null };\r\n  } catch (error: any) {\r\n    return { error: error.message };\r\n  }\r\n};\r\n\r\nexport const getCurrentUser = (): Promise<User | null> => {\r\n  if (!auth) return Promise.resolve(null);\r\n\r\n  return new Promise((resolve) => {\r\n    const unsubscribe = onAuthStateChanged(auth!, (user) => {\r\n      unsubscribe();\r\n      resolve(user);\r\n    });\r\n  });\r\n};\r\n\r\nexport const getIdToken = async (forceRefresh = false): Promise<string | null> => {\r\n  if (!auth) return null;\r\n\r\n  try {\r\n    const user = auth.currentUser;\r\n    if (user) {\r\n      return await user.getIdToken(forceRefresh);\r\n    }\r\n    return null;\r\n  } catch (error) {\r\n    console.error('Error getting ID token:', error);\r\n    return null;\r\n  }\r\n};\r\n\r\n/**\r\n * Updates a user's profile with only the changed fields\r\n * @param updates Object containing the fields to update\r\n * @returns Promise that resolves when the update is complete\r\n */\r\nexport async function updateUserProfile(updates: {\r\n  displayName?: string;\r\n  email?: string;\r\n  password?: string;\r\n  photoURL?: string;\r\n}): Promise<void> {\r\n  if (!auth) throw new Error('Auth is not initialized');\r\n\r\n  const user = auth.currentUser;\r\n\r\n  if (!user) {\r\n    throw new Error(\"No user is signed in\");\r\n  }\r\n\r\n  const updatePromises: Promise<void>[] = [];\r\n\r\n  // Only update fields that are provided and different from current values\r\n  if (updates.displayName !== undefined && updates.displayName !== user.displayName) {\r\n    updatePromises.push(updateProfile(user, { displayName: updates.displayName }));\r\n  }\r\n\r\n  if (updates.photoURL !== undefined && updates.photoURL !== user.photoURL) {\r\n    updatePromises.push(updateProfile(user, { photoURL: updates.photoURL }));\r\n  }\r\n\r\n  if (updates.email !== undefined && updates.email !== user.email) {\r\n    updatePromises.push(updateEmail(user, updates.email));\r\n  }\r\n\r\n  if (updates.password !== undefined) {\r\n    updatePromises.push(updatePassword(user, updates.password));\r\n  }\r\n\r\n  // If no changes, return immediately\r\n  if (updatePromises.length === 0) {\r\n    return;\r\n  }\r\n\r\n  // Wait for all updates to complete\r\n  await Promise.all(updatePromises);\r\n\r\n  // Force token refresh to ensure the latest user data is available\r\n  await user.getIdToken(true);\r\n}\r\n\r\n// Auth state listener\r\nexport const onAuthStateChange = (callback: (user: User | null) => void) => {\r\n  if (!auth) {\r\n    // If auth is not initialized, immediately call the callback with null\r\n    callback(null);\r\n    // Return a no-op function as the unsubscribe function\r\n    return () => {};\r\n  }\r\n\r\n  return onAuthStateChanged(auth, callback);\r\n};\r\n\r\n// Export auth instance\r\nexport { auth };\r\nexport default app;"], "names": [], "mappings": "AAAA,4CAA4C;;;;;;;;;;;;;AAE5C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AAeA,8CAA8C;AAC9C,MAAM,iBAAiB;IACrB,MAAM;IACN,UAAU;IACV,SAAS;IACT,iBAAiB;IACjB,KAAK;AACP;AAEA,kDAAkD;AAClD,IAAI,MAA0B;AAC9B,IAAI,OAAoB;AACxB,IAAI,iBAA4C;AAEhD,kDAAkD;AAClD,uCAAmC;;AAyBnC;AAGO,MAAM,kBAAkB,OAAO,OAAe;IACnD,IAAI,CAAC,MAAM,MAAM,IAAI,MAAM;IAE3B,IAAI;QACF,QAAQ,GAAG,CAAC,0CAA0C;QACtD,MAAM,SAAS,MAAM,CAAA,GAAA,iOAAA,CAAA,6BAA0B,AAAD,EAAE,MAAM,OAAO;QAC7D,QAAQ,GAAG,CAAC,6BAA6B,OAAO,IAAI,EAAE;QACtD,OAAO;YAAE,MAAM,OAAO,IAAI;YAAE,OAAO;QAAK;IAC1C,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,wBAAwB;QACtC,QAAQ,KAAK,CAAC,eAAe,MAAM,IAAI;QAEvC,uCAAuC;QACvC,IAAI,sBAAsB,MAAM,OAAO;QACvC,IAAI,MAAM,IAAI,KAAK,uBAAuB;YACxC,sBAAsB;QACxB,OAAO,IAAI,MAAM,IAAI,KAAK,uBAAuB;YAC/C,sBAAsB;QACxB,OAAO,IAAI,MAAM,IAAI,KAAK,sBAAsB;YAC9C,sBAAsB;QACxB,OAAO,IAAI,MAAM,IAAI,KAAK,0BAA0B;YAClD,sBAAsB;QACxB;QAEA,OAAO;YAAE,MAAM;YAAM,OAAO;QAAoB;IAClD;AACF;AAEO,MAAM,kBAAkB,OAAO,OAAe,UAAkB;IACrE,IAAI,CAAC,MAAM,MAAM,IAAI,MAAM;IAE3B,IAAI;QACF,MAAM,SAAS,MAAM,CAAA,GAAA,qOAAA,CAAA,iCAA8B,AAAD,EAAE,MAAM,OAAO;QACjE,6CAA6C;QAC7C,IAAI,KAAK,WAAW,IAAI,MAAM;YAC5B,MAAM,CAAA,GAAA,oNAAA,CAAA,gBAAa,AAAD,EAAE,KAAK,WAAW,EAAE;gBAAE,aAAa;YAAK;QAC5D;QACA,OAAO;YAAE,MAAM,OAAO,IAAI;YAAE,OAAO;QAAK;IAC1C,EAAE,OAAO,OAAY;QACnB,OAAO;YAAE,MAAM;YAAM,OAAO,MAAM,OAAO;QAAC;IAC5C;AACF;AAEO,MAAM,mBAAmB;IAC9B,IAAI,CAAC,QAAQ,CAAC,gBAAgB,MAAM,IAAI,MAAM;IAE9C,IAAI;QACF,QAAQ,GAAG,CAAC;QACZ,MAAM,SAAS,MAAM,CAAA,GAAA,qNAAA,CAAA,kBAAe,AAAD,EAAE,MAAM;QAC3C,QAAQ,GAAG,CAAC,8BAA8B,OAAO,IAAI,EAAE;QACvD,OAAO;YAAE,MAAM,OAAO,IAAI;YAAE,OAAO;QAAK;IAC1C,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,yBAAyB;QACvC,QAAQ,KAAK,CAAC,eAAe,MAAM,IAAI;QACvC,QAAQ,KAAK,CAAC,kBAAkB,MAAM,OAAO;QAE7C,uCAAuC;QACvC,IAAI,sBAAsB,MAAM,OAAO;QACvC,IAAI,MAAM,IAAI,KAAK,sBAAsB;YACvC,sBAAsB;QACxB,OAAO,IAAI,MAAM,IAAI,KAAK,6BAA6B;YACrD,sBAAsB;QACxB,OAAO,IAAI,MAAM,IAAI,KAAK,4BAA4B;YACpD,sBAAsB;QACxB;QAEA,OAAO;YAAE,MAAM;YAAM,OAAO;QAAoB;IAClD;AACF;AAEO,MAAM,SAAS;IACpB,IAAI,CAAC,MAAM,MAAM,IAAI,MAAM;IAE3B,IAAI;QACF,MAAM,CAAA,GAAA,6MAAA,CAAA,UAAO,AAAD,EAAE;QACd,OAAO;YAAE,OAAO;QAAK;IACvB,EAAE,OAAO,OAAY;QACnB,OAAO;YAAE,OAAO,MAAM,OAAO;QAAC;IAChC;AACF;AAEO,MAAM,iBAAiB;IAC5B,IAAI,CAAC,MAAM,OAAO,QAAQ,OAAO,CAAC;IAElC,OAAO,IAAI,QAAQ,CAAC;QAClB,MAAM,cAAc,CAAA,GAAA,wNAAA,CAAA,qBAAkB,AAAD,EAAE,MAAO,CAAC;YAC7C;YACA,QAAQ;QACV;IACF;AACF;AAEO,MAAM,aAAa,OAAO,eAAe,KAAK;IACnD,IAAI,CAAC,MAAM,OAAO;IAElB,IAAI;QACF,MAAM,OAAO,KAAK,WAAW;QAC7B,IAAI,MAAM;YACR,OAAO,MAAM,KAAK,UAAU,CAAC;QAC/B;QACA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO;IACT;AACF;AAOO,eAAe,kBAAkB,OAKvC;IACC,IAAI,CAAC,MAAM,MAAM,IAAI,MAAM;IAE3B,MAAM,OAAO,KAAK,WAAW;IAE7B,IAAI,CAAC,MAAM;QACT,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,iBAAkC,EAAE;IAE1C,yEAAyE;IACzE,IAAI,QAAQ,WAAW,KAAK,aAAa,QAAQ,WAAW,KAAK,KAAK,WAAW,EAAE;QACjF,eAAe,IAAI,CAAC,CAAA,GAAA,oNAAA,CAAA,gBAAa,AAAD,EAAE,MAAM;YAAE,aAAa,QAAQ,WAAW;QAAC;IAC7E;IAEA,IAAI,QAAQ,QAAQ,KAAK,aAAa,QAAQ,QAAQ,KAAK,KAAK,QAAQ,EAAE;QACxE,eAAe,IAAI,CAAC,CAAA,GAAA,oNAAA,CAAA,gBAAa,AAAD,EAAE,MAAM;YAAE,UAAU,QAAQ,QAAQ;QAAC;IACvE;IAEA,IAAI,QAAQ,KAAK,KAAK,aAAa,QAAQ,KAAK,KAAK,KAAK,KAAK,EAAE;QAC/D,eAAe,IAAI,CAAC,CAAA,GAAA,kNAAA,CAAA,cAAW,AAAD,EAAE,MAAM,QAAQ,KAAK;IACrD;IAEA,IAAI,QAAQ,QAAQ,KAAK,WAAW;QAClC,eAAe,IAAI,CAAC,CAAA,GAAA,qNAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,QAAQ,QAAQ;IAC3D;IAEA,oCAAoC;IACpC,IAAI,eAAe,MAAM,KAAK,GAAG;QAC/B;IACF;IAEA,mCAAmC;IACnC,MAAM,QAAQ,GAAG,CAAC;IAElB,kEAAkE;IAClE,MAAM,KAAK,UAAU,CAAC;AACxB;AAGO,MAAM,oBAAoB,CAAC;IAChC,IAAI,CAAC,MAAM;QACT,sEAAsE;QACtE,SAAS;QACT,sDAAsD;QACtD,OAAO,KAAO;IAChB;IAEA,OAAO,CAAA,GAAA,wNAAA,CAAA,qBAAkB,AAAD,EAAE,MAAM;AAClC;;uCAIe", "debugId": null}}, {"offset": {"line": 219, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/BlocksConnect/web-panel-blocksconnect/src/lib/auth.ts"], "sourcesContent": ["'use client';\r\n\r\nimport { User } from 'firebase/auth';\r\n\r\n// Enhanced authentication utilities for admin panel\r\nexport interface AuthUser {\r\n  uid: string;\r\n  email: string;\r\n  displayName?: string;\r\n  isAdmin: boolean;\r\n  photoURL?: string;\r\n}\r\n\r\n// Preview access email whitelist - these emails are allowed to access the admin panel\r\nconst getPreviewAccessEmails = (): string[] => {\r\n  if (typeof window !== 'undefined') {\r\n    // Try new naming convention first, fallback to old for backward compatibility\r\n    const previewEmails = process.env.NEXT_PUBLIC_PREVIEW_ACCESS_EMAILS || process.env.NEXT_PUBLIC_ADMIN_EMAILS || '';\r\n    return previewEmails.split(',')\r\n      .map(email => email.trim())\r\n      .filter(email => email.length > 0 && isValidEmail(email));\r\n  }\r\n  return [];\r\n};\r\n\r\n// Validate email format\r\nconst isValidEmail = (email: string): boolean => {\r\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\r\n  return emailRegex.test(email);\r\n};\r\n\r\nexport const hasPreviewAccess = (email: string): boolean => {\r\n  if (!email || !isValidEmail(email)) {\r\n    return false;\r\n  }\r\n\r\n  const previewAccessEmails = getPreviewAccessEmails();\r\n  return previewAccessEmails.includes(email.toLowerCase());\r\n};\r\n\r\nexport const createAuthUser = (firebaseUser: User): AuthUser => {\r\n  return {\r\n    uid: firebaseUser.uid,\r\n    email: firebaseUser.email || '',\r\n    displayName: firebaseUser.displayName || undefined,\r\n    isAdmin: hasPreviewAccess(firebaseUser.email || ''),\r\n    photoURL: firebaseUser.photoURL || undefined,\r\n  };\r\n};\r\n\r\nexport const setAuthToken = async (token: string): Promise<void> => {\r\n  try {\r\n    // Set HTTP-only cookie via API call\r\n    await fetch('/api/auth/set-token', {\r\n      method: 'POST',\r\n      headers: {\r\n        'Content-Type': 'application/json',\r\n      },\r\n      body: JSON.stringify({ token }),\r\n    });\r\n  } catch (error) {\r\n    console.error('Failed to set auth token:', error);\r\n  }\r\n};\r\n\r\nexport const clearAuthToken = async (): Promise<void> => {\r\n  try {\r\n    // Clear HTTP-only cookie via API call\r\n    await fetch('/api/auth/clear-token', {\r\n      method: 'POST',\r\n    });\r\n  } catch (error) {\r\n    console.error('Failed to clear auth token:', error);\r\n  }\r\n};\r\n\r\nexport const redirectToMainSite = (): void => {\r\n  const mainDomain = process.env.NEXT_PUBLIC_MAIN_DOMAIN || 'blocksconnect.com';\r\n  window.location.href = `https://${mainDomain}`;\r\n};\r\n\r\nexport const logout = async (): Promise<void> => {\r\n  try {\r\n    // Clear server-side token\r\n    await clearAuthToken();\r\n\r\n    // Clear any client-side storage\r\n    if (typeof window !== 'undefined') {\r\n      localStorage.removeItem('auth_user');\r\n      sessionStorage.clear();\r\n    }\r\n  } catch (error) {\r\n    console.error('Logout error:', error);\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;;;;;;AAAA;AAaA,sFAAsF;AACtF,MAAM,yBAAyB;IAC7B,uCAAmC;;IAMnC;IACA,OAAO,EAAE;AACX;AAEA,wBAAwB;AACxB,MAAM,eAAe,CAAC;IACpB,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,MAAM,mBAAmB,CAAC;IAC/B,IAAI,CAAC,SAAS,CAAC,aAAa,QAAQ;QAClC,OAAO;IACT;IAEA,MAAM,sBAAsB;IAC5B,OAAO,oBAAoB,QAAQ,CAAC,MAAM,WAAW;AACvD;AAEO,MAAM,iBAAiB,CAAC;IAC7B,OAAO;QACL,KAAK,aAAa,GAAG;QACrB,OAAO,aAAa,KAAK,IAAI;QAC7B,aAAa,aAAa,WAAW,IAAI;QACzC,SAAS,iBAAiB,aAAa,KAAK,IAAI;QAChD,UAAU,aAAa,QAAQ,IAAI;IACrC;AACF;AAEO,MAAM,eAAe,OAAO;IACjC,IAAI;QACF,oCAAoC;QACpC,MAAM,MAAM,uBAAuB;YACjC,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE;YAAM;QAC/B;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6BAA6B;IAC7C;AACF;AAEO,MAAM,iBAAiB;IAC5B,IAAI;QACF,sCAAsC;QACtC,MAAM,MAAM,yBAAyB;YACnC,QAAQ;QACV;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;IAC/C;AACF;AAEO,MAAM,qBAAqB;IAChC,MAAM,aAAa,yDAAuC;IAC1D,OAAO,QAAQ,CAAC,IAAI,GAAG,CAAC,QAAQ,EAAE,YAAY;AAChD;AAEO,MAAM,SAAS;IACpB,IAAI;QACF,0BAA0B;QAC1B,MAAM;QAEN,gCAAgC;QAChC,uCAAmC;;QAGnC;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iBAAiB;IACjC;AACF", "debugId": null}}, {"offset": {"line": 304, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/BlocksConnect/web-panel-blocksconnect/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client';\n\nimport React, { createContext, useContext, useState, useEffect, useRef } from 'react';\nimport { User } from 'firebase/auth';\nimport {\n  onAuthStateChange,\n  logOut,\n  getIdToken,\n  signInWithEmail,\n  signInWithGoogle,\n  signUpWithEmail\n} from '../lib/firebase';\nimport {\n  createAuthUser,\n  hasPreviewAccess,\n  setAuthToken,\n  clearAuthToken,\n  redirectToMainSite,\n  AuthUser\n} from '../lib/auth';\n\ninterface AuthContextType {\n  user: User | null;\n  authUser: AuthUser | null;\n  isLoading: boolean;\n  isAdmin: boolean;\n  tokenSyncError: string | null;\n  signIn: (email: string, password: string) => Promise<{ user: User | null; error: string | null }>;\n  signUp: (email: string, password: string, name: string) => Promise<{ user: User | null; error: string | null }>;\n  signInWithGoogle: () => Promise<{ user: User | null; error: string | null }>;\n  logout: () => Promise<{ error: string | null }>;\n  getToken: (forceRefresh?: boolean) => Promise<string | null>;\n  refreshToken: () => Promise<string | null>;\n  recoverFromTokenError: () => Promise<boolean>;\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined);\n\nexport const useAuth = () => {\n  const context = useContext(AuthContext);\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider');\n  }\n  return context;\n};\n\nexport const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {\n  const [user, setUser] = useState<User | null>(null);\n  const [authUser, setAuthUser] = useState<AuthUser | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const [tokenSyncError, setTokenSyncError] = useState<string | null>(null);\n  const tokenRefreshTimeoutRef = useRef<NodeJS.Timeout | null>(null);\n  const tokenRetryCountRef = useRef(0);\n  const MAX_RETRY_COUNT = 3;\n  const TOKEN_REFRESH_INTERVAL = 15 * 60 * 1000; // 15 minutes\n\n  // Function to synchronize tokens - ensures both Firebase and server tokens are in sync\n  const synchronizeTokens = async (forceRefresh = true, retryCount = 0): Promise<string | null> => {\n    if (!user) {\n      return null;\n    }\n    \n    try {\n      // Get fresh Firebase ID token\n      const idToken = await getIdToken(forceRefresh);\n      \n      if (idToken) {\n        // Update server-side token\n        await setAuthToken(idToken);\n        setTokenSyncError(null);\n        tokenRetryCountRef.current = 0;\n        return idToken;\n      } else {\n        throw new Error('Failed to get ID token');\n      }\n    } catch (error) {\n      console.error('Token synchronization failed:', error);\n      setTokenSyncError(`Token sync error: ${error instanceof Error ? error.message : 'Unknown error'}`);\n      \n      // Implement retry logic if needed\n      if (retryCount < MAX_RETRY_COUNT) {\n        console.log(`Retrying token sync (${retryCount + 1}/${MAX_RETRY_COUNT})...`);\n        // Exponential backoff: wait longer between retries\n        const backoffTime = Math.pow(2, retryCount) * 1000;\n        await new Promise(resolve => setTimeout(resolve, backoffTime));\n        return synchronizeTokens(forceRefresh, retryCount + 1);\n      }\n      \n      tokenRetryCountRef.current = retryCount;\n      return null;\n    }\n  };\n\n  // Set up token refresh interval\n  useEffect(() => {\n    const setupTokenRefresh = async () => {\n      // Clear any existing refresh timeout\n      if (tokenRefreshTimeoutRef.current) {\n        clearTimeout(tokenRefreshTimeoutRef.current);\n        tokenRefreshTimeoutRef.current = null;\n      }\n\n      // Only set up refresh for authenticated users\n      if (user) {\n        tokenRefreshTimeoutRef.current = setTimeout(async () => {\n          console.log('Performing scheduled token refresh');\n          await synchronizeTokens(true);\n          // Set up next refresh\n          setupTokenRefresh();\n        }, TOKEN_REFRESH_INTERVAL);\n      }\n    };\n\n    setupTokenRefresh();\n\n    // Cleanup on unmount\n    return () => {\n      if (tokenRefreshTimeoutRef.current) {\n        clearTimeout(tokenRefreshTimeoutRef.current);\n      }\n    };\n  }, [user]);\n\n  useEffect(() => {\n    // Listen for authentication state changes\n    const unsubscribe = onAuthStateChange(async (firebaseUser) => {\n      setUser(firebaseUser);\n\n      if (firebaseUser) {\n        // Check if user has preview access\n        if (!hasPreviewAccess(firebaseUser.email || '')) {\n          // User is not authorized, redirect to main site\n          console.warn('Unauthorized preview access attempt:', firebaseUser.email);\n          await logOut();\n          redirectToMainSite();\n          return;\n        }\n\n        // Create auth user and set server-side token\n        const newAuthUser = createAuthUser(firebaseUser);\n        setAuthUser(newAuthUser);\n\n        try {\n          // Synchronize Firebase and server-side tokens\n          await synchronizeTokens(true);\n        } catch (error) {\n          console.error('Failed to set auth token during auth state change:', error);\n        }\n      } else {\n        // User logged out\n        setAuthUser(null);\n        await clearAuthToken();\n        \n        // Clear any token refresh timeout\n        if (tokenRefreshTimeoutRef.current) {\n          clearTimeout(tokenRefreshTimeoutRef.current);\n          tokenRefreshTimeoutRef.current = null;\n        }\n      }\n\n      setIsLoading(false);\n    });\n\n    return () => unsubscribe();\n  }, []);\n\n  const signIn = async (email: string, password: string) => {\n    setIsLoading(true);\n    try {\n      const result = await signInWithEmail(email, password);\n      return result;\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const signUp = async (email: string, password: string, name: string) => {\n    setIsLoading(true);\n    try {\n      const result = await signUpWithEmail(email, password, name);\n      return result;\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const signInWithGoogleProvider = async () => {\n    setIsLoading(true);\n    try {\n      const result = await signInWithGoogle();\n      return result;\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const logout = async () => {\n    setIsLoading(true);\n    try {\n      // Clear server-side token first\n      await clearAuthToken();\n\n      // Then logout from Firebase\n      const result = await logOut();\n\n      // Clear local state\n      setAuthUser(null);\n\n      return result;\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const getToken = async (forceRefresh = false) => {\n    // Prefer synchronized tokens to ensure consistency\n    try {\n      // If forcing refresh, use synchronizeTokens to ensure both Firebase and server tokens are updated\n      if (forceRefresh) {\n        return await synchronizeTokens(true);\n      }\n      \n      // For non-forced refreshes, we can use the cached token if available\n      return await getIdToken(false);\n    } catch (error) {\n      console.error('Error getting token:', error);\n      return null;\n    }\n  };\n\n  const refreshToken = async () => {\n    // Always use synchronizeTokens for refreshes to ensure both tokens are updated\n    return await synchronizeTokens(true);\n  };\n  \n  // This function can be called to recover from token sync errors\n  const recoverFromTokenError = async (): Promise<boolean> => {\n    if (tokenSyncError && user) {\n      try {\n        const token = await synchronizeTokens(true);\n        return !!token;\n      } catch (error) {\n        console.error('Failed to recover from token error:', error);\n        return false;\n      }\n    }\n    return false;\n  };\n\n  const value = {\n    user,\n    authUser,\n    isLoading,\n    isAdmin: authUser?.isAdmin || false,\n    signIn,\n    signUp,\n    signInWithGoogle: signInWithGoogleProvider,\n    logout,\n    getToken,\n    refreshToken,\n    tokenSyncError,\n    recoverFromTokenError\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  );\n};\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;AAQA;AAZA;;;;;AAoCA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,MAAM,UAAU;IACrB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAEO,MAAM,eAAwD,CAAC,EAAE,QAAQ,EAAE;IAChF,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,yBAAyB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAyB;IAC7D,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAE;IAClC,MAAM,kBAAkB;IACxB,MAAM,yBAAyB,KAAK,KAAK,MAAM,aAAa;IAE5D,uFAAuF;IACvF,MAAM,oBAAoB,OAAO,eAAe,IAAI,EAAE,aAAa,CAAC;QAClE,IAAI,CAAC,MAAM;YACT,OAAO;QACT;QAEA,IAAI;YACF,8BAA8B;YAC9B,MAAM,UAAU,MAAM,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE;YAEjC,IAAI,SAAS;gBACX,2BAA2B;gBAC3B,MAAM,CAAA,GAAA,kHAAA,CAAA,eAAY,AAAD,EAAE;gBACnB,kBAAkB;gBAClB,mBAAmB,OAAO,GAAG;gBAC7B,OAAO;YACT,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,kBAAkB,CAAC,kBAAkB,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,iBAAiB;YAEjG,kCAAkC;YAClC,IAAI,aAAa,iBAAiB;gBAChC,QAAQ,GAAG,CAAC,CAAC,qBAAqB,EAAE,aAAa,EAAE,CAAC,EAAE,gBAAgB,IAAI,CAAC;gBAC3E,mDAAmD;gBACnD,MAAM,cAAc,KAAK,GAAG,CAAC,GAAG,cAAc;gBAC9C,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBACjD,OAAO,kBAAkB,cAAc,aAAa;YACtD;YAEA,mBAAmB,OAAO,GAAG;YAC7B,OAAO;QACT;IACF;IAEA,gCAAgC;IAChC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,oBAAoB;YACxB,qCAAqC;YACrC,IAAI,uBAAuB,OAAO,EAAE;gBAClC,aAAa,uBAAuB,OAAO;gBAC3C,uBAAuB,OAAO,GAAG;YACnC;YAEA,8CAA8C;YAC9C,IAAI,MAAM;gBACR,uBAAuB,OAAO,GAAG,WAAW;oBAC1C,QAAQ,GAAG,CAAC;oBACZ,MAAM,kBAAkB;oBACxB,sBAAsB;oBACtB;gBACF,GAAG;YACL;QACF;QAEA;QAEA,qBAAqB;QACrB,OAAO;YACL,IAAI,uBAAuB,OAAO,EAAE;gBAClC,aAAa,uBAAuB,OAAO;YAC7C;QACF;IACF,GAAG;QAAC;KAAK;IAET,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,0CAA0C;QAC1C,MAAM,cAAc,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,OAAO;YAC3C,QAAQ;YAER,IAAI,cAAc;gBAChB,mCAAmC;gBACnC,IAAI,CAAC,CAAA,GAAA,kHAAA,CAAA,mBAAgB,AAAD,EAAE,aAAa,KAAK,IAAI,KAAK;oBAC/C,gDAAgD;oBAChD,QAAQ,IAAI,CAAC,wCAAwC,aAAa,KAAK;oBACvE,MAAM,CAAA,GAAA,sHAAA,CAAA,SAAM,AAAD;oBACX,CAAA,GAAA,kHAAA,CAAA,qBAAkB,AAAD;oBACjB;gBACF;gBAEA,6CAA6C;gBAC7C,MAAM,cAAc,CAAA,GAAA,kHAAA,CAAA,iBAAc,AAAD,EAAE;gBACnC,YAAY;gBAEZ,IAAI;oBACF,8CAA8C;oBAC9C,MAAM,kBAAkB;gBAC1B,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,sDAAsD;gBACtE;YACF,OAAO;gBACL,kBAAkB;gBAClB,YAAY;gBACZ,MAAM,CAAA,GAAA,kHAAA,CAAA,iBAAc,AAAD;gBAEnB,kCAAkC;gBAClC,IAAI,uBAAuB,OAAO,EAAE;oBAClC,aAAa,uBAAuB,OAAO;oBAC3C,uBAAuB,OAAO,GAAG;gBACnC;YACF;YAEA,aAAa;QACf;QAEA,OAAO,IAAM;IACf,GAAG,EAAE;IAEL,MAAM,SAAS,OAAO,OAAe;QACnC,aAAa;QACb,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,sHAAA,CAAA,kBAAe,AAAD,EAAE,OAAO;YAC5C,OAAO;QACT,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,SAAS,OAAO,OAAe,UAAkB;QACrD,aAAa;QACb,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,sHAAA,CAAA,kBAAe,AAAD,EAAE,OAAO,UAAU;YACtD,OAAO;QACT,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,2BAA2B;QAC/B,aAAa;QACb,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,sHAAA,CAAA,mBAAgB,AAAD;YACpC,OAAO;QACT,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,SAAS;QACb,aAAa;QACb,IAAI;YACF,gCAAgC;YAChC,MAAM,CAAA,GAAA,kHAAA,CAAA,iBAAc,AAAD;YAEnB,4BAA4B;YAC5B,MAAM,SAAS,MAAM,CAAA,GAAA,sHAAA,CAAA,SAAM,AAAD;YAE1B,oBAAoB;YACpB,YAAY;YAEZ,OAAO;QACT,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,WAAW,OAAO,eAAe,KAAK;QAC1C,mDAAmD;QACnD,IAAI;YACF,kGAAkG;YAClG,IAAI,cAAc;gBAChB,OAAO,MAAM,kBAAkB;YACjC;YAEA,qEAAqE;YACrE,OAAO,MAAM,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,OAAO;QACT;IACF;IAEA,MAAM,eAAe;QACnB,+EAA+E;QAC/E,OAAO,MAAM,kBAAkB;IACjC;IAEA,gEAAgE;IAChE,MAAM,wBAAwB;QAC5B,IAAI,kBAAkB,MAAM;YAC1B,IAAI;gBACF,MAAM,QAAQ,MAAM,kBAAkB;gBACtC,OAAO,CAAC,CAAC;YACX,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,uCAAuC;gBACrD,OAAO;YACT;QACF;QACA,OAAO;IACT;IAEA,MAAM,QAAQ;QACZ;QACA;QACA;QACA,SAAS,UAAU,WAAW;QAC9B;QACA;QACA,kBAAkB;QAClB;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 531, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/BlocksConnect/web-panel-blocksconnect/src/components/ThemeSettings.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, createContext, useContext, ReactNode } from 'react';\n\ntype Theme = 'dark' | 'light' | 'system';\n\ninterface ThemeContextType {\n  theme: Theme;\n  setTheme: (theme: Theme) => void;\n  resolvedTheme: 'dark' | 'light';\n}\n\nconst ThemeContext = createContext<ThemeContextType | undefined>(undefined);\n\nexport function ThemeProvider({ children }: { children: ReactNode }) {\n  const [theme, setTheme] = useState<Theme>('dark');\n  const [resolvedTheme, setResolvedTheme] = useState<'dark' | 'light'>('dark');\n\n  useEffect(() => {\n    // Load theme from localStorage\n    const savedTheme = localStorage.getItem('theme') as Theme;\n    if (savedTheme && ['dark', 'light', 'system'].includes(savedTheme)) {\n      setTheme(savedTheme);\n    }\n  }, []);\n\n  useEffect(() => {\n    const updateResolvedTheme = () => {\n      let resolved: 'dark' | 'light' = 'dark';\n      \n      if (theme === 'system') {\n        resolved = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';\n      } else {\n        resolved = theme as 'dark' | 'light';\n      }\n      \n      setResolvedTheme(resolved);\n      \n      // Apply theme to document\n      document.documentElement.classList.remove('dark', 'light');\n      document.documentElement.classList.add(resolved);\n      \n      // Save to localStorage\n      localStorage.setItem('theme', theme);\n    };\n\n    updateResolvedTheme();\n\n    // Listen for system theme changes\n    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');\n    const handleChange = () => {\n      if (theme === 'system') {\n        updateResolvedTheme();\n      }\n    };\n\n    mediaQuery.addEventListener('change', handleChange);\n    return () => mediaQuery.removeEventListener('change', handleChange);\n  }, [theme]);\n\n  return (\n    <ThemeContext.Provider value={{ theme, setTheme, resolvedTheme }}>\n      {children}\n    </ThemeContext.Provider>\n  );\n}\n\nexport function useTheme() {\n  const context = useContext(ThemeContext);\n  if (context === undefined) {\n    throw new Error('useTheme must be used within a ThemeProvider');\n  }\n  return context;\n}\n\ninterface ThemeSettingsProps {\n  onClose?: () => void;\n}\n\nexport default function ThemeSettings({ onClose }: ThemeSettingsProps) {\n  const { theme, setTheme, resolvedTheme } = useTheme();\n  const [isOpen, setIsOpen] = useState(false);\n\n  const themes: { value: Theme; label: string; description: string; icon: string }[] = [\n    {\n      value: 'light',\n      label: 'Light',\n      description: 'Light theme with bright colors',\n      icon: '☀️'\n    },\n    {\n      value: 'dark',\n      label: 'Dark',\n      description: 'Dark theme with muted colors',\n      icon: '🌙'\n    },\n    {\n      value: 'system',\n      label: 'System',\n      description: 'Follow system preference',\n      icon: '💻'\n    }\n  ];\n\n  const handleThemeChange = (newTheme: Theme) => {\n    setTheme(newTheme);\n  };\n\n  const handleClose = () => {\n    setIsOpen(false);\n    onClose?.();\n  };\n\n  return (\n    <>\n      {/* Theme Toggle Button */}\n      <button\n        onClick={() => setIsOpen(true)}\n        className=\"flex items-center space-x-2 px-4 py-2 rounded-lg bg-white/5 hover:bg-white/10 border border-white/10 hover:border-white/20 text-gray-300 hover:text-white transition-all duration-300\"\n        title=\"Theme Settings\"\n      >\n        <span className=\"text-lg\">\n          {resolvedTheme === 'dark' ? '🌙' : '☀️'}\n        </span>\n        <span className=\"hidden sm:inline text-sm font-medium\">Theme</span>\n      </button>\n\n      {/* Theme Settings Modal */}\n      {isOpen && (\n        <div className=\"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4\">\n          <div className=\"bg-gray-800 rounded-lg w-full max-w-md border border-white/10 shadow-2xl\">\n            <div className=\"px-6 py-4 border-b border-white/10\">\n              <div className=\"flex items-center justify-between\">\n                <h2 className=\"text-lg font-semibold text-white flex items-center\">\n                  <span className=\"mr-2\">🎨</span>\n                  Theme Settings\n                </h2>\n                <button\n                  onClick={handleClose}\n                  className=\"text-gray-400 hover:text-white transition-colors\"\n                >\n                  <svg className=\"h-5 w-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                  </svg>\n                </button>\n              </div>\n              <p className=\"text-gray-400 text-sm mt-1\">Choose your preferred theme</p>\n            </div>\n\n            <div className=\"p-6 space-y-3\">\n              {themes.map((themeOption) => (\n                <button\n                  key={themeOption.value}\n                  onClick={() => handleThemeChange(themeOption.value)}\n                  className={`w-full flex items-center space-x-4 p-4 rounded-lg border transition-all duration-300 ${\n                    theme === themeOption.value\n                      ? 'border-blue-500 bg-blue-500/10 text-blue-400'\n                      : 'border-white/10 bg-white/5 hover:bg-white/10 text-gray-300 hover:text-white hover:border-white/20'\n                  }`}\n                >\n                  <span className=\"text-2xl\">{themeOption.icon}</span>\n                  <div className=\"flex-1 text-left\">\n                    <h3 className=\"font-medium\">{themeOption.label}</h3>\n                    <p className=\"text-sm opacity-75\">{themeOption.description}</p>\n                  </div>\n                  {theme === themeOption.value && (\n                    <svg className=\"h-5 w-5 text-blue-400\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                      <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n                    </svg>\n                  )}\n                </button>\n              ))}\n            </div>\n\n            <div className=\"px-6 py-4 border-t border-white/10 bg-white/5\">\n              <div className=\"flex items-center justify-between text-sm\">\n                <span className=\"text-gray-400\">Current theme:</span>\n                <span className=\"text-white font-medium capitalize\">\n                  {resolvedTheme} {theme === 'system' && '(System)'}\n                </span>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n    </>\n  );\n}\n\n// Quick theme toggle component for header\nexport function QuickThemeToggle() {\n  const { theme, setTheme, resolvedTheme } = useTheme();\n\n  const toggleTheme = () => {\n    if (theme === 'dark') {\n      setTheme('light');\n    } else if (theme === 'light') {\n      setTheme('system');\n    } else {\n      setTheme('dark');\n    }\n  };\n\n  return (\n    <button\n      onClick={toggleTheme}\n      className=\"p-2 rounded-lg bg-white/5 hover:bg-white/10 border border-white/10 hover:border-white/20 text-gray-300 hover:text-white transition-all duration-300\"\n      title={`Current: ${theme === 'system' ? `System (${resolvedTheme})` : theme}`}\n    >\n      <span className=\"text-lg\">\n        {theme === 'system' ? '💻' : resolvedTheme === 'dark' ? '🌙' : '☀️'}\n      </span>\n    </button>\n  );\n}\n\n// Light theme CSS variables (to be added to globals.css)\nexport const lightThemeCSS = `\n.light {\n  --background: #ffffff;\n  --background-secondary: #f8fafc;\n  --background-tertiary: #f1f5f9;\n  --foreground: #0f172a;\n  --primary: #3b82f6;\n  --primary-foreground: #ffffff;\n  --secondary: #64748b;\n  --muted: #f1f5f9;\n  --muted-foreground: #64748b;\n  --border: #e2e8f0;\n  --input: #f1f5f9;\n  --ring: #3b82f6;\n  --glow-primary: 0 0 20px rgba(59, 130, 246, 0.3);\n}\n\n.light .card {\n  background: rgba(248, 250, 252, 0.8);\n  border: 1px solid rgba(226, 232, 240, 0.3);\n  backdrop-filter: blur(10px);\n}\n\n.light .gradient-text {\n  background: linear-gradient(135deg, #3b82f6, #8b5cf6);\n  -webkit-background-clip: text;\n  -webkit-text-fill-color: transparent;\n  background-clip: text;\n}\n\n.light .input-field {\n  background: #f8fafc;\n  border: 1px solid #e2e8f0;\n  color: #0f172a;\n}\n\n.light .input-field:focus {\n  border-color: #3b82f6;\n  box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);\n  background: #ffffff;\n}\n\n.light .form-checkbox {\n  background: #f8fafc;\n  border: 2px solid #e2e8f0;\n}\n\n.light .form-checkbox:checked {\n  background: #3b82f6;\n  border-color: #3b82f6;\n}\n`;\n"], "names": [], "mappings": ";;;;;;;;AAEA;AAFA;;;AAYA,MAAM,6BAAe,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAgC;AAE1D,SAAS,cAAc,EAAE,QAAQ,EAA2B;IACjE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAS;IAC1C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;IAErE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,+BAA+B;QAC/B,MAAM,aAAa,aAAa,OAAO,CAAC;QACxC,IAAI,cAAc;YAAC;YAAQ;YAAS;SAAS,CAAC,QAAQ,CAAC,aAAa;YAClE,SAAS;QACX;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,sBAAsB;YAC1B,IAAI,WAA6B;YAEjC,IAAI,UAAU,UAAU;gBACtB,WAAW,OAAO,UAAU,CAAC,gCAAgC,OAAO,GAAG,SAAS;YAClF,OAAO;gBACL,WAAW;YACb;YAEA,iBAAiB;YAEjB,0BAA0B;YAC1B,SAAS,eAAe,CAAC,SAAS,CAAC,MAAM,CAAC,QAAQ;YAClD,SAAS,eAAe,CAAC,SAAS,CAAC,GAAG,CAAC;YAEvC,uBAAuB;YACvB,aAAa,OAAO,CAAC,SAAS;QAChC;QAEA;QAEA,kCAAkC;QAClC,MAAM,aAAa,OAAO,UAAU,CAAC;QACrC,MAAM,eAAe;YACnB,IAAI,UAAU,UAAU;gBACtB;YACF;QACF;QAEA,WAAW,gBAAgB,CAAC,UAAU;QACtC,OAAO,IAAM,WAAW,mBAAmB,CAAC,UAAU;IACxD,GAAG;QAAC;KAAM;IAEV,qBACE,8OAAC,aAAa,QAAQ;QAAC,OAAO;YAAE;YAAO;YAAU;QAAc;kBAC5D;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAMe,SAAS,cAAc,EAAE,OAAO,EAAsB;IACnE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE,GAAG;IAC3C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErC,MAAM,SAA+E;QACnF;YACE,OAAO;YACP,OAAO;YACP,aAAa;YACb,MAAM;QACR;QACA;YACE,OAAO;YACP,OAAO;YACP,aAAa;YACb,MAAM;QACR;QACA;YACE,OAAO;YACP,OAAO;YACP,aAAa;YACb,MAAM;QACR;KACD;IAED,MAAM,oBAAoB,CAAC;QACzB,SAAS;IACX;IAEA,MAAM,cAAc;QAClB,UAAU;QACV;IACF;IAEA,qBACE;;0BAEE,8OAAC;gBACC,SAAS,IAAM,UAAU;gBACzB,WAAU;gBACV,OAAM;;kCAEN,8OAAC;wBAAK,WAAU;kCACb,kBAAkB,SAAS,OAAO;;;;;;kCAErC,8OAAC;wBAAK,WAAU;kCAAuC;;;;;;;;;;;;YAIxD,wBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;oDAAK,WAAU;8DAAO;;;;;;gDAAS;;;;;;;sDAGlC,8OAAC;4CACC,SAAS;4CACT,WAAU;sDAEV,cAAA,8OAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACjE,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;8CAI3E,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;sCAG5C,8OAAC;4BAAI,WAAU;sCACZ,OAAO,GAAG,CAAC,CAAC,4BACX,8OAAC;oCAEC,SAAS,IAAM,kBAAkB,YAAY,KAAK;oCAClD,WAAW,CAAC,qFAAqF,EAC/F,UAAU,YAAY,KAAK,GACvB,iDACA,qGACJ;;sDAEF,8OAAC;4CAAK,WAAU;sDAAY,YAAY,IAAI;;;;;;sDAC5C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAe,YAAY,KAAK;;;;;;8DAC9C,8OAAC;oDAAE,WAAU;8DAAsB,YAAY,WAAW;;;;;;;;;;;;wCAE3D,UAAU,YAAY,KAAK,kBAC1B,8OAAC;4CAAI,WAAU;4CAAwB,MAAK;4CAAe,SAAQ;sDACjE,cAAA,8OAAC;gDAAK,UAAS;gDAAU,GAAE;gDAAwI,UAAS;;;;;;;;;;;;mCAf3K,YAAY,KAAK;;;;;;;;;;sCAsB5B,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,8OAAC;wCAAK,WAAU;;4CACb;4CAAc;4CAAE,UAAU,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASzD;AAGO,SAAS;IACd,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,aAAa,EAAE,GAAG;IAE3C,MAAM,cAAc;QAClB,IAAI,UAAU,QAAQ;YACpB,SAAS;QACX,OAAO,IAAI,UAAU,SAAS;YAC5B,SAAS;QACX,OAAO;YACL,SAAS;QACX;IACF;IAEA,qBACE,8OAAC;QACC,SAAS;QACT,WAAU;QACV,OAAO,CAAC,SAAS,EAAE,UAAU,WAAW,CAAC,QAAQ,EAAE,cAAc,CAAC,CAAC,GAAG,OAAO;kBAE7E,cAAA,8OAAC;YAAK,WAAU;sBACb,UAAU,WAAW,OAAO,kBAAkB,SAAS,OAAO;;;;;;;;;;;AAIvE;AAGO,MAAM,gBAAgB,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmD9B,CAAC", "debugId": null}}, {"offset": {"line": 947, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/BlocksConnect/web-panel-blocksconnect/src/components/ParticleBackground.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef } from 'react';\n\ninterface Particle {\n  x: number;\n  y: number;\n  vx: number;\n  vy: number;\n  size: number;\n  opacity: number;\n}\n\nexport default function ParticleBackground() {\n  const canvasRef = useRef<HTMLCanvasElement>(null);\n  const particlesRef = useRef<Particle[]>([]);\n  const animationRef = useRef<number | null>(null);\n\n  useEffect(() => {\n    const canvas = canvasRef.current;\n    if (!canvas) return;\n\n    const ctx = canvas.getContext('2d');\n    if (!ctx) return;\n\n    const resizeCanvas = () => {\n      canvas.width = window.innerWidth;\n      canvas.height = window.innerHeight;\n    };\n\n    const createParticles = () => {\n      const particles: Particle[] = [];\n      const particleCount = Math.min(50, Math.floor((canvas.width * canvas.height) / 15000));\n\n      for (let i = 0; i < particleCount; i++) {\n        particles.push({\n          x: Math.random() * canvas.width,\n          y: Math.random() * canvas.height,\n          vx: (Math.random() - 0.5) * 0.5,\n          vy: (Math.random() - 0.5) * 0.5,\n          size: Math.random() * 2 + 1,\n          opacity: Math.random() * 0.5 + 0.2,\n        });\n      }\n\n      particlesRef.current = particles;\n    };\n\n    const drawParticle = (particle: Particle) => {\n      ctx.save();\n      ctx.globalAlpha = particle.opacity;\n\n      // Create gradient for particle\n      const gradient = ctx.createRadialGradient(\n        particle.x, particle.y, 0,\n        particle.x, particle.y, particle.size * 2\n      );\n      gradient.addColorStop(0, '#3b82f6');\n      gradient.addColorStop(0.5, '#8b5cf6');\n      gradient.addColorStop(1, 'transparent');\n\n      ctx.fillStyle = gradient;\n      ctx.beginPath();\n      ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);\n      ctx.fill();\n\n      ctx.restore();\n    };\n\n    const drawConnections = () => {\n      const particles = particlesRef.current;\n      const maxDistance = 120;\n\n      for (let i = 0; i < particles.length; i++) {\n        for (let j = i + 1; j < particles.length; j++) {\n          const dx = particles[i].x - particles[j].x;\n          const dy = particles[i].y - particles[j].y;\n          const distance = Math.sqrt(dx * dx + dy * dy);\n\n          if (distance < maxDistance) {\n            const opacity = (1 - distance / maxDistance) * 0.2;\n\n            ctx.save();\n            ctx.globalAlpha = opacity;\n            ctx.strokeStyle = '#3b82f6';\n            ctx.lineWidth = 0.5;\n            ctx.beginPath();\n            ctx.moveTo(particles[i].x, particles[i].y);\n            ctx.lineTo(particles[j].x, particles[j].y);\n            ctx.stroke();\n            ctx.restore();\n          }\n        }\n      }\n    };\n\n    const updateParticles = () => {\n      const particles = particlesRef.current;\n\n      particles.forEach(particle => {\n        particle.x += particle.vx;\n        particle.y += particle.vy;\n\n        // Bounce off edges\n        if (particle.x < 0 || particle.x > canvas.width) {\n          particle.vx *= -1;\n        }\n        if (particle.y < 0 || particle.y > canvas.height) {\n          particle.vy *= -1;\n        }\n\n        // Keep particles within bounds\n        particle.x = Math.max(0, Math.min(canvas.width, particle.x));\n        particle.y = Math.max(0, Math.min(canvas.height, particle.y));\n\n        // Subtle opacity animation\n        particle.opacity += (Math.random() - 0.5) * 0.01;\n        particle.opacity = Math.max(0.1, Math.min(0.7, particle.opacity));\n      });\n    };\n\n    const animate = () => {\n      ctx.clearRect(0, 0, canvas.width, canvas.height);\n\n      updateParticles();\n      drawConnections();\n\n      particlesRef.current.forEach(drawParticle);\n\n      animationRef.current = requestAnimationFrame(animate);\n    };\n\n    // Initialize\n    resizeCanvas();\n    createParticles();\n    animate();\n\n    // Handle resize\n    const handleResize = () => {\n      resizeCanvas();\n      createParticles();\n    };\n\n    window.addEventListener('resize', handleResize);\n\n    return () => {\n      window.removeEventListener('resize', handleResize);\n      if (animationRef.current) {\n        cancelAnimationFrame(animationRef.current);\n      }\n    };\n  }, []);\n\n  return (\n    <canvas\n      ref={canvasRef}\n      className=\"particle-background\"\n      style={{\n        position: 'fixed',\n        top: 0,\n        left: 0,\n        width: '100%',\n        height: '100%',\n        pointerEvents: 'none',\n        zIndex: -1,\n      }}\n    />\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAae,SAAS;IACtB,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAqB;IAC5C,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAc,EAAE;IAC1C,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAiB;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,SAAS,UAAU,OAAO;QAChC,IAAI,CAAC,QAAQ;QAEb,MAAM,MAAM,OAAO,UAAU,CAAC;QAC9B,IAAI,CAAC,KAAK;QAEV,MAAM,eAAe;YACnB,OAAO,KAAK,GAAG,OAAO,UAAU;YAChC,OAAO,MAAM,GAAG,OAAO,WAAW;QACpC;QAEA,MAAM,kBAAkB;YACtB,MAAM,YAAwB,EAAE;YAChC,MAAM,gBAAgB,KAAK,GAAG,CAAC,IAAI,KAAK,KAAK,CAAC,AAAC,OAAO,KAAK,GAAG,OAAO,MAAM,GAAI;YAE/E,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,IAAK;gBACtC,UAAU,IAAI,CAAC;oBACb,GAAG,KAAK,MAAM,KAAK,OAAO,KAAK;oBAC/B,GAAG,KAAK,MAAM,KAAK,OAAO,MAAM;oBAChC,IAAI,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;oBAC5B,IAAI,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;oBAC5B,MAAM,KAAK,MAAM,KAAK,IAAI;oBAC1B,SAAS,KAAK,MAAM,KAAK,MAAM;gBACjC;YACF;YAEA,aAAa,OAAO,GAAG;QACzB;QAEA,MAAM,eAAe,CAAC;YACpB,IAAI,IAAI;YACR,IAAI,WAAW,GAAG,SAAS,OAAO;YAElC,+BAA+B;YAC/B,MAAM,WAAW,IAAI,oBAAoB,CACvC,SAAS,CAAC,EAAE,SAAS,CAAC,EAAE,GACxB,SAAS,CAAC,EAAE,SAAS,CAAC,EAAE,SAAS,IAAI,GAAG;YAE1C,SAAS,YAAY,CAAC,GAAG;YACzB,SAAS,YAAY,CAAC,KAAK;YAC3B,SAAS,YAAY,CAAC,GAAG;YAEzB,IAAI,SAAS,GAAG;YAChB,IAAI,SAAS;YACb,IAAI,GAAG,CAAC,SAAS,CAAC,EAAE,SAAS,CAAC,EAAE,SAAS,IAAI,EAAE,GAAG,KAAK,EAAE,GAAG;YAC5D,IAAI,IAAI;YAER,IAAI,OAAO;QACb;QAEA,MAAM,kBAAkB;YACtB,MAAM,YAAY,aAAa,OAAO;YACtC,MAAM,cAAc;YAEpB,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;gBACzC,IAAK,IAAI,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;oBAC7C,MAAM,KAAK,SAAS,CAAC,EAAE,CAAC,CAAC,GAAG,SAAS,CAAC,EAAE,CAAC,CAAC;oBAC1C,MAAM,KAAK,SAAS,CAAC,EAAE,CAAC,CAAC,GAAG,SAAS,CAAC,EAAE,CAAC,CAAC;oBAC1C,MAAM,WAAW,KAAK,IAAI,CAAC,KAAK,KAAK,KAAK;oBAE1C,IAAI,WAAW,aAAa;wBAC1B,MAAM,UAAU,CAAC,IAAI,WAAW,WAAW,IAAI;wBAE/C,IAAI,IAAI;wBACR,IAAI,WAAW,GAAG;wBAClB,IAAI,WAAW,GAAG;wBAClB,IAAI,SAAS,GAAG;wBAChB,IAAI,SAAS;wBACb,IAAI,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE,SAAS,CAAC,EAAE,CAAC,CAAC;wBACzC,IAAI,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE,SAAS,CAAC,EAAE,CAAC,CAAC;wBACzC,IAAI,MAAM;wBACV,IAAI,OAAO;oBACb;gBACF;YACF;QACF;QAEA,MAAM,kBAAkB;YACtB,MAAM,YAAY,aAAa,OAAO;YAEtC,UAAU,OAAO,CAAC,CAAA;gBAChB,SAAS,CAAC,IAAI,SAAS,EAAE;gBACzB,SAAS,CAAC,IAAI,SAAS,EAAE;gBAEzB,mBAAmB;gBACnB,IAAI,SAAS,CAAC,GAAG,KAAK,SAAS,CAAC,GAAG,OAAO,KAAK,EAAE;oBAC/C,SAAS,EAAE,IAAI,CAAC;gBAClB;gBACA,IAAI,SAAS,CAAC,GAAG,KAAK,SAAS,CAAC,GAAG,OAAO,MAAM,EAAE;oBAChD,SAAS,EAAE,IAAI,CAAC;gBAClB;gBAEA,+BAA+B;gBAC/B,SAAS,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,OAAO,KAAK,EAAE,SAAS,CAAC;gBAC1D,SAAS,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,OAAO,MAAM,EAAE,SAAS,CAAC;gBAE3D,2BAA2B;gBAC3B,SAAS,OAAO,IAAI,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gBAC5C,SAAS,OAAO,GAAG,KAAK,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,KAAK,SAAS,OAAO;YACjE;QACF;QAEA,MAAM,UAAU;YACd,IAAI,SAAS,CAAC,GAAG,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM;YAE/C;YACA;YAEA,aAAa,OAAO,CAAC,OAAO,CAAC;YAE7B,aAAa,OAAO,GAAG,sBAAsB;QAC/C;QAEA,aAAa;QACb;QACA;QACA;QAEA,gBAAgB;QAChB,MAAM,eAAe;YACnB;YACA;QACF;QAEA,OAAO,gBAAgB,CAAC,UAAU;QAElC,OAAO;YACL,OAAO,mBAAmB,CAAC,UAAU;YACrC,IAAI,aAAa,OAAO,EAAE;gBACxB,qBAAqB,aAAa,OAAO;YAC3C;QACF;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC;QACC,KAAK;QACL,WAAU;QACV,OAAO;YACL,UAAU;YACV,KAAK;YACL,MAAM;YACN,OAAO;YACP,QAAQ;YACR,eAAe;YACf,QAAQ,CAAC;QACX;;;;;;AAGN", "debugId": null}}]}