#!/bin/bash

echo "Initializing Gradle Wrapper for BlocksConnect Forge Mod..."
echo

# Check if Java is available
if ! command -v java &> /dev/null; then
    echo "ERROR: Java is not installed or not in PATH"
    echo "Please install Java 21 or higher and try again"
    exit 1
fi

echo "Java version:"
java -version
echo

# Create gradle wrapper directory
echo "Creating gradle wrapper directory..."
mkdir -p gradle/wrapper

echo
echo "Downloading Gradle wrapper JAR..."
echo "This may take a moment..."

# Try to download using curl or wget
if command -v curl &> /dev/null; then
    curl -L -o gradle/wrapper/gradle-wrapper.jar \
        "https://github.com/gradle/gradle/raw/v8.8.0/gradle/wrapper/gradle-wrapper.jar"
elif command -v wget &> /dev/null; then
    wget -O gradle/wrapper/gradle-wrapper.jar \
        "https://github.com/gradle/gradle/raw/v8.8.0/gradle/wrapper/gradle-wrapper.jar"
else
    echo "ERROR: Neither curl nor wget is available"
    echo "Please download gradle-wrapper.jar manually from:"
    echo "https://github.com/gradle/gradle/raw/v8.8.0/gradle/wrapper/gradle-wrapper.jar"
    echo "And place it in: gradle/wrapper/gradle-wrapper.jar"
    exit 1
fi

# Make gradlew executable
chmod +x gradlew

# Check if download was successful
if [ -f "gradle/wrapper/gradle-wrapper.jar" ]; then
    echo
    echo "✅ Gradle wrapper initialized successfully!"
    echo
    echo "You can now build the mod with:"
    echo "  ./gradlew build"
    echo
    echo "Or run in development mode with:"
    echo "  ./gradlew runServer"
    echo
else
    echo
    echo "❌ Failed to download Gradle wrapper JAR."
    echo
    echo "Manual steps:"
    echo "1. Download gradle-wrapper.jar from:"
    echo "   https://github.com/gradle/gradle/raw/v8.8.0/gradle/wrapper/gradle-wrapper.jar"
    echo "2. Place it in: gradle/wrapper/gradle-wrapper.jar"
    echo "3. Run: ./gradlew build"
    echo
fi
