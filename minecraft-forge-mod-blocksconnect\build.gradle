plugins {
    id 'eclipse'
    id 'maven-publish'
    id 'net.minecraftforge.gradle' version '6.0.+'
}

version = '1.0.0'
group = 'com.blocksconnect'
archivesBaseName = 'blocksconnect-forge-mod'

// Mojang ships Java 21 to end users in 1.20.5+, so your mod should target Java 21.
java.toolchain.languageVersion = JavaLanguageVersion.of(21)

println "Java: ${System.getProperty 'java.version'}, JVM: ${System.getProperty 'java.vm.version'} (${System.getProperty 'java.vendor'}), Arch: ${System.getProperty 'os.arch'}"
minecraft {
    // The mappings can be changed at any time and must be in the following format.
    // Channel:   Version:
    // official   MCVersion             Official field/method names from Mojang mapping files

    mappings channel: 'official', version: '1.21.1'

    // When true, this property will have all Eclipse/IntelliJ IDEA run configurations run the "prepareX" task for the given run configuration before launching the game.
    // In most cases, it is not necessary to enable.
    // enableEclipsePrepareRuns = true
    // enableIdeaPrepareRuns = true

    // This property allows configuring Gradle's ProcessFork options for the game and data generation runs.
    // It does not affect the runs in the IDE.
    // gameTestServer {
    //     systemProperty 'forge.logging.markers', 'REGISTRIES'
    //     systemProperty 'forge.logging.console.level', 'debug'
    // }

    copyIdeResources = true // Calls processResources when building

    runs {
        // applies to all the run configs below
        configureEach {
            workingDirectory project.file('run')

            // Recommended logging data for a userdev environment
            // The markers can be added/remove as needed separated by commas.
            // "SCAN": For mods scan.
            // "REGISTRIES": For firing of registry events.
            // "REGISTRYDUMP": For getting the contents of all registries.
            property 'forge.logging.markers', 'REGISTRIES'

            // Recommended logging level for the console
            // You can set various levels here.
            // Please read: https://stackoverflow.com/questions/2031163/when-to-use-the-different-log-levels
            property 'forge.logging.console.level', 'debug'

            mods {
                blocksconnect {
                    source sourceSets.main
                }
            }
        }

        client {
            // Comma-separated list of namespaces to load gametests from. Empty = all namespaces.
            property 'forge.enabledGameTestNamespaces', 'blocksconnect'
        }

        server {
            property 'forge.enabledGameTestNamespaces', 'blocksconnect'
            args '--nogui'
        }

        // This run config launches GameTestServer and runs all registered gametests, then exits.
        // By default, the server will crash when no gametests are provided.
        // The gametest system is also enabled by default for other run configs under the /test command.
        gameTestServer {
            property 'forge.enabledGameTestNamespaces', 'blocksconnect'
        }

        data {
            // example of overriding the workingDirectory set in configureEach above
            workingDirectory project.file('run-data')

            // Specify the modid for data generation, where to output the resulting resource, and where to look for existing resources.
            args '--mod', 'blocksconnect', '--all', '--output', file('src/generated/resources/'), '--existing', file('src/main/resources/')
        }
    }
}

// Include resources generated by data generators.
sourceSets.main.resources { srcDir 'src/generated/resources' }

repositories {
    // Put repositories for dependencies here
    // ForgeGradle automatically adds the Forge maven and Maven Central for you

    // If you have mod jar dependencies in ./libs, you can declare them as a repository like so.
    // See https://docs.gradle.org/current/userguide/declaring_repositories.html#sub:flat_dir_resolver
    // flatDir {
    //     dir 'libs'
    // }
}

dependencies {
    // Specify the version of Minecraft to use.
    // Any artifact can be supplied so long as it has a "userdev" classifier artifact.
    // The userdev artifact is a special name and will get all the correct obfuscated and deobfuscated Minecraft dependencies.
    minecraft 'net.minecraftforge:forge:1.21.1-52.0.17'

    // Example mod dependency with JEI - using fg.deobf() ensures the dependency is remapped to your development mappings
    // The JEI API is declared for compile time use, while the full JEI artifact is used at runtime
    // compileOnly fg.deobf("mezz.jei:jei-${mc_version}-common-api:${jei_version}")
    // compileOnly fg.deobf("mezz.jei:jei-${mc_version}-forge-api:${jei_version}")
    // runtimeOnly fg.deobf("mezz.jei:jei-${mc_version}-forge:${jei_version}")

    // Example mod dependency using a mod jar from ./libs with a flat dir repository
    // This maps to ./libs/coolmod-${mc_version}-${coolmod_version}.jar
    // The group id is ignored when searching -- in this case, it is "blank"
    // implementation fg.deobf("blank:coolmod-${mc_version}:${coolmod_version}")

    // HTTP client for API communication
    implementation 'com.squareup.okhttp3:okhttp:4.12.0'
    implementation 'com.google.code.gson:gson:2.10.1'

    // For real time you may want to use the :runtime_only scope instead (scope: 'runtime_only')
    // implementation fg.deobf("top.theillusivec4.curios:curios-forge:${mc_version}-${curios_version}")

    // Example mod dependency using a mod jar from ./libs with a flat dir repository
    // This maps to ./libs/coolmod-${mc_version}-${coolmod_version}.jar
    // The group id is ignored when searching -- in this case, it is "blank"
    // implementation fg.deobf("blank:coolmod-${mc_version}:${coolmod_version}")
}

// Example for how to get properties into the manifest for reading at runtime.
jar {
    manifest {
        attributes([
                "Specification-Title"     : "blocksconnect",
                "Specification-Vendor"   : "BlocksConnect Team",
                "Specification-Version"  : "1", // We are version 1 of ourselves
                "Implementation-Title"   : project.name,
                "Implementation-Version" : project.jar.archiveVersion,
                "Implementation-Vendor"  : "BlocksConnect Team",
                "Implementation-Timestamp": new Date().format("yyyy-MM-dd'T'HH:mm:ssZ")
        ])
    }
}

// Example configuration to allow publishing using the maven-publish plugin
publishing {
    publications {
        maven(MavenPublication) {
            from components.java
        }
    }
    repositories {
        // Add repositories to publish to here.
        // Notice: This block does NOT have the same function as the block in the top level.
        // The repositories here will be used for publishing your artifact, not for
        // retrieving dependencies.
    }
}

tasks.withType(JavaCompile).configureEach {
    options.encoding = 'UTF-8' // Use the UTF-8 charset for Java compilation
}
