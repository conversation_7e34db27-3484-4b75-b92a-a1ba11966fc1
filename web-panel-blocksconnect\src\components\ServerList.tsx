'use client';

import { useState, useEffect } from 'react';
import ConfirmModal from './ConfirmModal';
import { getServers, startServer, stopServer, deleteServer, toggleBackup, downloadBackup, Server } from '../services/api';
import { useAuth } from '../contexts/AuthContext';
import Link from 'next/link';
import { useRouter } from 'next/navigation';

interface ServerListProps {
  refreshTrigger: number;
}

export default function ServerList({ refreshTrigger }: ServerListProps) {
  const [servers, setServers] = useState<Server[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { user, isLoading: authLoading, getToken } = useAuth();
  const router = useRouter();

  useEffect(() => {
    const fetchServers = async () => {
      if (authLoading || !user) return;
      setIsLoading(true);
      setError(null);
      try {
        const token = await getToken();
        const data = await getServers(token);
        setServers(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An unknown error occurred');
      } finally {
        setIsLoading(false);
      }
    };
    fetchServers();
  }, [refreshTrigger, user, authLoading, getToken]);

  const handleStartServer = async (serverId: string) => {
    try {
      const token = await getToken();
      await startServer(serverId, token);
      setServers(servers.map(server =>
        server.id === serverId ? { ...server, status: 'running' } : server
      ));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
    }
  };

  const handleStopServer = async (serverId: string) => {
    try {
      const token = await getToken();
      await stopServer(serverId, token);
      setServers(servers.map(server =>
        server.id === serverId ? { ...server, status: 'stopped' } : server
      ));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
    }
  };

  const [deleteTarget, setDeleteTarget] = useState<Server | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDeleteServer = (server: Server) => {
    setDeleteTarget(server);
  };

  const confirmDeleteServer = async () => {
    if (!deleteTarget) return;
    setIsDeleting(true);
    try {
      const token = await getToken();
      await deleteServer(deleteTarget.id, token);
      router.refresh();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
    } finally {
      setIsDeleting(false);
      setDeleteTarget(null);
    }
  };

  const handleToggleBackup = async (serverId: string) => {
    try {
      const token = await getToken();
      await toggleBackup(serverId, token);
      setServers(servers.map(server =>
        server.id === serverId ? { ...server, backup: !server.backup } : server
      ));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
    }
  };

  const handleDownloadBackup = async (serverId: string) => {
    try {
      const token = await getToken();
      await downloadBackup(serverId, token);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running':
        return 'text-green-400 bg-green-500/20 border-green-500/30';
      case 'stopped':
        return 'text-red-400 bg-red-500/20 border-red-500/30';
      case 'starting':
        return 'text-yellow-400 bg-yellow-500/20 border-yellow-500/30';
      default:
        return 'text-gray-400 bg-gray-500/20 border-gray-500/30';
    }
  };

  if (authLoading || isLoading) {
    return (
      <div className="flex items-center justify-center py-16">
        <div className="relative">
          <div className="animate-spin h-12 w-12 border-4 border-blue-500/30 border-t-blue-500 rounded-full"></div>
          <div className="absolute inset-0 animate-ping h-12 w-12 border-4 border-blue-500/20 rounded-full"></div>
        </div>
        <span className="ml-4 text-gray-300 font-medium">
          {authLoading ? 'Authenticating...' : 'Loading servers...'}
        </span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-500/10 border border-red-500/30 text-red-400 px-6 py-4 rounded-lg backdrop-blur-sm">
        <div className="flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-3" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
          <div>
            <h3 className="font-semibold">Error Loading Servers</h3>
            <p className="text-sm text-red-300">{error}</p>
          </div>
        </div>
      </div>
    );
  }

  if (servers.length === 0) {
    return (
      <div className="text-center py-16">
        <div className="w-20 h-20 mx-auto mb-6 rounded-full bg-gradient-to-br from-blue-500/20 to-purple-500/20 flex items-center justify-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-10 w-10 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01" />
          </svg>
        </div>
        <h3 className="text-xl font-semibold text-white mb-2">No servers found</h3>
        <p className="text-gray-400 font-light">Create your first Minecraft server to get started.</p>
      </div>
    );
  }

  return (
    <>
      <div className="space-y-6">
        {servers.map((server) => (
          <div key={server.id} className="card p-6 transition-all duration-300">
            {/* Server Header */}
            <div className="flex items-center justify-between mb-6">
              <div className="flex items-center">
                <h3 className="text-xl font-bold text-white mr-4">{server.name}</h3>
                <span className={`px-4 py-2 rounded-full text-sm font-semibold border backdrop-blur-sm ${getStatusColor(server.status)}`}>
                  {server.status.charAt(0).toUpperCase() + server.status.slice(1)}
                </span>
              </div>
            </div>

            {/* Server Info Grid */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm mb-6">
              <div className="bg-blue-500/10 rounded-lg p-3 border border-blue-500/20">
                <span className="text-blue-300 font-medium block">Port</span>
                <span className="text-white font-semibold">{server.port}</span>
              </div>
              <div className="bg-purple-500/10 rounded-lg p-3 border border-purple-500/20">
                <span className="text-purple-300 font-medium block">Version</span>
                <span className="text-white font-semibold">{server.version}</span>
              </div>
              <div className="bg-indigo-500/10 rounded-lg p-3 border border-indigo-500/20">
                <span className="text-indigo-300 font-medium block">Memory</span>
                <span className="text-white font-semibold">{server.memory}</span>
              </div>
              <div className="bg-emerald-500/10 rounded-lg p-3 border border-emerald-500/20">
                <span className="text-emerald-300 font-medium block">Backup</span>
                <span className="text-white font-semibold">{server.backup ? 'Enabled' : 'Disabled'}</span>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-wrap items-center gap-2 justify-end border-t border-white/10 pt-4">
              {server.status === 'stopped' ? (
                <button
                  onClick={() => handleStartServer(server.id)}
                  className="inline-flex items-center px-3 py-2 text-sm font-medium rounded-lg border transition-all duration-200 hover:scale-105 bg-green-500/20 hover:bg-green-500/30 border-green-500/30 hover:border-green-500/50 text-green-400 hover:text-green-300 hover:shadow-lg hover:shadow-green-500/20"
                >
                  <svg className="h-4 w-4 mr-1.5" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                  </svg>
                  Start
                </button>
              ) : (
                <button
                  onClick={() => handleStopServer(server.id)}
                  className="inline-flex items-center px-3 py-2 text-sm font-medium rounded-lg border transition-all duration-200 hover:scale-105 bg-red-500/20 hover:bg-red-500/30 border-red-500/30 hover:border-red-500/50 text-red-400 hover:text-red-300 hover:shadow-lg hover:shadow-red-500/20"
                >
                  <svg className="h-4 w-4 mr-1.5" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 00-1 1v4a1 1 0 001 1h4a1 1 0 001-1V8a1 1 0 00-1-1H8z" clipRule="evenodd" />
                  </svg>
                  Stop
                </button>
              )}

              <button
                onClick={() => handleToggleBackup(server.id)}
                className={`inline-flex items-center px-3 py-2 text-sm font-medium rounded-lg border transition-all duration-200 hover:scale-105 ${
                  server.backup
                    ? 'bg-blue-500/20 hover:bg-blue-500/30 border-blue-500/30 hover:border-blue-500/50 text-blue-400 hover:text-blue-300 hover:shadow-lg hover:shadow-blue-500/20'
                    : 'bg-gray-500/20 hover:bg-gray-500/30 border-gray-500/30 hover:border-gray-500/50 text-gray-400 hover:text-gray-300 hover:shadow-lg hover:shadow-gray-500/20'
                }`}
              >
                <svg className="h-4 w-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10" />
                </svg>
                {server.backup ? 'Backup On' : 'Backup Off'}
              </button>

              {server.backup && (
                <button
                  onClick={() => handleDownloadBackup(server.id)}
                  className="inline-flex items-center px-3 py-2 text-sm font-medium rounded-lg border transition-all duration-200 hover:scale-105 bg-purple-500/20 hover:bg-purple-500/30 border-purple-500/30 hover:border-purple-500/50 text-purple-400 hover:text-purple-300 hover:shadow-lg hover:shadow-purple-500/20"
                >
                  <svg className="h-4 w-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  Download
                </button>
              )}

              <Link
                href={`/minecraft/server/${server.id}`}
                className="inline-flex items-center px-3 py-2 text-sm font-medium rounded-lg border transition-all duration-200 hover:scale-105 bg-indigo-500/20 hover:bg-indigo-500/30 border-indigo-500/30 hover:border-indigo-500/50 text-indigo-400 hover:text-indigo-300 hover:shadow-lg hover:shadow-indigo-500/20"
              >
                <svg className="h-4 w-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                Details
              </Link>

              <button
                onClick={() => handleDeleteServer(server)}
                className="inline-flex items-center px-3 py-2 text-sm font-medium rounded-lg border transition-all duration-200 hover:scale-105 bg-red-500/20 hover:bg-red-500/30 border-red-500/30 hover:border-red-500/50 text-red-400 hover:text-red-300 hover:shadow-lg hover:shadow-red-500/20"
                disabled={isDeleting && deleteTarget?.id === server.id}
              >
                <svg className="h-4 w-4 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1-1H8a1 1 0 00-1 1v3M4 7h16" />
                </svg>
                {isDeleting && deleteTarget?.id === server.id ? 'Deleting...' : 'Delete'}
              </button>
            </div>
          </div>
        ))}
      </div>
      <ConfirmModal
        open={!!deleteTarget}
        title="Delete Server"
        message={deleteTarget ? `Are you sure you want to delete the server "${deleteTarget.name}"? This action cannot be undone.` : ''}
        confirmText={isDeleting ? 'Deleting...' : 'Delete'}
        cancelText="Cancel"
        onConfirm={confirmDeleteServer}
        onCancel={() => setDeleteTarget(null)}
      />
    </>
  )}
