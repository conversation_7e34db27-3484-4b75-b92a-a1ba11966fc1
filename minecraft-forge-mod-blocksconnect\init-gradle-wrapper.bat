@echo off
echo Initializing Gradle Wrapper for BlocksConnect Forge Mod...
echo.

REM Check if Java is available
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Java is not installed or not in PATH
    echo Please install Java 21 or higher and try again
    pause
    exit /b 1
)

REM Check if we have internet connection
ping -n 1 google.com >nul 2>&1
if %errorlevel% neq 0 (
    echo WARNING: No internet connection detected
    echo You may need to download Gradle manually
)

echo Creating gradle wrapper directory...
if not exist "gradle\wrapper" mkdir "gradle\wrapper"

echo.
echo Downloading Gradle wrapper JAR...
echo This may take a moment...

REM Try to download using PowerShell (Windows 10+)
powershell -Command "try { Invoke-WebRequest -Uri 'https://github.com/gradle/gradle/raw/v8.8.0/gradle/wrapper/gradle-wrapper.jar' -OutFile 'gradle/wrapper/gradle-wrapper.jar' -UseBasicParsing; Write-Host 'Gradle wrapper JAR downloaded successfully' } catch { Write-Host 'Failed to download Gradle wrapper JAR. Please download manually from: https://github.com/gradle/gradle/raw/v8.8.0/gradle/wrapper/gradle-wrapper.jar' }"

REM Check if download was successful
if exist "gradle\wrapper\gradle-wrapper.jar" (
    echo.
    echo ✅ Gradle wrapper initialized successfully!
    echo.
    echo You can now build the mod with:
    echo   gradlew.bat build
    echo.
    echo Or run in development mode with:
    echo   gradlew.bat runServer
    echo.
) else (
    echo.
    echo ❌ Failed to download Gradle wrapper JAR automatically.
    echo.
    echo Manual steps:
    echo 1. Download gradle-wrapper.jar from:
    echo    https://github.com/gradle/gradle/raw/v8.8.0/gradle/wrapper/gradle-wrapper.jar
    echo 2. Place it in: gradle\wrapper\gradle-wrapper.jar
    echo 3. Run: gradlew.bat build
    echo.
)

pause
