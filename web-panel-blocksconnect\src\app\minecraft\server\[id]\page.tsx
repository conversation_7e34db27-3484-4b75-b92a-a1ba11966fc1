'use client';

import { useState, useEffect, use } from 'react';
import ConfirmModal from '../../../../components/ConfirmModal';
import { useParams, useRouter } from 'next/navigation';
import { getServerDetails, startServer, stopServer, deleteServer, toggleBackup, downloadBackup, Server } from '../../../../services/api';
import { useAuth } from '../../../../contexts/AuthContext';
import ServerConsole from '../../../../components/ServerConsole';
import ServerPropertiesEditor from '../../../../components/ServerPropertiesEditor';
import BackupManager from '../../../../components/BackupManager';
import PlayerManager from '../../../../components/PlayerManager';
import ServerMonitoring from '../../../../components/ServerMonitoring';
import FileManager from '../../../../components/FileManager';
import ProtectedRoute from '../../../../components/ProtectedRoute';
import AdminHeader from '../../../../components/Header';


export default function ServerDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const serverId = params.id as string;
  const { user, isLoading: authLoading, getToken } = useAuth();

  const [server, setServer] = useState<Server | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [activeTab, setActiveTab] = useState<'overview' | 'properties' | 'players' | 'backups' | 'files' | 'monitoring' | 'console'>('overview');

  const fetchServerDetails = async () => {
    // Don't fetch if auth is still loading or user is not authenticated
    if (authLoading || !user) {
      return;
    }

    try {
      setIsLoading(true);
      const token = await getToken()
      const data = await getServerDetails(serverId, token);
      setServer(data);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    if (serverId) {
      fetchServerDetails();
    }
  }, [serverId, user, authLoading]);


  const handleStartServer = async () => {
    if (!server) return;

    try {
      const token = await getToken();
      await startServer(server.id, token);
      setServer({ ...server, status: 'running' });
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
    }
  };

  const handleStopServer = async () => {
    if (!server) return;

    try {
      const token = await getToken();
      await stopServer(server.id, token);
      setServer({ ...server, status: 'stopped' });
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
    }
  };

  const handleDeleteServer = () => {
    setDeleteModalOpen(true);
  };

  const confirmDeleteServer = async () => {
    if (!server) return;
    setIsDeleting(true);
    try {
      const token = await getToken();
      await deleteServer(server.id, token);
      router.push('/minecraft/dashboard');
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
    } finally {
      setIsDeleting(false);
      setDeleteModalOpen(false);
      router.push('/minecraft/dashboard');
    }
  };

  const handleToggleBackup = async () => {
    if (!server) return;

    try {
      const token = await getToken();
      await toggleBackup(server.id, token);
      // Refresh server details
      const updatedServer = await getServerDetails(serverId, token);
      setServer(updatedServer);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
    }
  };

  const handleDownloadBackup = async () => {
    if (!server) return;

    try {
      const token = await getToken();
      await downloadBackup(server.id, token);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running':
        return 'text-green-400 bg-green-500/20 border-green-500/30';
      case 'stopped':
        return 'text-red-400 bg-red-500/20 border-red-500/30';
      case 'starting':
        return 'text-yellow-400 bg-yellow-500/20 border-yellow-500/30';
      default:
        return 'text-gray-400 bg-gray-500/20 border-gray-500/30';
    }
  };

  if (authLoading || isLoading) {
    return (
      <ProtectedRoute>
        <div className="min-h-screen text-white">
          <AdminHeader />
          <div className="flex items-center justify-center py-20">
            <div className="text-center fade-in-fast">
              <div className="relative mb-6">
                <div className="animate-spin h-16 w-16 border-4 border-blue-500/30 border-t-blue-500 rounded-full mx-auto"></div>
                <div className="absolute inset-0 animate-ping h-16 w-16 border-4 border-blue-500/20 rounded-full mx-auto"></div>
              </div>
              <h3 className="text-xl font-semibold text-white mb-2">
                {authLoading ? 'Authenticating...' : 'Loading Server Details'}
              </h3>
              <p className="text-gray-300 font-light">
                {authLoading ? 'Please wait while we verify your authentication...' : 'Please wait while we fetch the server information...'}
              </p>
            </div>
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  if (error || !server) {
    return (
      <ProtectedRoute>
        <div className="min-h-screen text-white">
          <AdminHeader />
          <div className="container mx-auto py-12 px-4">
            <div className="card p-8 text-center fade-in-up">
              <div className="w-20 h-20 mx-auto mb-6 rounded-full bg-gradient-to-br from-red-500/20 to-red-600/20 flex items-center justify-center">
                <svg className="h-10 w-10 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              </div>
              <h3 className="text-2xl font-bold text-white mb-4">Server Not Found</h3>
              <p className="text-red-400 mb-8">{error || 'The requested server could not be found.'}</p>
              <button
                onClick={() => router.push('/minecraft/dashboard')}
                className="btn-primary px-8 py-3 font-semibold"
              >
                <svg className="h-5 w-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                Back to Dashboard
              </button>
            </div>
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute>
      <div className="min-h-screen text-white">
        <AdminHeader />

        <main className="container mx-auto py-12 px-4 max-w-7xl">
          <div className="mb-8 fade-in-up">
            <button
              onClick={() => router.push('/minecraft/dashboard')}
              className="text-blue-400 hover:text-blue-300 mb-4 inline-flex items-center font-medium transition-colors"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
              </svg>
              Back to Dashboard
            </button>
          </div>

          {/* Server Info Card */}
          <div className="card mb-8 fade-in-fast">
            <div className="px-8 py-6 border-b border-white/10 bg-gradient-to-r from-blue-500/10 to-purple-500/10">
              <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-6">
                <div>
                  <h1 className="text-4xl font-bold flex flex-col lg:flex-row lg:items-center gap-4">
                    {server.name}
                   <span className={`px-4 py-2 rounded-full text-sm font-semibold border backdrop-blur-sm ${getStatusColor(server.status)} w-fit !text-white`}>
                    {server.status.charAt(0).toUpperCase() + server.status.slice(1)}
                  </span>
                  </h1>
                </div>
                <div className="flex flex-wrap gap-3">
                  {server.status === 'stopped' ? (
                    <button
                      onClick={handleStartServer}
                      className="bg-green-500/20 hover:bg-green-500/30 border border-green-500/30 hover:border-green-500/50 text-green-400 hover:text-green-300 px-6 py-3 rounded-lg font-semibold transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-green-500/20"
                    >
                      <svg className="h-5 w-5 mr-2 inline" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                      </svg>
                      Start Server
                    </button>
                  ) : (
                    <button
                      onClick={handleStopServer}
                      className="bg-red-500/20 hover:bg-red-500/30 border border-red-500/30 hover:border-red-500/50 text-red-400 hover:text-red-300 px-6 py-3 rounded-lg font-semibold transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-red-500/20"
                    >
                      <svg className="h-5 w-5 mr-2 inline" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 00-1 1v4a1 1 0 001 1h4a1 1 0 001-1V8a1 1 0 00-1-1H8z" clipRule="evenodd" />
                      </svg>
                      Stop Server
                    </button>
                  )}
                  <button
                    onClick={handleDeleteServer}
                    className="bg-red-500/20 hover:bg-red-500/30 border border-red-500/30 hover:border-red-500/50 text-red-400 hover:text-red-300 px-6 py-3 rounded-lg font-semibold transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-red-500/20"
                    disabled={isDeleting}
                  >
                    <svg className="h-5 w-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1-1H8a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                    {isDeleting ? 'Deleting...' : 'Delete Server'}
                  </button>
      <ConfirmModal
        open={deleteModalOpen}
        title="Delete Server"
        message={server ? `Are you sure you want to delete the server \"${server.name}\"? This action cannot be undone.` : ''}
        confirmText={isDeleting ? 'Deleting...' : 'Delete'}
        cancelText="Cancel"
        onConfirm={confirmDeleteServer}
        onCancel={() => setDeleteModalOpen(false)}
      />
                </div>

              </div>
              <div className="flex items-center gap-2">
                  Your server ip: <span className="text-white font-semibold">play.blocksconnect.com:{server.port}</span>
                  <button
                    onClick={() => navigator.clipboard.writeText(`play.blocksconnect.com:${server.port}`)}
                    className="text-blue-400 hover:text-blue-300 cursor-pointer transition-colors"
                  >
                    Copy
                  </button>
                </div>
            </div>

            <div className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="bg-blue-500/10 rounded-lg p-3 border border-blue-500/20">
                  <h3 className="text-blue-300 font-semibold mb-1">Port</h3>
                  <p className="text-xl font-bold text-white">{server.port}</p>
                </div>
                <div className="bg-purple-500/10 rounded-lg p-3 border border-purple-500/20">
                  <h3 className="text-purple-300 font-semibold mb-1">Version</h3>
                  <p className="text-xl font-bold text-white">{server.version}</p>
                </div>
                <div className="bg-indigo-500/10 rounded-lg p-3 border border-indigo-500/20">
                  <h3 className="text-indigo-300 font-semibold mb-1">Memory</h3>
                  <p className="text-xl font-bold text-white">{server.memory}</p>
                </div>
                <div className="bg-emerald-500/10 rounded-lg p-3 border border-emerald-500/20">
                  <h3 className="text-emerald-300 font-semibold mb-1">Backup</h3>
                  <div className="flex flex-col space-y-2">
                    <p className="text-xl font-bold text-white">{server.backup ? 'Enabled' : 'Disabled'}</p>
                    <div className="flex flex-wrap gap-2">
                      <button
                        onClick={handleToggleBackup}
                        className={`px-3 py-1.5 rounded-lg text-sm font-semibold transition-all duration-300 hover:scale-105 ${
                          server.backup
                            ? 'bg-blue-500/20 hover:bg-blue-500/30 border border-blue-500/30 hover:border-blue-500/50 text-blue-400 hover:text-blue-300 hover:shadow-lg hover:shadow-blue-500/20'
                            : 'bg-gray-500/20 hover:bg-gray-500/30 border border-gray-500/30 hover:border-gray-500/50 text-gray-400 hover:text-gray-300 hover:shadow-lg hover:shadow-gray-500/20'
                        }`}
                      >
                        Toggle
                      </button>
                      {server.backup && (
                        <button
                          onClick={handleDownloadBackup}
                          className="bg-purple-500/20 hover:bg-purple-500/30 border border-purple-500/30 hover:border-purple-500/50 text-purple-400 hover:text-purple-300 px-3 py-1.5 rounded-lg text-sm font-semibold transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-purple-500/20"
                        >
                          Download
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Tab Navigation */}
          <div className="mb-8">
            <div className="border-b border-white/10">
              <nav className="flex space-x-0 overflow-x-auto">
                {[
                  { id: 'overview', label: 'Overview', icon: '📊' },
                  { id: 'properties', label: 'Properties', icon: '⚙️' },
                  { id: 'players', label: 'Players', icon: '👥' },
                  { id: 'backups', label: 'Backups', icon: '💾' },
                  { id: 'files', label: 'Files', icon: '📁' },
                  { id: 'monitoring', label: 'Monitoring', icon: '📈' },
                  { id: 'console', label: 'Console', icon: '💻' }
                ].map((tab) => (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id as any)}
                    className={`flex items-center px-4 lg:px-6 py-4 text-sm font-medium border-b-2 transition-colors whitespace-nowrap ${
                      activeTab === tab.id
                        ? 'border-blue-500 text-blue-400 bg-blue-500/10'
                        : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'
                    }`}
                  >
                    <span className="mr-2">{tab.icon}</span>
                    {tab.label}
                  </button>
                ))}
              </nav>
            </div>
          </div>

          {/* Tab Content */}
          <div className="fade-in-fast">
            {activeTab === 'overview' && (
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <ServerMonitoring server={server} />
                <div className="space-y-8">
                  <BackupManager
                    server={server}
                    onBackupUpdated={() => fetchServerDetails()}
                  />
                </div>
              </div>
            )}

            {activeTab === 'properties' && (
              <ServerPropertiesEditor
                server={server}
                onPropertiesUpdated={() => fetchServerDetails()}
              />
            )}

            {activeTab === 'players' && (
              <PlayerManager
                server={server}
                onPlayerUpdated={() => fetchServerDetails()}
              />
            )}

            {activeTab === 'backups' && (
              <BackupManager
                server={server}
                onBackupUpdated={() => fetchServerDetails()}
              />
            )}

            {activeTab === 'files' && (
              <FileManager server={server} />
            )}

            {activeTab === 'monitoring' && (
              <ServerMonitoring server={server} />
            )}

            {activeTab === 'console' && (
              <ServerConsole serverId={serverId} isRunning={server.status === 'running'} />
            )}
          </div>
        </main>

        <footer className="relative mt-20">
          <div className="absolute inset-0 bg-gradient-to-r from-blue-900/20 to-purple-900/20 backdrop-blur-sm"></div>
          <div className="relative z-10 container mx-auto text-center py-8 px-4">
            <div className="border-t border-white/10 pt-8">
              <p className="text-sm text-gray-300 font-medium">
                &copy; {new Date().getFullYear()} BlocksConnect
              </p>
            </div>
          </div>
        </footer>
      </div>
    </ProtectedRoute>
  );
}
