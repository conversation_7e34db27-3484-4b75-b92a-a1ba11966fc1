package com.blocksconnect.api.models;

import java.time.Instant;

/**
 * Server event model for API communication
 * Represents events that occur on the Minecraft server
 */
public class ServerEvent {
    private String eventType;
    private String serverId;
    private String playerUuid;
    private Instant timestamp;
    private Object data;
    
    public ServerEvent() {
        this.timestamp = Instant.now();
    }
    
    public ServerEvent(String eventType, String serverId, String playerUuid, Object data) {
        this();
        this.eventType = eventType;
        this.serverId = serverId;
        this.playerUuid = playerUuid;
        this.data = data;
    }
    
    // Getters and setters
    public String getEventType() {
        return eventType;
    }
    
    public void setEventType(String eventType) {
        this.eventType = eventType;
    }
    
    public String getServerId() {
        return serverId;
    }
    
    public void setServerId(String serverId) {
        this.serverId = serverId;
    }
    
    public String getPlayerUuid() {
        return playerUuid;
    }
    
    public void setPlayerUuid(String playerUuid) {
        this.playerUuid = playerUuid;
    }
    
    public Instant getTimestamp() {
        return timestamp;
    }
    
    public void setTimestamp(Instant timestamp) {
        this.timestamp = timestamp;
    }
    
    public Object getData() {
        return data;
    }
    
    public void setData(Object data) {
        this.data = data;
    }
    
    @Override
    public String toString() {
        return "ServerEvent{" +
                "eventType='" + eventType + '\'' +
                ", serverId='" + serverId + '\'' +
                ", playerUuid='" + playerUuid + '\'' +
                ", timestamp=" + timestamp +
                '}';
    }
}
