'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { Server, buildAuthHeaders } from '../services/api';

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';

interface BackupManagerProps {
  server: Server;
  onBackupUpdated: () => void;
}

interface BackupSchedule {
  enabled: boolean;
  frequency: 'hourly' | 'daily' | 'weekly';
  time?: string; // For daily/weekly backups
  dayOfWeek?: number; // For weekly backups (0-6, Sunday-Saturday)
  maxBackups: number;
}

interface BackupFile {
  id: string;
  name: string;
  size: number;
  created: string;
  path: string;
}

export default function BackupManager({ server, onBackupUpdated }: BackupManagerProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [backups, setBackups] = useState<BackupFile[]>([]);
  const [schedule, setSchedule] = useState<BackupSchedule>({
    enabled: false,
    frequency: 'daily',
    time: '02:00',
    maxBackups: 7
  });
  const { getToken } = useAuth();

  useEffect(() => {
    fetchBackups();
    fetchSchedule();
  }, [server.id]);

  const fetchBackups = async () => {
    try {
      const token = await getToken();
      const headers = buildAuthHeaders(token);
      const response = await fetch(`${API_URL}/minecraft/servers/${server.id}/backups`, {
        headers
      });
      
      if (response.ok) {
        const data = await response.json();
        setBackups(data.backups || []);
      }
    } catch (err) {
      console.error('Error fetching backups:', err);
    }
  };

  const fetchSchedule = async () => {
    try {
      const token = await getToken();
      const headers = buildAuthHeaders(token);
      const response = await fetch(`${API_URL}/minecraft/servers/${server.id}/backup-schedule`, {
        headers
      });
      
      if (response.ok) {
        const data = await response.json();
        setSchedule(data.schedule || schedule);
      }
    } catch (err) {
      console.error('Error fetching backup schedule:', err);
    }
  };

  const createBackup = async () => {
    setIsLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const token = await getToken();
      const headers = buildAuthHeaders(token);
      const response = await fetch(`${API_URL}/minecraft/servers/${server.id}/backup`, {
        method: 'POST',
        headers,
        body: JSON.stringify({ manual: true })
      });

      if (response.ok) {
        setSuccess('Backup created successfully!');
        fetchBackups();
        onBackupUpdated();
        setTimeout(() => setSuccess(null), 3000);
      } else {
        const errorData = await response.json();
        setError(errorData.error || 'Failed to create backup');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create backup');
    } finally {
      setIsLoading(false);
    }
  };

  const downloadBackup = async (backupId: string) => {
    try {
      const token = await getToken();
      const headers = buildAuthHeaders(token);
      const response = await fetch(`${API_URL}/minecraft/servers/${server.id}/backups/${backupId}/download`, {
        headers
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${server.name}_backup_${backupId}.tar.gz`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        window.URL.revokeObjectURL(url);
      } else {
        setError('Failed to download backup');
      }
    } catch (err) {
      setError('Failed to download backup');
    }
  };

  const deleteBackup = async (backupId: string) => {
    try {
      const token = await getToken();
      const headers = buildAuthHeaders(token);
      const response = await fetch(`${API_URL}/minecraft/servers/${server.id}/backups/${backupId}`, {
        method: 'DELETE',
        headers
      });

      if (response.ok) {
        setSuccess('Backup deleted successfully!');
        fetchBackups();
        setTimeout(() => setSuccess(null), 3000);
      } else {
        setError('Failed to delete backup');
      }
    } catch (err) {
      setError('Failed to delete backup');
    }
  };

  const updateSchedule = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const token = await getToken();
      const headers = buildAuthHeaders(token);
      const response = await fetch(`${API_URL}/minecraft/servers/${server.id}/backup-schedule`, {
        method: 'PUT',
        headers,
        body: JSON.stringify({ schedule })
      });

      if (response.ok) {
        setSuccess('Backup schedule updated successfully!');
        setTimeout(() => setSuccess(null), 3000);
      } else {
        const errorData = await response.json();
        setError(errorData.error || 'Failed to update schedule');
      }
    } catch (err) {
      setError('Failed to update schedule');
    } finally {
      setIsLoading(false);
    }
  };

  const formatFileSize = (bytes: number) => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  return (
    <div className="card overflow-hidden fade-in-fast">
      <div className="px-6 lg:px-8 py-6 border-b border-white/10 bg-gradient-to-r from-green-500/10 to-emerald-500/10">
        <h2 className="text-xl lg:text-2xl font-bold flex items-center gradient-text">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 lg:h-6 lg:w-6 mr-3 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12" />
          </svg>
          Backup Management
        </h2>
        <p className="text-gray-300 mt-2 font-light text-sm lg:text-base">Manage server backups and scheduling</p>
      </div>

      <div className="p-6 lg:p-8 space-y-8">
        {/* Status Messages */}
        {error && (
          <div className="p-4 bg-red-500/20 border border-red-500/30 rounded-lg text-red-400 flex items-center">
            <svg className="h-5 w-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
            {error}
          </div>
        )}

        {success && (
          <div className="p-4 bg-green-500/20 border border-green-500/30 rounded-lg text-green-400 flex items-center">
            <svg className="h-5 w-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
            </svg>
            {success}
          </div>
        )}

        {/* Manual Backup */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-white">Manual Backup</h3>
          <div className="flex flex-col sm:flex-row gap-4">
            <button
              onClick={createBackup}
              disabled={isLoading}
              className="flex-1 bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 disabled:from-gray-600 disabled:to-gray-600 text-white px-6 py-3 rounded-lg font-semibold transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-green-500/20 disabled:hover:scale-100 disabled:hover:shadow-none disabled:cursor-not-allowed"
            >
              {isLoading ? (
                <div className="flex items-center justify-center">
                  <div className="animate-spin h-5 w-5 border-2 border-white/30 border-t-white rounded-full mr-2"></div>
                  Creating Backup...
                </div>
              ) : (
                <>
                  <svg className="h-5 w-5 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12" />
                  </svg>
                  Create Backup Now
                </>
              )}
            </button>
          </div>
        </div>

        {/* Backup Schedule */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-white">Automatic Backup Schedule</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="flex items-center space-x-3 cursor-pointer mb-4">
                <input
                  type="checkbox"
                  className="form-checkbox h-5 w-5 text-green-500"
                  checked={schedule.enabled}
                  onChange={(e) => setSchedule(prev => ({ ...prev, enabled: e.target.checked }))}
                />
                <span className="text-gray-200 font-medium">Enable Automatic Backups</span>
              </label>

              {schedule.enabled && (
                <div className="space-y-4">
                  <div>
                    <label className="block text-gray-200 text-sm font-semibold mb-3">Frequency</label>
                    <select
                      className="input-field w-full"
                      value={schedule.frequency}
                      onChange={(e) => setSchedule(prev => ({ ...prev, frequency: e.target.value as any }))}
                    >
                      <option value="hourly">Every Hour</option>
                      <option value="daily">Daily</option>
                      <option value="weekly">Weekly</option>
                    </select>
                  </div>

                  {schedule.frequency !== 'hourly' && (
                    <div>
                      <label className="block text-gray-200 text-sm font-semibold mb-3">Time</label>
                      <input
                        type="time"
                        className="input-field w-full"
                        value={schedule.time}
                        onChange={(e) => setSchedule(prev => ({ ...prev, time: e.target.value }))}
                      />
                    </div>
                  )}

                  {schedule.frequency === 'weekly' && (
                    <div>
                      <label className="block text-gray-200 text-sm font-semibold mb-3">Day of Week</label>
                      <select
                        className="input-field w-full"
                        value={schedule.dayOfWeek || 0}
                        onChange={(e) => setSchedule(prev => ({ ...prev, dayOfWeek: parseInt(e.target.value) }))}
                      >
                        <option value={0}>Sunday</option>
                        <option value={1}>Monday</option>
                        <option value={2}>Tuesday</option>
                        <option value={3}>Wednesday</option>
                        <option value={4}>Thursday</option>
                        <option value={5}>Friday</option>
                        <option value={6}>Saturday</option>
                      </select>
                    </div>
                  )}
                </div>
              )}
            </div>

            <div>
              <div>
                <label className="block text-gray-200 text-sm font-semibold mb-3">Max Backups to Keep</label>
                <input
                  type="number"
                  className="input-field w-full"
                  min="1"
                  max="30"
                  value={schedule.maxBackups}
                  onChange={(e) => setSchedule(prev => ({ ...prev, maxBackups: parseInt(e.target.value) }))}
                />
                <p className="text-gray-400 text-xs mt-1">Older backups will be automatically deleted</p>
              </div>

              <button
                onClick={updateSchedule}
                disabled={isLoading}
                className="mt-4 w-full bg-blue-500 hover:bg-blue-600 disabled:bg-gray-600 text-white px-4 py-2 rounded-lg font-semibold transition-all duration-300 disabled:cursor-not-allowed"
              >
                Update Schedule
              </button>
            </div>
          </div>
        </div>

        {/* Backup List */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-white">Existing Backups</h3>
          {backups.length === 0 ? (
            <div className="text-center py-8">
              <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br from-green-500/20 to-emerald-500/20 flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12" />
                </svg>
              </div>
              <h4 className="text-lg font-semibold text-white mb-2">No backups found</h4>
              <p className="text-gray-400 font-light">Create your first backup to get started.</p>
            </div>
          ) : (
            <div className="space-y-3">
              {backups.map((backup) => (
                <div key={backup.id} className="bg-white/5 rounded-lg p-4 flex items-center justify-between">
                  <div>
                    <h4 className="text-white font-medium">{backup.name}</h4>
                    <p className="text-gray-400 text-sm">
                      {formatFileSize(backup.size)} • {new Date(backup.created).toLocaleString()}
                    </p>
                  </div>
                  <div className="flex gap-2">
                    <button
                      onClick={() => downloadBackup(backup.id)}
                      className="bg-blue-500/20 hover:bg-blue-500/30 border border-blue-500/30 hover:border-blue-500/50 text-blue-400 hover:text-blue-300 px-3 py-2 rounded-lg text-sm font-semibold transition-all duration-300 hover:scale-105"
                    >
                      Download
                    </button>
                    <button
                      onClick={() => deleteBackup(backup.id)}
                      className="bg-red-500/20 hover:bg-red-500/30 border border-red-500/30 hover:border-red-500/50 text-red-400 hover:text-red-300 px-3 py-2 rounded-lg text-sm font-semibold transition-all duration-300 hover:scale-105"
                    >
                      Delete
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
