package com.blocksconnect.api.models;

import java.time.Instant;
import java.util.Objects;

/**
 * Player data model for API communication
 * Contains all relevant player information for BlocksConnect integration
 */
public class PlayerData {
    private String uuid;
    private String username;
    private String displayName;
    private String ipAddress;
    private Instant joinTime;
    private Instant lastSeen;
    private boolean isOnline;
    private int gameMode;
    private double health;
    private int foodLevel;
    private int experienceLevel;
    private float experienceProgress;
    private String dimension;
    private double positionX;
    private double positionY;
    private double positionZ;
    private float yaw;
    private float pitch;
    
    public PlayerData() {
        this.joinTime = Instant.now();
        this.lastSeen = Instant.now();
        this.isOnline = true;
    }
    
    public PlayerData(String uuid, String username) {
        this();
        this.uuid = uuid;
        this.username = username;
        this.displayName = username;
    }
    
    // Getters and setters
    public String getUuid() {
        return uuid;
    }
    
    public void setUuid(String uuid) {
        this.uuid = uuid;
    }
    
    public String getUsername() {
        return username;
    }
    
    public void setUsername(String username) {
        this.username = username;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }
    
    public String getIpAddress() {
        return ipAddress;
    }
    
    public void setIpAddress(String ipAddress) {
        this.ipAddress = ipAddress;
    }
    
    public Instant getJoinTime() {
        return joinTime;
    }
    
    public void setJoinTime(Instant joinTime) {
        this.joinTime = joinTime;
    }
    
    public Instant getLastSeen() {
        return lastSeen;
    }
    
    public void setLastSeen(Instant lastSeen) {
        this.lastSeen = lastSeen;
    }
    
    public boolean isOnline() {
        return isOnline;
    }
    
    public void setOnline(boolean online) {
        isOnline = online;
    }
    
    public int getGameMode() {
        return gameMode;
    }
    
    public void setGameMode(int gameMode) {
        this.gameMode = gameMode;
    }
    
    public double getHealth() {
        return health;
    }
    
    public void setHealth(double health) {
        this.health = health;
    }
    
    public int getFoodLevel() {
        return foodLevel;
    }
    
    public void setFoodLevel(int foodLevel) {
        this.foodLevel = foodLevel;
    }
    
    public int getExperienceLevel() {
        return experienceLevel;
    }
    
    public void setExperienceLevel(int experienceLevel) {
        this.experienceLevel = experienceLevel;
    }
    
    public float getExperienceProgress() {
        return experienceProgress;
    }
    
    public void setExperienceProgress(float experienceProgress) {
        this.experienceProgress = experienceProgress;
    }
    
    public String getDimension() {
        return dimension;
    }
    
    public void setDimension(String dimension) {
        this.dimension = dimension;
    }
    
    public double getPositionX() {
        return positionX;
    }
    
    public void setPositionX(double positionX) {
        this.positionX = positionX;
    }
    
    public double getPositionY() {
        return positionY;
    }
    
    public void setPositionY(double positionY) {
        this.positionY = positionY;
    }
    
    public double getPositionZ() {
        return positionZ;
    }
    
    public void setPositionZ(double positionZ) {
        this.positionZ = positionZ;
    }
    
    public float getYaw() {
        return yaw;
    }
    
    public void setYaw(float yaw) {
        this.yaw = yaw;
    }
    
    public float getPitch() {
        return pitch;
    }
    
    public void setPitch(float pitch) {
        this.pitch = pitch;
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        PlayerData that = (PlayerData) o;
        return Objects.equals(uuid, that.uuid);
    }
    
    @Override
    public int hashCode() {
        return Objects.hash(uuid);
    }
    
    @Override
    public String toString() {
        return "PlayerData{" +
                "uuid='" + uuid + '\'' +
                ", username='" + username + '\'' +
                ", displayName='" + displayName + '\'' +
                ", isOnline=" + isOnline +
                '}';
    }
}
