{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/BlocksConnect/web-panel-blocksconnect/src/components/ConfirmModal.tsx"], "sourcesContent": ["import React from 'react';\r\n\r\ninterface ConfirmModalProps {\r\n  open: boolean;\r\n  title?: string;\r\n  message: string;\r\n  confirmText?: string;\r\n  cancelText?: string;\r\n  onConfirm: () => void;\r\n  onCancel: () => void;\r\n}\r\n\r\nexport default function ConfirmModal({\r\n  open,\r\n  title = 'Confirm',\r\n  message,\r\n  confirmText = 'Delete',\r\n  cancelText = 'Cancel',\r\n  onConfirm,\r\n  onCancel,\r\n}: ConfirmModalProps) {\r\n  if (!open) return null;\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 z-50 flex items-center justify-center bg-black/60 backdrop-blur-sm\">\r\n      <div className=\"card p-8 max-w-sm w-full shadow-xl animate-fade-in-up\">\r\n        {title && <h3 className=\"text-xl font-bold mb-4 gradient-text\">{title}</h3>}\r\n        <p className=\"text-gray-200 mb-6\">{message}</p>\r\n        <div className=\"flex justify-end gap-3\">\r\n          <button\r\n            className=\"btn-secondary px-6 py-2\"\r\n            type=\"button\"\r\n            onClick={onCancel}\r\n          >\r\n            {cancelText}\r\n          </button>\r\n          <button\r\n            className=\"btn-primary px-6 py-2\"\r\n            type=\"button\"\r\n            onClick={onConfirm}\r\n          >\r\n            {confirmText}\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAYe,SAAS,aAAa,EACnC,IAAI,EACJ,QAAQ,SAAS,EACjB,OAAO,EACP,cAAc,QAAQ,EACtB,aAAa,QAAQ,EACrB,SAAS,EACT,QAAQ,EACU;IAClB,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;gBACZ,uBAAS,8OAAC;oBAAG,WAAU;8BAAwC;;;;;;8BAChE,8OAAC;oBAAE,WAAU;8BAAsB;;;;;;8BACnC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,WAAU;4BACV,MAAK;4BACL,SAAS;sCAER;;;;;;sCAEH,8OAAC;4BACC,WAAU;4BACV,MAAK;4BACL,SAAS;sCAER;;;;;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/BlocksConnect/web-panel-blocksconnect/src/services/api.ts"], "sourcesContent": ["'use client';\n\nconst API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';\n\n// Configuration\nconst MAX_RETRY_ATTEMPTS = 3;\nconst INITIAL_RETRY_DELAY = 1000; // 1 second\nconst AUTH_ERROR_EVENT = 'auth-error'; // Custom event name for auth errors\n\nexport interface ServerProperties {\n  [key: string]: string;\n}\n\nexport interface PlayerInfo {\n  online: number;\n  max: number;\n  list: string[];\n}\n\nexport interface Server {\n  id: string;\n  name: string;\n  port: number;\n  version: string;\n  memory: string;\n  status: string;\n  backup?: boolean;\n  container_id?: string;\n  properties?: ServerProperties;\n  players?: PlayerInfo;\n}\n\nexport interface ServerFormData {\n  name: string;\n  port: number;\n  version: string;\n  type: string;\n  memory: string;\n  eulaAccepted?: boolean;\n  eulaAcceptedAt?: string;\n}\n\n// Error class for authentication errors to distinguish them from other errors\nexport class AuthenticationError extends Error {\n  status: number;\n\n  constructor(message: string, status: number = 401) {\n    super(message);\n    this.name = 'AuthenticationError';\n    this.status = status;\n  }\n}\n\n// Helper function to emit auth error events for app-wide notification\nexport function emitAuthErrorEvent(error: AuthenticationError): void {\n  if (typeof window !== 'undefined') {\n    const event = new CustomEvent(AUTH_ERROR_EVENT, {\n      detail: { message: error.message, status: error.status }\n    });\n    window.dispatchEvent(event);\n  }\n}\n\n// Setup listener for auth error events\nexport function setupAuthErrorListener(callback: (error: { message: string, status: number }) => void): () => void {\n  if (typeof window === 'undefined') return () => {};\n\n  const listener = (event: Event) => {\n    const customEvent = event as CustomEvent;\n    callback(customEvent.detail);\n  };\n\n  window.addEventListener(AUTH_ERROR_EVENT, listener);\n  return () => window.removeEventListener(AUTH_ERROR_EVENT, listener);\n}\n\n// Helper function to get authentication headers from a token\nexport function buildAuthHeaders(token: string | null): HeadersInit {\n  const headers: HeadersInit = {\n    'Content-Type': 'application/json',\n    'Accept': 'application/json',\n  };\n  if (token) {\n    headers['Authorization'] = `Bearer ${token}`;\n  }\n  return headers;\n}\n\n// Simple fetch wrapper (no hooks)\nexport async function authenticatedFetch<T>(\n  url: string,\n  options: RequestInit\n): Promise<T> {\n  const response = await fetch(url, options);\n  if (response.ok) {\n    if (response.headers.get('Content-Type')?.includes('application/json')) {\n      return await response.json() as T;\n    }\n    return response as unknown as T;\n  }\n  // Handle authentication errors (401 Unauthorized)\n  if (response.status === 401) {\n    const errorData = await response.json().catch(() => ({ error: 'Authentication failed' }));\n    const authError = new AuthenticationError(\n      errorData.error || `Authentication failed: ${response.status} ${response.statusText}`,\n      response.status\n    );\n    emitAuthErrorEvent(authError);\n    throw authError;\n  }\n  // Handle other error responses\n  const errorData = await response.json().catch(() => ({ error: `Request failed: ${response.status} ${response.statusText}` }));\n  throw new Error(errorData.error || `Request failed: ${response.status} ${response.statusText}`);\n}\n\nexport async function getServers(token: string | null): Promise<Server[]> {\n  try {\n    const headers = buildAuthHeaders(token);\n    return await authenticatedFetch<Server[]>(`${API_URL}/minecraft/servers`, {\n      method: 'GET',\n      headers,\n      cache: 'no-store',\n    });\n  } catch (error) {\n    console.error('Error fetching servers:', error);\n    if (error instanceof AuthenticationError) {\n      console.error('Authentication error while fetching servers');\n    }\n    throw error;\n  }\n}\n\nexport async function createServer(data: ServerFormData, token: string | null): Promise<Server> {\n  try {\n    const headers = buildAuthHeaders(token);\n    return await authenticatedFetch<Server>(`${API_URL}/minecraft/servers`, {\n      method: 'POST',\n      headers,\n      body: JSON.stringify(data),\n    });\n  } catch (error) {\n    console.error('Error creating server:', error);\n    if (error instanceof AuthenticationError) {\n      console.error('Authentication error while creating server');\n    }\n    throw error;\n  }\n}\n\nexport async function startServer(serverId: string, token: string | null): Promise<{ status: string; message: string }> {\n  try {\n    const headers = buildAuthHeaders(token);\n    return await authenticatedFetch<{ status: string; message: string }>(`${API_URL}/minecraft/servers/${serverId}/start`, {\n      method: 'POST',\n      headers: {\n        ...headers,\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({}),\n    });\n  } catch (error) {\n    console.error(`Error starting server ${serverId}:`, error);\n    if (error instanceof AuthenticationError) {\n      console.error(`Authentication error while starting server ${serverId}`);\n    }\n    throw error;\n  }\n}\n\nexport async function stopServer(serverId: string, token: string | null): Promise<{ status: string; message: string }> {\n  try {\n    const headers = buildAuthHeaders(token);\n    return await authenticatedFetch<{ status: string; message: string }>(`${API_URL}/minecraft/servers/${serverId}/stop`, {\n      method: 'POST',\n      headers: {\n        ...headers,\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({}),\n    });\n  } catch (error) {\n    console.error(`Error stopping server ${serverId}:`, error);\n    if (error instanceof AuthenticationError) {\n      console.error(`Authentication error while stopping server ${serverId}`);\n    }\n    throw error;\n  }\n}\n\nexport async function deleteServer(serverId: string, token: string | null): Promise<{ message: string }> {\n  try {\n    const headers = buildAuthHeaders(token);\n    return await authenticatedFetch<{ message: string }>(`${API_URL}/minecraft/servers/${serverId}`, {\n      method: 'DELETE',\n      headers: {\n        ...headers,\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({}),\n    });\n  } catch (error) {\n    console.error(`Error deleting server ${serverId}:`, error);\n    if (error instanceof AuthenticationError) {\n      console.error(`Authentication error while deleting server ${serverId}`);\n    }\n    throw error;\n  }\n}\n\nexport async function toggleBackup(serverId: string, token: string | null): Promise<{ message: string }> {\n  try {\n    const headers = buildAuthHeaders(token);\n    return await authenticatedFetch<{ message: string }>(`${API_URL}/minecraft/servers/${serverId}/backup`, {\n      method: 'POST',\n      headers: {\n        ...headers,\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({}),\n    });\n  } catch (error) {\n    console.error(`Error toggling backup for server ${serverId}:`, error);\n    if (error instanceof AuthenticationError) {\n      console.error(`Authentication error while toggling backup for server ${serverId}`);\n    }\n    throw error;\n  }\n}\n\nexport async function downloadBackup(serverId: string, token: string | null): Promise<void> {\n  try {\n    const headers = buildAuthHeaders(token);\n    const response = await authenticatedFetch<Response>(`${API_URL}/minecraft/servers/${serverId}/backup`, {\n      method: 'GET',\n      headers,\n    });\n    if (response instanceof Response) {\n      const blob = await response.blob();\n      const url = window.URL.createObjectURL(blob);\n      const a = document.createElement('a');\n      a.href = url;\n      a.download = `server_${serverId}_backup.zip`;\n      document.body.appendChild(a);\n      a.click();\n      document.body.removeChild(a);\n      window.URL.revokeObjectURL(url);\n    } else {\n      throw new Error('Unexpected response type');\n    }\n  } catch (error) {\n    console.error(`Error downloading backup for server ${serverId}:`, error);\n    if (error instanceof AuthenticationError) {\n      console.error(`Authentication error while downloading backup for server ${serverId}`);\n    }\n    throw error;\n  }\n}\n\nexport async function getServerDetails(serverId: string, token: string | null): Promise<Server> {\n  try {\n    const headers = buildAuthHeaders(token);\n    return await authenticatedFetch<Server>(`${API_URL}/minecraft/servers/${serverId}`, {\n      method: 'GET',\n      headers,\n      cache: 'no-store',\n    });\n  } catch (error) {\n    console.error(`Error fetching server details for ${serverId}:`, error);\n    if (error instanceof AuthenticationError) {\n      console.error(`Authentication error while fetching server details for ${serverId}`);\n    }\n    throw error;\n  }\n}\n\nexport async function getServerLogs(serverId: string, token: string | null): Promise<{ logs: string }> {\n  try {\n    const headers = buildAuthHeaders(token);\n    return await authenticatedFetch<{ logs: string }>(`${API_URL}/minecraft/servers/${serverId}/logs`, {\n      method: 'GET',\n      headers,\n      cache: 'no-store',\n    });\n  } catch (error) {\n    console.error(`Error fetching logs for server ${serverId}:`, error);\n    if (error instanceof AuthenticationError) {\n      console.error(`Authentication error while fetching logs for server ${serverId}`);\n    }\n    throw error;\n  }\n}\n\nexport async function sendServerCommand(serverId: string, command: string, token: string | null): Promise<{ response: string }> {\n  try {\n    const headers = buildAuthHeaders(token);\n    return await authenticatedFetch<{ response: string }>(`${API_URL}/minecraft/servers/${serverId}/command`, {\n      method: 'POST',\n      headers,\n      body: JSON.stringify({ command }),\n    });\n  } catch (error) {\n    console.error(`Error sending command to server ${serverId}:`, error);\n    if (error instanceof AuthenticationError) {\n      console.error(`Authentication error while sending command to server ${serverId}`);\n    }\n    throw error;\n  }\n}\n\nexport interface ServerPropertiesUpdate {\n  properties: { [key: string]: string | boolean | number };\n  memory?: string;\n}\n\nexport async function updateServerProperties(serverId: string, properties: ServerPropertiesUpdate, token: string | null): Promise<{ message: string }> {\n  try {\n    const headers = buildAuthHeaders(token);\n    return await authenticatedFetch<{ message: string }>(`${API_URL}/minecraft/servers/${serverId}/properties`, {\n      method: 'PUT',\n      headers,\n      body: JSON.stringify(properties),\n    });\n  } catch (error) {\n    console.error(`Error updating properties for server ${serverId}:`, error);\n    if (error instanceof AuthenticationError) {\n      console.error(`Authentication error while updating properties for server ${serverId}`);\n    }\n    throw error;\n  }\n}\n\n// Enhanced helper function to handle auth errors at the component level\nexport function handleApiAuthError(error: unknown, onAuthError?: () => void): boolean {\n  if (error instanceof AuthenticationError) {\n    console.error('Authentication error occurred:', error.message);\n\n    // Emit auth error event\n    emitAuthErrorEvent(error);\n\n    // If a callback is provided, execute it\n    if (onAuthError) {\n      onAuthError();\n    }\n\n    return true;\n  }\n  return false;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA;AAEA,MAAM,UAAU,iEAAmC;AAEnD,gBAAgB;AAChB,MAAM,qBAAqB;AAC3B,MAAM,sBAAsB,MAAM,WAAW;AAC7C,MAAM,mBAAmB,cAAc,oCAAoC;AAoCpE,MAAM,4BAA4B;IACvC,OAAe;IAEf,YAAY,OAAe,EAAE,SAAiB,GAAG,CAAE;QACjD,KAAK,CAAC;QACN,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,MAAM,GAAG;IAChB;AACF;AAGO,SAAS,mBAAmB,KAA0B;IAC3D,uCAAmC;;IAKnC;AACF;AAGO,SAAS,uBAAuB,QAA8D;IACnG,wCAAmC,OAAO,KAAO;;IAEjD,MAAM;AAOR;AAGO,SAAS,iBAAiB,KAAoB;IACnD,MAAM,UAAuB;QAC3B,gBAAgB;QAChB,UAAU;IACZ;IACA,IAAI,OAAO;QACT,OAAO,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,OAAO;IAC9C;IACA,OAAO;AACT;AAGO,eAAe,mBACpB,GAAW,EACX,OAAoB;IAEpB,MAAM,WAAW,MAAM,MAAM,KAAK;IAClC,IAAI,SAAS,EAAE,EAAE;QACf,IAAI,SAAS,OAAO,CAAC,GAAG,CAAC,iBAAiB,SAAS,qBAAqB;YACtE,OAAO,MAAM,SAAS,IAAI;QAC5B;QACA,OAAO;IACT;IACA,kDAAkD;IAClD,IAAI,SAAS,MAAM,KAAK,KAAK;QAC3B,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC;gBAAE,OAAO;YAAwB,CAAC;QACvF,MAAM,YAAY,IAAI,oBACpB,UAAU,KAAK,IAAI,CAAC,uBAAuB,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE,EACrF,SAAS,MAAM;QAEjB,mBAAmB;QACnB,MAAM;IACR;IACA,+BAA+B;IAC/B,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC;YAAE,OAAO,CAAC,gBAAgB,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;QAAC,CAAC;IAC3H,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI,CAAC,gBAAgB,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;AAChG;AAEO,eAAe,WAAW,KAAoB;IACnD,IAAI;QACF,MAAM,UAAU,iBAAiB;QACjC,OAAO,MAAM,mBAA6B,GAAG,QAAQ,kBAAkB,CAAC,EAAE;YACxE,QAAQ;YACR;YACA,OAAO;QACT;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,IAAI,iBAAiB,qBAAqB;YACxC,QAAQ,KAAK,CAAC;QAChB;QACA,MAAM;IACR;AACF;AAEO,eAAe,aAAa,IAAoB,EAAE,KAAoB;IAC3E,IAAI;QACF,MAAM,UAAU,iBAAiB;QACjC,OAAO,MAAM,mBAA2B,GAAG,QAAQ,kBAAkB,CAAC,EAAE;YACtE,QAAQ;YACR;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,IAAI,iBAAiB,qBAAqB;YACxC,QAAQ,KAAK,CAAC;QAChB;QACA,MAAM;IACR;AACF;AAEO,eAAe,YAAY,QAAgB,EAAE,KAAoB;IACtE,IAAI;QACF,MAAM,UAAU,iBAAiB;QACjC,OAAO,MAAM,mBAAwD,GAAG,QAAQ,mBAAmB,EAAE,SAAS,MAAM,CAAC,EAAE;YACrH,QAAQ;YACR,SAAS;gBACP,GAAG,OAAO;gBACV,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC,CAAC;QACxB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,sBAAsB,EAAE,SAAS,CAAC,CAAC,EAAE;QACpD,IAAI,iBAAiB,qBAAqB;YACxC,QAAQ,KAAK,CAAC,CAAC,2CAA2C,EAAE,UAAU;QACxE;QACA,MAAM;IACR;AACF;AAEO,eAAe,WAAW,QAAgB,EAAE,KAAoB;IACrE,IAAI;QACF,MAAM,UAAU,iBAAiB;QACjC,OAAO,MAAM,mBAAwD,GAAG,QAAQ,mBAAmB,EAAE,SAAS,KAAK,CAAC,EAAE;YACpH,QAAQ;YACR,SAAS;gBACP,GAAG,OAAO;gBACV,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC,CAAC;QACxB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,sBAAsB,EAAE,SAAS,CAAC,CAAC,EAAE;QACpD,IAAI,iBAAiB,qBAAqB;YACxC,QAAQ,KAAK,CAAC,CAAC,2CAA2C,EAAE,UAAU;QACxE;QACA,MAAM;IACR;AACF;AAEO,eAAe,aAAa,QAAgB,EAAE,KAAoB;IACvE,IAAI;QACF,MAAM,UAAU,iBAAiB;QACjC,OAAO,MAAM,mBAAwC,GAAG,QAAQ,mBAAmB,EAAE,UAAU,EAAE;YAC/F,QAAQ;YACR,SAAS;gBACP,GAAG,OAAO;gBACV,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC,CAAC;QACxB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,sBAAsB,EAAE,SAAS,CAAC,CAAC,EAAE;QACpD,IAAI,iBAAiB,qBAAqB;YACxC,QAAQ,KAAK,CAAC,CAAC,2CAA2C,EAAE,UAAU;QACxE;QACA,MAAM;IACR;AACF;AAEO,eAAe,aAAa,QAAgB,EAAE,KAAoB;IACvE,IAAI;QACF,MAAM,UAAU,iBAAiB;QACjC,OAAO,MAAM,mBAAwC,GAAG,QAAQ,mBAAmB,EAAE,SAAS,OAAO,CAAC,EAAE;YACtG,QAAQ;YACR,SAAS;gBACP,GAAG,OAAO;gBACV,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC,CAAC;QACxB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,iCAAiC,EAAE,SAAS,CAAC,CAAC,EAAE;QAC/D,IAAI,iBAAiB,qBAAqB;YACxC,QAAQ,KAAK,CAAC,CAAC,sDAAsD,EAAE,UAAU;QACnF;QACA,MAAM;IACR;AACF;AAEO,eAAe,eAAe,QAAgB,EAAE,KAAoB;IACzE,IAAI;QACF,MAAM,UAAU,iBAAiB;QACjC,MAAM,WAAW,MAAM,mBAA6B,GAAG,QAAQ,mBAAmB,EAAE,SAAS,OAAO,CAAC,EAAE;YACrG,QAAQ;YACR;QACF;QACA,IAAI,oBAAoB,UAAU;YAChC,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC;YACvC,MAAM,IAAI,SAAS,aAAa,CAAC;YACjC,EAAE,IAAI,GAAG;YACT,EAAE,QAAQ,GAAG,CAAC,OAAO,EAAE,SAAS,WAAW,CAAC;YAC5C,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,EAAE,KAAK;YACP,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,OAAO,GAAG,CAAC,eAAe,CAAC;QAC7B,OAAO;YACL,MAAM,IAAI,MAAM;QAClB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,oCAAoC,EAAE,SAAS,CAAC,CAAC,EAAE;QAClE,IAAI,iBAAiB,qBAAqB;YACxC,QAAQ,KAAK,CAAC,CAAC,yDAAyD,EAAE,UAAU;QACtF;QACA,MAAM;IACR;AACF;AAEO,eAAe,iBAAiB,QAAgB,EAAE,KAAoB;IAC3E,IAAI;QACF,MAAM,UAAU,iBAAiB;QACjC,OAAO,MAAM,mBAA2B,GAAG,QAAQ,mBAAmB,EAAE,UAAU,EAAE;YAClF,QAAQ;YACR;YACA,OAAO;QACT;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,kCAAkC,EAAE,SAAS,CAAC,CAAC,EAAE;QAChE,IAAI,iBAAiB,qBAAqB;YACxC,QAAQ,KAAK,CAAC,CAAC,uDAAuD,EAAE,UAAU;QACpF;QACA,MAAM;IACR;AACF;AAEO,eAAe,cAAc,QAAgB,EAAE,KAAoB;IACxE,IAAI;QACF,MAAM,UAAU,iBAAiB;QACjC,OAAO,MAAM,mBAAqC,GAAG,QAAQ,mBAAmB,EAAE,SAAS,KAAK,CAAC,EAAE;YACjG,QAAQ;YACR;YACA,OAAO;QACT;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,+BAA+B,EAAE,SAAS,CAAC,CAAC,EAAE;QAC7D,IAAI,iBAAiB,qBAAqB;YACxC,QAAQ,KAAK,CAAC,CAAC,oDAAoD,EAAE,UAAU;QACjF;QACA,MAAM;IACR;AACF;AAEO,eAAe,kBAAkB,QAAgB,EAAE,OAAe,EAAE,KAAoB;IAC7F,IAAI;QACF,MAAM,UAAU,iBAAiB;QACjC,OAAO,MAAM,mBAAyC,GAAG,QAAQ,mBAAmB,EAAE,SAAS,QAAQ,CAAC,EAAE;YACxG,QAAQ;YACR;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE;YAAQ;QACjC;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,gCAAgC,EAAE,SAAS,CAAC,CAAC,EAAE;QAC9D,IAAI,iBAAiB,qBAAqB;YACxC,QAAQ,KAAK,CAAC,CAAC,qDAAqD,EAAE,UAAU;QAClF;QACA,MAAM;IACR;AACF;AAOO,eAAe,uBAAuB,QAAgB,EAAE,UAAkC,EAAE,KAAoB;IACrH,IAAI;QACF,MAAM,UAAU,iBAAiB;QACjC,OAAO,MAAM,mBAAwC,GAAG,QAAQ,mBAAmB,EAAE,SAAS,WAAW,CAAC,EAAE;YAC1G,QAAQ;YACR;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,qCAAqC,EAAE,SAAS,CAAC,CAAC,EAAE;QACnE,IAAI,iBAAiB,qBAAqB;YACxC,QAAQ,KAAK,CAAC,CAAC,0DAA0D,EAAE,UAAU;QACvF;QACA,MAAM;IACR;AACF;AAGO,SAAS,mBAAmB,KAAc,EAAE,WAAwB;IACzE,IAAI,iBAAiB,qBAAqB;QACxC,QAAQ,KAAK,CAAC,kCAAkC,MAAM,OAAO;QAE7D,wBAAwB;QACxB,mBAAmB;QAEnB,wCAAwC;QACxC,IAAI,aAAa;YACf;QACF;QAEA,OAAO;IACT;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 379, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/BlocksConnect/web-panel-blocksconnect/src/components/ServerConsole.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useRef } from 'react';\nimport { getServerLogs, sendServerCommand } from '../services/api';\nimport { useAuth } from '../contexts/AuthContext';\n\ninterface ServerConsoleProps {\n  serverId: string;\n  isRunning: boolean;\n}\n\nexport default function ServerConsole({ serverId, isRunning }: ServerConsoleProps) {\n  const [logs, setLogs] = useState<string>('');\n  const [command, setCommand] = useState<string>('');\n  const [isLoading, setIsLoading] = useState<boolean>(false);\n  const [error, setError] = useState<string | null>(null);\n  const [lastLogLength, setLastLogLength] = useState<number>(0);\n  const consoleRef = useRef<HTMLDivElement>(null);\n  const { user, isLoading: authLoading, getToken } = useAuth();\n\n  // Helper function to process and normalize log text\n  const processLogs = (rawLogs: string): string => {\n    if (!rawLogs) return '';\n\n    // First, let's handle various line ending formats\n    let processedText = rawLogs\n      .replace(/\\r\\n/g, '\\n')  // Convert Windows line endings\n      .replace(/\\r/g, '\\n');   // Convert old Mac line endings\n\n    // Handle specific patterns we see in the logs that are concatenated\n    // Based on the screenshot, we need to split on specific patterns\n    processedText = processedText\n      // Split before [init] when it appears after other content\n      .replace(/([^\\n])\\[init\\]/g, '$1\\n[init]')\n      // Split before timestamp patterns like [13:51:21]\n      .replace(/([^\\n])\\[(\\d{2}:\\d{2}:\\d{2})\\]/g, '$1\\n[$2]')\n      // Split before [Server thread/INFO], [Server thread/WARN], [Server thread/ERROR]\n      .replace(/([^\\n])\\[Server thread\\/(INFO|WARN|ERROR)\\]/g, '$1\\n[Server thread/$1]')\n      // Split on common log separators that might be missing newlines\n      .replace(/(\\w+)\\s*,\\s*\\[/g, '$1\\n[')\n      // Handle cases where logs are separated by commas followed by brackets\n      .replace(/,\\s*\\[/g, '\\n[');\n\n    // Split into lines and clean up\n    const lines = processedText\n      .split('\\n')\n      .map(line => line.trim())  // Trim whitespace from each line\n      .filter(line => line.length > 0)  // Remove empty lines\n      .filter(line => line !== '');  // Remove lines that are just whitespace\n\n    return lines.join('\\n');\n  };\n\n  // Fetch logs on component mount and when server status changes\n  useEffect(() => {\n    const fetchLogs = async () => {\n      // Don't fetch if auth is still loading or user is not authenticated\n      if (authLoading || !user) {\n        return;\n      }\n\n      try {\n        const token = await getToken()\n        const response = await getServerLogs(serverId, token);\n        const rawLogs = response.logs ? String(response.logs) : '';\n\n        // Debug: Log the raw data to console to see what we're getting\n        if (rawLogs && rawLogs.length > 0) {\n          console.log('Raw logs received:', {\n            length: rawLogs.length,\n            hasNewlines: rawLogs.includes('\\n'),\n            hasCarriageReturns: rawLogs.includes('\\r'),\n            firstChars: rawLogs.substring(0, 200),\n            sample: rawLogs.substring(0, 500)\n          });\n        }\n\n        if (rawLogs) {\n          const processedLogs = processLogs(rawLogs);\n          const currentLength = processedLogs.length;\n\n          // Debug: Log processed data\n          console.log('Processed logs:', {\n            originalLength: rawLogs.length,\n            processedLength: processedLogs.length,\n            lineCount: processedLogs.split('\\n').length,\n            firstProcessedLines: processedLogs.split('\\n').slice(0, 5)\n          });\n\n          // Only update if logs have changed to prevent unnecessary re-renders\n          if (currentLength !== lastLogLength) {\n            setLogs(processedLogs);\n            setLastLogLength(currentLength);\n          }\n        } else {\n          setLogs('No logs available');\n          setLastLogLength(0);\n        }\n\n        setError(null);\n      } catch (err) {\n        setError(err instanceof Error ? err.message : 'Failed to fetch logs');\n        console.error('Error fetching logs:', err);\n      }\n    };\n\n    fetchLogs();\n\n    // Set up polling for logs if server is running\n    let interval: NodeJS.Timeout | null = null;\n    if (isRunning && user && !authLoading) {\n      interval = setInterval(fetchLogs, 5000); // Poll every 5 seconds\n    }\n\n    return () => {\n      if (interval) {\n        clearInterval(interval);\n      }\n    };\n  }, [serverId, isRunning, user, authLoading, lastLogLength, processLogs, getToken]);\n\n  // Auto-scroll to bottom when logs update\n  useEffect(() => {\n    if (consoleRef.current) {\n      consoleRef.current.scrollTop = consoleRef.current.scrollHeight;\n    }\n  }, [logs]);\n\n  const handleSendCommand = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    if (!command.trim() || !isRunning) return;\n\n    setIsLoading(true);\n\n    try {\n      const token = await getToken()\n      const response = await sendServerCommand(serverId, command, token);\n\n      // Add the command and response to the logs with proper formatting\n      setLogs(prev => {\n        const commandEntry = `> ${command}`;\n        const responseEntry = response.response ? processLogs(response.response) : '';\n\n        // Combine previous logs with new command and response\n        const newEntries = [commandEntry];\n        if (responseEntry) {\n          newEntries.push(responseEntry);\n        }\n\n        const combinedLogs = prev ? `${prev}\\n${newEntries.join('\\n')}` : newEntries.join('\\n');\n        const processedCombined = processLogs(combinedLogs);\n\n        // Update the length tracker\n        setLastLogLength(processedCombined.length);\n\n        return processedCombined;\n      });\n\n      // Clear the command input\n      setCommand('');\n      setError(null);\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Failed to send command');\n      console.error('Error sending command:', err);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  return (\n    <div className=\"card overflow-hidden\">\n      <div className=\"px-8 py-6 border-b border-white/10 bg-gradient-to-r from-blue-500/10 to-purple-500/10\">\n        <h3 className=\"text-xl font-bold flex items-center gradient-text\">\n          <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6 mr-3 text-blue-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n          </svg>\n          Server Console\n        </h3>\n        <p className=\"text-gray-300 mt-2 font-light\">\n          {isRunning ? 'Interactive console for server management' : 'Server is not running. Start the server to send commands.'}\n        </p>\n      </div>\n\n      {error && (\n        <div className=\"bg-red-500/10 border-l-4 border-red-500 p-6 m-6 rounded-lg backdrop-blur-sm\">\n          <div className=\"flex\">\n            <div className=\"flex-shrink-0\">\n              <svg className=\"h-6 w-6 text-red-400\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\n              </svg>\n            </div>\n            <div className=\"ml-3\">\n              <h4 className=\"text-red-400 font-semibold\">Console Error</h4>\n              <p className=\"text-sm text-red-300 mt-1\">{error}</p>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Console Output */}\n      <div\n        ref={consoleRef}\n        className=\"bg-black/50 text-green-400 font-mono text-sm p-6 h-96 overflow-y-auto border border-white/5 m-6 rounded-lg backdrop-blur-sm\"\n        style={{ fontFamily: 'JetBrains Mono, Consolas, Monaco, \"Courier New\", monospace' }}\n      >\n        <div className=\"flex items-center mb-4 pb-2 border-b border-green-500/20\">\n          <div className=\"flex space-x-2\">\n            <div className=\"w-3 h-3 rounded-full bg-red-500\"></div>\n            <div className=\"w-3 h-3 rounded-full bg-yellow-500\"></div>\n            <div className=\"w-3 h-3 rounded-full bg-green-500\"></div>\n          </div>\n          <span className=\"ml-4 text-green-300 text-xs font-semibold\">Server Console</span>\n        </div>\n        <pre className=\"whitespace-pre-wrap break-words text-green-300 leading-relaxed\">\n          {logs ? logs.split('\\n').map((line, index) => {\n            // Style command lines differently\n            const isCommand = line.startsWith('> ');\n            const isServerInfo = line.includes('[Server thread/INFO]');\n            const isServerWarn = line.includes('[Server thread/WARN]');\n            const isServerError = line.includes('[Server thread/ERROR]');\n\n            let lineClass = 'block';\n            if (isCommand) {\n              lineClass += ' text-yellow-300 font-semibold';\n            } else if (isServerError) {\n              lineClass += ' text-red-300';\n            } else if (isServerWarn) {\n              lineClass += ' text-orange-300';\n            } else if (isServerInfo) {\n              lineClass += ' text-blue-300';\n            }\n\n            return (\n              <span key={index} className={lineClass}>\n                {line || '\\u00A0'} {/* Non-breaking space for empty lines */}\n              </span>\n            );\n          }) : (\n            <span className=\"text-gray-500 italic\">\n              {isRunning ? 'Loading logs...' : 'Server is not running. No logs available.'}\n            </span>\n          )}\n        </pre>\n      </div>\n\n      {/* Command Input */}\n      <form onSubmit={handleSendCommand} className=\"flex items-center bg-black/30 border-t border-white/10 backdrop-blur-sm\">\n        <div className=\"text-green-400 px-6 py-4 select-none font-mono font-bold text-lg\">&gt;</div>\n        <input\n          type=\"text\"\n          value={command}\n          onChange={(e) => setCommand(e.target.value)}\n          placeholder={isRunning ? \"Type a command...\" : \"Server is not running\"}\n          disabled={!isRunning || isLoading}\n          className=\"flex-1 bg-transparent text-white px-2 py-4 focus:outline-none font-mono placeholder-gray-500 focus:placeholder-gray-400\"\n        />\n        <button\n          type=\"submit\"\n          disabled={!isRunning || isLoading || !command.trim()}\n          className=\"btn-primary mx-4 my-2 px-6 py-2 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none\"\n        >\n          {isLoading ? (\n            <svg className=\"animate-spin h-4 w-4\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n              <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n              <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n            </svg>\n          ) : (\n            <span className=\"flex items-center\">\n              <svg className=\"h-4 w-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 19l9 2-9-18-9 18 9-2zm0 0v-8\" />\n              </svg>\n              Send\n            </span>\n          )}\n        </button>\n      </form>\n    </div>\n  );\n}\n\n\n\n\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAWe,SAAS,cAAc,EAAE,QAAQ,EAAE,SAAS,EAAsB;IAC/E,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IACpD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC3D,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC1C,MAAM,EAAE,IAAI,EAAE,WAAW,WAAW,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEzD,oDAAoD;IACpD,MAAM,cAAc,CAAC;QACnB,IAAI,CAAC,SAAS,OAAO;QAErB,kDAAkD;QAClD,IAAI,gBAAgB,QACjB,OAAO,CAAC,SAAS,MAAO,+BAA+B;SACvD,OAAO,CAAC,OAAO,OAAS,+BAA+B;QAE1D,oEAAoE;QACpE,iEAAiE;QACjE,gBAAgB,aACd,0DAA0D;SACzD,OAAO,CAAC,oBAAoB,aAC7B,kDAAkD;SACjD,OAAO,CAAC,mCAAmC,WAC5C,iFAAiF;SAChF,OAAO,CAAC,gDAAgD,yBACzD,gEAAgE;SAC/D,OAAO,CAAC,mBAAmB,QAC5B,uEAAuE;SACtE,OAAO,CAAC,WAAW;QAEtB,gCAAgC;QAChC,MAAM,QAAQ,cACX,KAAK,CAAC,MACN,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI,IAAK,iCAAiC;SAC3D,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,GAAG,GAAI,qBAAqB;SACtD,MAAM,CAAC,CAAA,OAAQ,SAAS,KAAM,wCAAwC;QAEzE,OAAO,MAAM,IAAI,CAAC;IACpB;IAEA,+DAA+D;IAC/D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY;YAChB,oEAAoE;YACpE,IAAI,eAAe,CAAC,MAAM;gBACxB;YACF;YAEA,IAAI;gBACF,MAAM,QAAQ,MAAM;gBACpB,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,gBAAa,AAAD,EAAE,UAAU;gBAC/C,MAAM,UAAU,SAAS,IAAI,GAAG,OAAO,SAAS,IAAI,IAAI;gBAExD,+DAA+D;gBAC/D,IAAI,WAAW,QAAQ,MAAM,GAAG,GAAG;oBACjC,QAAQ,GAAG,CAAC,sBAAsB;wBAChC,QAAQ,QAAQ,MAAM;wBACtB,aAAa,QAAQ,QAAQ,CAAC;wBAC9B,oBAAoB,QAAQ,QAAQ,CAAC;wBACrC,YAAY,QAAQ,SAAS,CAAC,GAAG;wBACjC,QAAQ,QAAQ,SAAS,CAAC,GAAG;oBAC/B;gBACF;gBAEA,IAAI,SAAS;oBACX,MAAM,gBAAgB,YAAY;oBAClC,MAAM,gBAAgB,cAAc,MAAM;oBAE1C,4BAA4B;oBAC5B,QAAQ,GAAG,CAAC,mBAAmB;wBAC7B,gBAAgB,QAAQ,MAAM;wBAC9B,iBAAiB,cAAc,MAAM;wBACrC,WAAW,cAAc,KAAK,CAAC,MAAM,MAAM;wBAC3C,qBAAqB,cAAc,KAAK,CAAC,MAAM,KAAK,CAAC,GAAG;oBAC1D;oBAEA,qEAAqE;oBACrE,IAAI,kBAAkB,eAAe;wBACnC,QAAQ;wBACR,iBAAiB;oBACnB;gBACF,OAAO;oBACL,QAAQ;oBACR,iBAAiB;gBACnB;gBAEA,SAAS;YACX,EAAE,OAAO,KAAK;gBACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;gBAC9C,QAAQ,KAAK,CAAC,wBAAwB;YACxC;QACF;QAEA;QAEA,+CAA+C;QAC/C,IAAI,WAAkC;QACtC,IAAI,aAAa,QAAQ,CAAC,aAAa;YACrC,WAAW,YAAY,WAAW,OAAO,uBAAuB;QAClE;QAEA,OAAO;YACL,IAAI,UAAU;gBACZ,cAAc;YAChB;QACF;IACF,GAAG;QAAC;QAAU;QAAW;QAAM;QAAa;QAAe;QAAa;KAAS;IAEjF,yCAAyC;IACzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW,OAAO,EAAE;YACtB,WAAW,OAAO,CAAC,SAAS,GAAG,WAAW,OAAO,CAAC,YAAY;QAChE;IACF,GAAG;QAAC;KAAK;IAET,MAAM,oBAAoB,OAAO;QAC/B,EAAE,cAAc;QAEhB,IAAI,CAAC,QAAQ,IAAI,MAAM,CAAC,WAAW;QAEnC,aAAa;QAEb,IAAI;YACF,MAAM,QAAQ,MAAM;YACpB,MAAM,WAAW,MAAM,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,UAAU,SAAS;YAE5D,kEAAkE;YAClE,QAAQ,CAAA;gBACN,MAAM,eAAe,CAAC,EAAE,EAAE,SAAS;gBACnC,MAAM,gBAAgB,SAAS,QAAQ,GAAG,YAAY,SAAS,QAAQ,IAAI;gBAE3E,sDAAsD;gBACtD,MAAM,aAAa;oBAAC;iBAAa;gBACjC,IAAI,eAAe;oBACjB,WAAW,IAAI,CAAC;gBAClB;gBAEA,MAAM,eAAe,OAAO,GAAG,KAAK,EAAE,EAAE,WAAW,IAAI,CAAC,OAAO,GAAG,WAAW,IAAI,CAAC;gBAClF,MAAM,oBAAoB,YAAY;gBAEtC,4BAA4B;gBAC5B,iBAAiB,kBAAkB,MAAM;gBAEzC,OAAO;YACT;YAEA,0BAA0B;YAC1B,WAAW;YACX,SAAS;QACX,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC9C,QAAQ,KAAK,CAAC,0BAA0B;QAC1C,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC;gCAAI,OAAM;gCAA6B,WAAU;gCAA6B,MAAK;gCAAO,SAAQ;gCAAY,QAAO;0CACpH,cAAA,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;4BACjE;;;;;;;kCAGR,8OAAC;wBAAE,WAAU;kCACV,YAAY,8CAA8C;;;;;;;;;;;;YAI9D,uBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;gCAAuB,OAAM;gCAA6B,SAAQ;gCAAY,MAAK;0CAChG,cAAA,8OAAC;oCAAK,UAAS;oCAAU,GAAE;oCAA0N,UAAS;;;;;;;;;;;;;;;;sCAGlQ,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;;;;;;;;;;;;0BAOlD,8OAAC;gBACC,KAAK;gBACL,WAAU;gBACV,OAAO;oBAAE,YAAY;gBAA6D;;kCAElF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;;;;;;;;;;;0CAEjB,8OAAC;gCAAK,WAAU;0CAA4C;;;;;;;;;;;;kCAE9D,8OAAC;wBAAI,WAAU;kCACZ,OAAO,KAAK,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC,MAAM;4BAClC,kCAAkC;4BAClC,MAAM,YAAY,KAAK,UAAU,CAAC;4BAClC,MAAM,eAAe,KAAK,QAAQ,CAAC;4BACnC,MAAM,eAAe,KAAK,QAAQ,CAAC;4BACnC,MAAM,gBAAgB,KAAK,QAAQ,CAAC;4BAEpC,IAAI,YAAY;4BAChB,IAAI,WAAW;gCACb,aAAa;4BACf,OAAO,IAAI,eAAe;gCACxB,aAAa;4BACf,OAAO,IAAI,cAAc;gCACvB,aAAa;4BACf,OAAO,IAAI,cAAc;gCACvB,aAAa;4BACf;4BAEA,qBACE,8OAAC;gCAAiB,WAAW;;oCAC1B,QAAQ;oCAAS;;+BADT;;;;;wBAIf,mBACE,8OAAC;4BAAK,WAAU;sCACb,YAAY,oBAAoB;;;;;;;;;;;;;;;;;0BAOzC,8OAAC;gBAAK,UAAU;gBAAmB,WAAU;;kCAC3C,8OAAC;wBAAI,WAAU;kCAAmE;;;;;;kCAClF,8OAAC;wBACC,MAAK;wBACL,OAAO;wBACP,UAAU,CAAC,IAAM,WAAW,EAAE,MAAM,CAAC,KAAK;wBAC1C,aAAa,YAAY,sBAAsB;wBAC/C,UAAU,CAAC,aAAa;wBACxB,WAAU;;;;;;kCAEZ,8OAAC;wBACC,MAAK;wBACL,UAAU,CAAC,aAAa,aAAa,CAAC,QAAQ,IAAI;wBAClD,WAAU;kCAET,0BACC,8OAAC;4BAAI,WAAU;4BAAuB,OAAM;4BAA6B,MAAK;4BAAO,SAAQ;;8CAC3F,8OAAC;oCAAO,WAAU;oCAAa,IAAG;oCAAK,IAAG;oCAAK,GAAE;oCAAK,QAAO;oCAAe,aAAY;;;;;;8CACxF,8OAAC;oCAAK,WAAU;oCAAa,MAAK;oCAAe,GAAE;;;;;;;;;;;iDAGrD,8OAAC;4BAAK,WAAU;;8CACd,8OAAC;oCAAI,WAAU;oCAAe,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACtE,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;gCACjE;;;;;;;;;;;;;;;;;;;;;;;;AAQpB", "debugId": null}}, {"offset": {"line": 861, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/BlocksConnect/web-panel-blocksconnect/src/components/ServerPropertiesEditor.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useForm } from 'react-hook-form';\nimport { Server, updateServerProperties, ServerPropertiesUpdate } from '../services/api';\nimport { useAuth } from '../contexts/AuthContext';\n\ninterface ServerPropertiesEditorProps {\n  server: Server;\n  onPropertiesUpdated: () => void;\n}\n\ninterface ServerPropertiesForm {\n  // Basic Settings\n  'server-port': string;\n  'max-players': string;\n  'motd': string;\n  'gamemode': string;\n  'difficulty': string;\n  'hardcore': boolean;\n  'pvp': boolean;\n\n  // World Settings\n  'level-name': string;\n  'level-seed': string;\n  'level-type': string;\n  'generate-structures': boolean;\n  'spawn-protection': string;\n  'view-distance': string;\n  'simulation-distance': string;\n\n  // Performance Settings\n  'max-tick-time': string;\n  'max-world-size': string;\n  'network-compression-threshold': string;\n\n  // Advanced Settings\n  'enable-command-block': boolean;\n  'enable-rcon': boolean;\n  'rcon-port': string;\n  'rcon-password': string;\n  'enable-query': boolean;\n  'query-port': string;\n  'white-list': boolean;\n  'enforce-whitelist': boolean;\n  'online-mode': boolean;\n  'allow-flight': boolean;\n  'allow-nether': boolean;\n  'spawn-monsters': boolean;\n  'spawn-animals': boolean;\n  'spawn-npcs': boolean;\n\n  // Memory Settings (not in server.properties but server config)\n  memory: string;\n}\n\nconst GAMEMODE_OPTIONS = [\n  { value: 'survival', label: 'Survival' },\n  { value: 'creative', label: 'Creative' },\n  { value: 'adventure', label: 'Adventure' },\n  { value: 'spectator', label: 'Spectator' }\n];\n\nconst DIFFICULTY_OPTIONS = [\n  { value: 'peaceful', label: 'Peaceful' },\n  { value: 'easy', label: 'Easy' },\n  { value: 'normal', label: 'Normal' },\n  { value: 'hard', label: 'Hard' }\n];\n\nconst LEVEL_TYPE_OPTIONS = [\n  { value: 'minecraft:normal', label: 'Normal' },\n  { value: 'minecraft:flat', label: 'Superflat' },\n  { value: 'minecraft:large_biomes', label: 'Large Biomes' },\n  { value: 'minecraft:amplified', label: 'Amplified' }\n];\n\nconst MEMORY_OPTIONS = [\n  '1G', '2G', '4G', '6G', '8G', '12G', '16G'\n];\n\nexport default function ServerPropertiesEditor({ server, onPropertiesUpdated }: ServerPropertiesEditorProps) {\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [success, setSuccess] = useState<string | null>(null);\n  const [activeTab, setActiveTab] = useState<'basic' | 'world' | 'performance' | 'advanced'>('basic');\n  const { getToken } = useAuth();\n\n  const { register, handleSubmit, reset, formState: { errors, isDirty } } = useForm<ServerPropertiesForm>();\n\n  useEffect(() => {\n    // Initialize form with current server properties\n    const defaultValues: Partial<ServerPropertiesForm> = {\n      'server-port': server.port ? server.port.toString() : '25565',\n      'max-players': '20',\n      'motd': `A Minecraft Server - ${server.name}`,\n      'gamemode': 'survival',\n      'difficulty': 'normal',\n      'hardcore': false,\n      'pvp': true,\n      'level-name': 'world',\n      'level-seed': '',\n      'level-type': 'minecraft:normal',\n      'generate-structures': true,\n      'spawn-protection': '16',\n      'view-distance': '10',\n      'simulation-distance': '10',\n      'max-tick-time': '60000',\n      'max-world-size': '29999984',\n      'network-compression-threshold': '256',\n      'enable-command-block': false,\n      'enable-rcon': false,\n      'rcon-port': '25575',\n      'rcon-password': '',\n      'enable-query': false,\n      'query-port': '25565',\n      'white-list': false,\n      'enforce-whitelist': false,\n      'online-mode': true,\n      'allow-flight': false,\n      'allow-nether': true,\n      'spawn-monsters': true,\n      'spawn-animals': true,\n      'spawn-npcs': true,\n      memory: server.memory || '2G',\n      ...server.properties\n    };\n\n    reset(defaultValues);\n  }, [server, reset]);\n\n  const onSubmit = async (data: ServerPropertiesForm) => {\n    setIsLoading(true);\n    setError(null);\n    setSuccess(null);\n\n    try {\n      const token = await getToken();\n\n      // Separate memory from properties\n      const { memory, ...properties } = data;\n\n      // Convert form data to the format expected by the API\n      const propertiesUpdate: ServerPropertiesUpdate = {\n        properties: Object.fromEntries(\n          Object.entries(properties).map(([key, value]) => [\n            key,\n            typeof value === 'boolean' ? value : String(value)\n          ])\n        ),\n        memory\n      };\n\n      await updateServerProperties(server.id, propertiesUpdate, token);\n\n      setSuccess('Server properties updated successfully!');\n      onPropertiesUpdated();\n\n      // Clear success message after 3 seconds\n      setTimeout(() => setSuccess(null), 3000);\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Failed to update server properties');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const tabs = [\n    { id: 'basic', label: 'Basic Settings', icon: '⚙️' },\n    { id: 'world', label: 'World Settings', icon: '🌍' },\n    { id: 'performance', label: 'Performance', icon: '⚡' },\n    { id: 'advanced', label: 'Advanced', icon: '🔧' }\n  ] as const;\n\n  return (\n    <div className=\"card overflow-hidden fade-in-fast\">\n      <div className=\"px-6 lg:px-8 py-6 border-b border-white/10 bg-gradient-to-r from-purple-500/10 to-pink-500/10\">\n        <h2 className=\"text-xl lg:text-2xl font-bold flex items-center gradient-text\">\n          <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 lg:h-6 lg:w-6 mr-3 text-purple-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z\" />\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 12a3 3 0 11-6 0 3 3 0 016 0z\" />\n          </svg>\n          Server Properties\n        </h2>\n        <p className=\"text-gray-300 mt-2 font-light text-sm lg:text-base\">Configure your Minecraft server settings</p>\n      </div>\n\n      {/* Tab Navigation */}\n      <div className=\"border-b border-white/10\">\n        <nav className=\"flex space-x-0 overflow-x-auto\">\n          {tabs.map((tab) => (\n            <button\n              key={tab.id}\n              onClick={() => setActiveTab(tab.id)}\n              className={`flex items-center px-4 lg:px-6 py-4 text-sm font-medium border-b-2 transition-colors whitespace-nowrap ${\n                activeTab === tab.id\n                  ? 'border-purple-500 text-purple-400 bg-purple-500/10'\n                  : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'\n              }`}\n            >\n              <span className=\"mr-2\">{tab.icon}</span>\n              {tab.label}\n            </button>\n          ))}\n        </nav>\n      </div>\n\n      <form onSubmit={handleSubmit(onSubmit)} className=\"p-6 lg:p-8\">\n        {/* Status Messages */}\n        {error && (\n          <div className=\"mb-6 p-4 bg-red-500/20 border border-red-500/30 rounded-lg text-red-400 flex items-center\">\n            <svg className=\"h-5 w-5 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n            </svg>\n            {error}\n          </div>\n        )}\n\n        {success && (\n          <div className=\"mb-6 p-4 bg-green-500/20 border border-green-500/30 rounded-lg text-green-400 flex items-center\">\n            <svg className=\"h-5 w-5 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n            </svg>\n            {success}\n          </div>\n        )}\n\n        {/* Basic Settings Tab */}\n        {activeTab === 'basic' && (\n          <div className=\"space-y-6\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div>\n                <label className=\"block text-gray-200 text-sm font-semibold mb-3\">Server Port</label>\n                <input\n                  type=\"number\"\n                  className=\"input-field w-full\"\n                  {...register('server-port', {\n                    required: 'Server port is required',\n                    min: { value: 1, message: 'Must be at least 1' },\n                    max: { value: 65535, message: 'Must be at most 65535' },\n                    valueAsNumber: true,\n                    validate: (value) => !isNaN(value) || 'Must be a valid number'\n                  })}\n                />\n                {errors['server-port'] && (\n                  <p className=\"text-red-400 text-sm mt-2\">{errors['server-port'].message}</p>\n                )}\n              </div>\n\n              <div>\n                <label className=\"block text-gray-200 text-sm font-semibold mb-3\">Max Players</label>\n                <input\n                  type=\"number\"\n                  className=\"input-field w-full\"\n                  {...register('max-players', {\n                    required: 'Max players is required',\n                    min: { value: 1, message: 'Must be at least 1' },\n                    max: { value: 1000, message: 'Must be at most 1000' },\n                    valueAsNumber: true,\n                    validate: (value) => !isNaN(value) || 'Must be a valid number'\n                  })}\n                />\n                {errors['max-players'] && (\n                  <p className=\"text-red-400 text-sm mt-2\">{errors['max-players'].message}</p>\n                )}\n              </div>\n\n              <div>\n                <label className=\"block text-gray-200 text-sm font-semibold mb-3\">Game Mode</label>\n                <select className=\"input-field w-full\" {...register('gamemode')}>\n                  {GAMEMODE_OPTIONS.map(option => (\n                    <option key={option.value} value={option.value}>{option.label}</option>\n                  ))}\n                </select>\n              </div>\n\n              <div>\n                <label className=\"block text-gray-200 text-sm font-semibold mb-3\">Difficulty</label>\n                <select className=\"input-field w-full\" {...register('difficulty')}>\n                  {DIFFICULTY_OPTIONS.map(option => (\n                    <option key={option.value} value={option.value}>{option.label}</option>\n                  ))}\n                </select>\n              </div>\n\n              <div>\n                <label className=\"block text-gray-200 text-sm font-semibold mb-3\">Memory Allocation</label>\n                <select className=\"input-field w-full\" {...register('memory')}>\n                  {MEMORY_OPTIONS.map(option => (\n                    <option key={option} value={option}>{option}</option>\n                  ))}\n                </select>\n              </div>\n            </div>\n\n            <div>\n              <label className=\"block text-gray-200 text-sm font-semibold mb-3\">Message of the Day (MOTD)</label>\n              <input\n                type=\"text\"\n                className=\"input-field w-full\"\n                placeholder=\"A Minecraft Server\"\n                {...register('motd')}\n              />\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n              <label className=\"flex items-center space-x-3 cursor-pointer\">\n                <input type=\"checkbox\" className=\"form-checkbox h-5 w-5 text-purple-500\" {...register('hardcore')} />\n                <span className=\"text-gray-200 font-medium\">Hardcore Mode</span>\n              </label>\n\n              <label className=\"flex items-center space-x-3 cursor-pointer\">\n                <input type=\"checkbox\" className=\"form-checkbox h-5 w-5 text-purple-500\" {...register('pvp')} />\n                <span className=\"text-gray-200 font-medium\">PvP Enabled</span>\n              </label>\n\n              <label className=\"flex items-center space-x-3 cursor-pointer\">\n                <input type=\"checkbox\" className=\"form-checkbox h-5 w-5 text-purple-500\" {...register('online-mode')} />\n                <span className=\"text-gray-200 font-medium\">Online Mode</span>\n              </label>\n            </div>\n          </div>\n        )}\n\n        {/* World Settings Tab */}\n        {activeTab === 'world' && (\n          <div className=\"space-y-6\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div>\n                <label className=\"block text-gray-200 text-sm font-semibold mb-3\">World Name</label>\n                <input\n                  type=\"text\"\n                  className=\"input-field w-full\"\n                  placeholder=\"world\"\n                  {...register('level-name')}\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-gray-200 text-sm font-semibold mb-3\">World Seed</label>\n                <input\n                  type=\"text\"\n                  className=\"input-field w-full\"\n                  placeholder=\"Leave empty for random\"\n                  {...register('level-seed')}\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-gray-200 text-sm font-semibold mb-3\">World Type</label>\n                <select className=\"input-field w-full\" {...register('level-type')}>\n                  {LEVEL_TYPE_OPTIONS.map(option => (\n                    <option key={option.value} value={option.value}>{option.label}</option>\n                  ))}\n                </select>\n              </div>\n\n              <div>\n                <label className=\"block text-gray-200 text-sm font-semibold mb-3\">Spawn Protection Radius</label>\n                <input\n                  type=\"number\"\n                  className=\"input-field w-full\"\n                  {...register('spawn-protection', { min: 0, max: 100 })}\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-gray-200 text-sm font-semibold mb-3\">View Distance</label>\n                <input\n                  type=\"number\"\n                  className=\"input-field w-full\"\n                  {...register('view-distance', { min: 3, max: 32 })}\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-gray-200 text-sm font-semibold mb-3\">Simulation Distance</label>\n                <input\n                  type=\"number\"\n                  className=\"input-field w-full\"\n                  {...register('simulation-distance', { min: 3, max: 32 })}\n                />\n              </div>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n              <label className=\"flex items-center space-x-3 cursor-pointer\">\n                <input type=\"checkbox\" className=\"form-checkbox h-5 w-5 text-purple-500\" {...register('generate-structures')} />\n                <span className=\"text-gray-200 font-medium\">Generate Structures</span>\n              </label>\n\n              <label className=\"flex items-center space-x-3 cursor-pointer\">\n                <input type=\"checkbox\" className=\"form-checkbox h-5 w-5 text-purple-500\" {...register('allow-nether')} />\n                <span className=\"text-gray-200 font-medium\">Allow Nether</span>\n              </label>\n\n              <label className=\"flex items-center space-x-3 cursor-pointer\">\n                <input type=\"checkbox\" className=\"form-checkbox h-5 w-5 text-purple-500\" {...register('spawn-monsters')} />\n                <span className=\"text-gray-200 font-medium\">Spawn Monsters</span>\n              </label>\n\n              <label className=\"flex items-center space-x-3 cursor-pointer\">\n                <input type=\"checkbox\" className=\"form-checkbox h-5 w-5 text-purple-500\" {...register('spawn-animals')} />\n                <span className=\"text-gray-200 font-medium\">Spawn Animals</span>\n              </label>\n\n              <label className=\"flex items-center space-x-3 cursor-pointer\">\n                <input type=\"checkbox\" className=\"form-checkbox h-5 w-5 text-purple-500\" {...register('spawn-npcs')} />\n                <span className=\"text-gray-200 font-medium\">Spawn NPCs</span>\n              </label>\n\n              <label className=\"flex items-center space-x-3 cursor-pointer\">\n                <input type=\"checkbox\" className=\"form-checkbox h-5 w-5 text-purple-500\" {...register('allow-flight')} />\n                <span className=\"text-gray-200 font-medium\">Allow Flight</span>\n              </label>\n            </div>\n          </div>\n        )}\n\n        {/* Performance Settings Tab */}\n        {activeTab === 'performance' && (\n          <div className=\"space-y-6\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div>\n                <label className=\"block text-gray-200 text-sm font-semibold mb-3\">Max Tick Time (ms)</label>\n                <input\n                  type=\"number\"\n                  className=\"input-field w-full\"\n                  {...register('max-tick-time', {\n                    min: { value: 1000, message: 'Must be at least 1000ms' },\n                    max: { value: 300000, message: 'Must be at most 300000ms' },\n                    valueAsNumber: true,\n                    validate: (value) => !isNaN(value) || 'Must be a valid number'\n                  })}\n                />\n                <p className=\"text-gray-400 text-xs mt-1\">Time before server watchdog stops the server</p>\n              </div>\n\n              <div>\n                <label className=\"block text-gray-200 text-sm font-semibold mb-3\">Max World Size</label>\n                <input\n                  type=\"number\"\n                  className=\"input-field w-full\"\n                  {...register('max-world-size', {\n                    min: { value: 1, message: 'Must be at least 1' },\n                    max: { value: 29999984, message: 'Must be at most 29999984' },\n                    valueAsNumber: true,\n                    validate: (value) => !isNaN(value) || 'Must be a valid number'\n                  })}\n                />\n                <p className=\"text-gray-400 text-xs mt-1\">Maximum radius of the world border</p>\n              </div>\n\n              <div>\n                <label className=\"block text-gray-200 text-sm font-semibold mb-3\">Network Compression Threshold</label>\n                <input\n                  type=\"number\"\n                  className=\"input-field w-full\"\n                  {...register('network-compression-threshold', {\n                    min: { value: -1, message: 'Must be at least -1' },\n                    max: { value: 1024, message: 'Must be at most 1024' },\n                    valueAsNumber: true,\n                    validate: (value) => !isNaN(value) || 'Must be a valid number'\n                  })}\n                />\n                <p className=\"text-gray-400 text-xs mt-1\">Packet size threshold for compression (-1 to disable)</p>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Advanced Settings Tab */}\n        {activeTab === 'advanced' && (\n          <div className=\"space-y-6\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              <div>\n                <label className=\"block text-gray-200 text-sm font-semibold mb-3\">RCON Port</label>\n                <input\n                  type=\"number\"\n                  className=\"input-field w-full\"\n                  {...register('rcon-port', {\n                    min: { value: 1, message: 'Must be at least 1' },\n                    max: { value: 65535, message: 'Must be at most 65535' },\n                    valueAsNumber: true,\n                    validate: (value) => !isNaN(value) || 'Must be a valid number'\n                  })}\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-gray-200 text-sm font-semibold mb-3\">RCON Password</label>\n                <input\n                  type=\"password\"\n                  className=\"input-field w-full\"\n                  placeholder=\"Leave empty to disable RCON\"\n                  {...register('rcon-password')}\n                />\n              </div>\n\n              <div>\n                <label className=\"block text-gray-200 text-sm font-semibold mb-3\">Query Port</label>\n                <input\n                  type=\"number\"\n                  className=\"input-field w-full\"\n                  {...register('query-port', {\n                    min: { value: 1, message: 'Must be at least 1' },\n                    max: { value: 65535, message: 'Must be at most 65535' },\n                    valueAsNumber: true,\n                    validate: (value) => !isNaN(value) || 'Must be a valid number'\n                  })}\n                />\n              </div>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n              <label className=\"flex items-center space-x-3 cursor-pointer\">\n                <input type=\"checkbox\" className=\"form-checkbox h-5 w-5 text-purple-500\" {...register('enable-command-block')} />\n                <span className=\"text-gray-200 font-medium\">Enable Command Blocks</span>\n              </label>\n\n              <label className=\"flex items-center space-x-3 cursor-pointer\">\n                <input type=\"checkbox\" className=\"form-checkbox h-5 w-5 text-purple-500\" {...register('enable-rcon')} />\n                <span className=\"text-gray-200 font-medium\">Enable RCON</span>\n              </label>\n\n              <label className=\"flex items-center space-x-3 cursor-pointer\">\n                <input type=\"checkbox\" className=\"form-checkbox h-5 w-5 text-purple-500\" {...register('enable-query')} />\n                <span className=\"text-gray-200 font-medium\">Enable Query</span>\n              </label>\n\n              <label className=\"flex items-center space-x-3 cursor-pointer\">\n                <input type=\"checkbox\" className=\"form-checkbox h-5 w-5 text-purple-500\" {...register('white-list')} />\n                <span className=\"text-gray-200 font-medium\">Enable Whitelist</span>\n              </label>\n\n              <label className=\"flex items-center space-x-3 cursor-pointer\">\n                <input type=\"checkbox\" className=\"form-checkbox h-5 w-5 text-purple-500\" {...register('enforce-whitelist')} />\n                <span className=\"text-gray-200 font-medium\">Enforce Whitelist</span>\n              </label>\n            </div>\n\n            <div className=\"bg-yellow-500/10 border border-yellow-500/30 rounded-lg p-4\">\n              <div className=\"flex items-start\">\n                <svg className=\"h-5 w-5 text-yellow-400 mt-0.5 mr-3 flex-shrink-0\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path fillRule=\"evenodd\" d=\"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n                </svg>\n                <div>\n                  <h4 className=\"text-yellow-400 font-semibold mb-1\">Advanced Settings Warning</h4>\n                  <p className=\"text-yellow-300 text-sm\">\n                    These settings can significantly impact server performance and security.\n                    Only modify them if you understand their implications.\n                  </p>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Action Buttons */}\n        <div className=\"flex flex-col sm:flex-row gap-4 pt-6 border-t border-white/10\">\n          <button\n            type=\"submit\"\n            disabled={isLoading || !isDirty}\n            className=\"flex-1 bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 disabled:from-gray-600 disabled:to-gray-600 text-white px-6 py-3 rounded-lg font-semibold transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-purple-500/20 disabled:hover:scale-100 disabled:hover:shadow-none disabled:cursor-not-allowed\"\n          >\n            {isLoading ? (\n              <div className=\"flex items-center justify-center\">\n                <div className=\"animate-spin h-5 w-5 border-2 border-white/30 border-t-white rounded-full mr-2\"></div>\n                Saving...\n              </div>\n            ) : (\n              'Save Properties'\n            )}\n          </button>\n\n          <button\n            type=\"button\"\n            onClick={() => reset()}\n            disabled={isLoading}\n            className=\"flex-1 sm:flex-none bg-gray-600 hover:bg-gray-700 disabled:bg-gray-800 text-white px-6 py-3 rounded-lg font-semibold transition-all duration-300 hover:scale-105 disabled:hover:scale-100 disabled:cursor-not-allowed\"\n          >\n            Reset\n          </button>\n        </div>\n      </form>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAwDA,MAAM,mBAAmB;IACvB;QAAE,OAAO;QAAY,OAAO;IAAW;IACvC;QAAE,OAAO;QAAY,OAAO;IAAW;IACvC;QAAE,OAAO;QAAa,OAAO;IAAY;IACzC;QAAE,OAAO;QAAa,OAAO;IAAY;CAC1C;AAED,MAAM,qBAAqB;IACzB;QAAE,OAAO;QAAY,OAAO;IAAW;IACvC;QAAE,OAAO;QAAQ,OAAO;IAAO;IAC/B;QAAE,OAAO;QAAU,OAAO;IAAS;IACnC;QAAE,OAAO;QAAQ,OAAO;IAAO;CAChC;AAED,MAAM,qBAAqB;IACzB;QAAE,OAAO;QAAoB,OAAO;IAAS;IAC7C;QAAE,OAAO;QAAkB,OAAO;IAAY;IAC9C;QAAE,OAAO;QAA0B,OAAO;IAAe;IACzD;QAAE,OAAO;QAAuB,OAAO;IAAY;CACpD;AAED,MAAM,iBAAiB;IACrB;IAAM;IAAM;IAAM;IAAM;IAAM;IAAO;CACtC;AAEc,SAAS,uBAAuB,EAAE,MAAM,EAAE,mBAAmB,EAA+B;IACzG,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACtD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkD;IAC3F,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAE3B,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE,KAAK,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD;IAEhF,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,iDAAiD;QACjD,MAAM,gBAA+C;YACnD,eAAe,OAAO,IAAI,GAAG,OAAO,IAAI,CAAC,QAAQ,KAAK;YACtD,eAAe;YACf,QAAQ,CAAC,qBAAqB,EAAE,OAAO,IAAI,EAAE;YAC7C,YAAY;YACZ,cAAc;YACd,YAAY;YACZ,OAAO;YACP,cAAc;YACd,cAAc;YACd,cAAc;YACd,uBAAuB;YACvB,oBAAoB;YACpB,iBAAiB;YACjB,uBAAuB;YACvB,iBAAiB;YACjB,kBAAkB;YAClB,iCAAiC;YACjC,wBAAwB;YACxB,eAAe;YACf,aAAa;YACb,iBAAiB;YACjB,gBAAgB;YAChB,cAAc;YACd,cAAc;YACd,qBAAqB;YACrB,eAAe;YACf,gBAAgB;YAChB,gBAAgB;YAChB,kBAAkB;YAClB,iBAAiB;YACjB,cAAc;YACd,QAAQ,OAAO,MAAM,IAAI;YACzB,GAAG,OAAO,UAAU;QACtB;QAEA,MAAM;IACR,GAAG;QAAC;QAAQ;KAAM;IAElB,MAAM,WAAW,OAAO;QACtB,aAAa;QACb,SAAS;QACT,WAAW;QAEX,IAAI;YACF,MAAM,QAAQ,MAAM;YAEpB,kCAAkC;YAClC,MAAM,EAAE,MAAM,EAAE,GAAG,YAAY,GAAG;YAElC,sDAAsD;YACtD,MAAM,mBAA2C;gBAC/C,YAAY,OAAO,WAAW,CAC5B,OAAO,OAAO,CAAC,YAAY,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,GAAK;wBAC/C;wBACA,OAAO,UAAU,YAAY,QAAQ,OAAO;qBAC7C;gBAEH;YACF;YAEA,MAAM,CAAA,GAAA,sHAAA,CAAA,yBAAsB,AAAD,EAAE,OAAO,EAAE,EAAE,kBAAkB;YAE1D,WAAW;YACX;YAEA,wCAAwC;YACxC,WAAW,IAAM,WAAW,OAAO;QACrC,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,OAAO;QACX;YAAE,IAAI;YAAS,OAAO;YAAkB,MAAM;QAAK;QACnD;YAAE,IAAI;YAAS,OAAO;YAAkB,MAAM;QAAK;QACnD;YAAE,IAAI;YAAe,OAAO;YAAe,MAAM;QAAI;QACrD;YAAE,IAAI;YAAY,OAAO;YAAY,MAAM;QAAK;KACjD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC;gCAAI,OAAM;gCAA6B,WAAU;gCAA6C,MAAK;gCAAO,SAAQ;gCAAY,QAAO;;kDACpI,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;kDACrE,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;4BACjE;;;;;;;kCAGR,8OAAC;wBAAE,WAAU;kCAAqD;;;;;;;;;;;;0BAIpE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACZ,KAAK,GAAG,CAAC,CAAC,oBACT,8OAAC;4BAEC,SAAS,IAAM,aAAa,IAAI,EAAE;4BAClC,WAAW,CAAC,uGAAuG,EACjH,cAAc,IAAI,EAAE,GAChB,uDACA,8EACJ;;8CAEF,8OAAC;oCAAK,WAAU;8CAAQ,IAAI,IAAI;;;;;;gCAC/B,IAAI,KAAK;;2BATL,IAAI,EAAE;;;;;;;;;;;;;;;0BAenB,8OAAC;gBAAK,UAAU,aAAa;gBAAW,WAAU;;oBAE/C,uBACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;gCAAe,MAAK;gCAAe,SAAQ;0CACxD,cAAA,8OAAC;oCAAK,UAAS;oCAAU,GAAE;oCAAoH,UAAS;;;;;;;;;;;4BAEzJ;;;;;;;oBAIJ,yBACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;gCAAe,MAAK;gCAAe,SAAQ;0CACxD,cAAA,8OAAC;oCAAK,UAAS;oCAAU,GAAE;oCAAwI,UAAS;;;;;;;;;;;4BAE7K;;;;;;;oBAKJ,cAAc,yBACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAAiD;;;;;;0DAClE,8OAAC;gDACC,MAAK;gDACL,WAAU;gDACT,GAAG,SAAS,eAAe;oDAC1B,UAAU;oDACV,KAAK;wDAAE,OAAO;wDAAG,SAAS;oDAAqB;oDAC/C,KAAK;wDAAE,OAAO;wDAAO,SAAS;oDAAwB;oDACtD,eAAe;oDACf,UAAU,CAAC,QAAU,CAAC,MAAM,UAAU;gDACxC,EAAE;;;;;;4CAEH,MAAM,CAAC,cAAc,kBACpB,8OAAC;gDAAE,WAAU;0DAA6B,MAAM,CAAC,cAAc,CAAC,OAAO;;;;;;;;;;;;kDAI3E,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAAiD;;;;;;0DAClE,8OAAC;gDACC,MAAK;gDACL,WAAU;gDACT,GAAG,SAAS,eAAe;oDAC1B,UAAU;oDACV,KAAK;wDAAE,OAAO;wDAAG,SAAS;oDAAqB;oDAC/C,KAAK;wDAAE,OAAO;wDAAM,SAAS;oDAAuB;oDACpD,eAAe;oDACf,UAAU,CAAC,QAAU,CAAC,MAAM,UAAU;gDACxC,EAAE;;;;;;4CAEH,MAAM,CAAC,cAAc,kBACpB,8OAAC;gDAAE,WAAU;0DAA6B,MAAM,CAAC,cAAc,CAAC,OAAO;;;;;;;;;;;;kDAI3E,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAAiD;;;;;;0DAClE,8OAAC;gDAAO,WAAU;gDAAsB,GAAG,SAAS,WAAW;0DAC5D,iBAAiB,GAAG,CAAC,CAAA,uBACpB,8OAAC;wDAA0B,OAAO,OAAO,KAAK;kEAAG,OAAO,KAAK;uDAAhD,OAAO,KAAK;;;;;;;;;;;;;;;;kDAK/B,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAAiD;;;;;;0DAClE,8OAAC;gDAAO,WAAU;gDAAsB,GAAG,SAAS,aAAa;0DAC9D,mBAAmB,GAAG,CAAC,CAAA,uBACtB,8OAAC;wDAA0B,OAAO,OAAO,KAAK;kEAAG,OAAO,KAAK;uDAAhD,OAAO,KAAK;;;;;;;;;;;;;;;;kDAK/B,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAAiD;;;;;;0DAClE,8OAAC;gDAAO,WAAU;gDAAsB,GAAG,SAAS,SAAS;0DAC1D,eAAe,GAAG,CAAC,CAAA,uBAClB,8OAAC;wDAAoB,OAAO;kEAAS;uDAAxB;;;;;;;;;;;;;;;;;;;;;;0CAMrB,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAAiD;;;;;;kDAClE,8OAAC;wCACC,MAAK;wCACL,WAAU;wCACV,aAAY;wCACX,GAAG,SAAS,OAAO;;;;;;;;;;;;0CAIxB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;;0DACf,8OAAC;gDAAM,MAAK;gDAAW,WAAU;gDAAyC,GAAG,SAAS,WAAW;;;;;;0DACjG,8OAAC;gDAAK,WAAU;0DAA4B;;;;;;;;;;;;kDAG9C,8OAAC;wCAAM,WAAU;;0DACf,8OAAC;gDAAM,MAAK;gDAAW,WAAU;gDAAyC,GAAG,SAAS,MAAM;;;;;;0DAC5F,8OAAC;gDAAK,WAAU;0DAA4B;;;;;;;;;;;;kDAG9C,8OAAC;wCAAM,WAAU;;0DACf,8OAAC;gDAAM,MAAK;gDAAW,WAAU;gDAAyC,GAAG,SAAS,cAAc;;;;;;0DACpG,8OAAC;gDAAK,WAAU;0DAA4B;;;;;;;;;;;;;;;;;;;;;;;;oBAOnD,cAAc,yBACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAAiD;;;;;;0DAClE,8OAAC;gDACC,MAAK;gDACL,WAAU;gDACV,aAAY;gDACX,GAAG,SAAS,aAAa;;;;;;;;;;;;kDAI9B,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAAiD;;;;;;0DAClE,8OAAC;gDACC,MAAK;gDACL,WAAU;gDACV,aAAY;gDACX,GAAG,SAAS,aAAa;;;;;;;;;;;;kDAI9B,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAAiD;;;;;;0DAClE,8OAAC;gDAAO,WAAU;gDAAsB,GAAG,SAAS,aAAa;0DAC9D,mBAAmB,GAAG,CAAC,CAAA,uBACtB,8OAAC;wDAA0B,OAAO,OAAO,KAAK;kEAAG,OAAO,KAAK;uDAAhD,OAAO,KAAK;;;;;;;;;;;;;;;;kDAK/B,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAAiD;;;;;;0DAClE,8OAAC;gDACC,MAAK;gDACL,WAAU;gDACT,GAAG,SAAS,oBAAoB;oDAAE,KAAK;oDAAG,KAAK;gDAAI,EAAE;;;;;;;;;;;;kDAI1D,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAAiD;;;;;;0DAClE,8OAAC;gDACC,MAAK;gDACL,WAAU;gDACT,GAAG,SAAS,iBAAiB;oDAAE,KAAK;oDAAG,KAAK;gDAAG,EAAE;;;;;;;;;;;;kDAItD,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAAiD;;;;;;0DAClE,8OAAC;gDACC,MAAK;gDACL,WAAU;gDACT,GAAG,SAAS,uBAAuB;oDAAE,KAAK;oDAAG,KAAK;gDAAG,EAAE;;;;;;;;;;;;;;;;;;0CAK9D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;;0DACf,8OAAC;gDAAM,MAAK;gDAAW,WAAU;gDAAyC,GAAG,SAAS,sBAAsB;;;;;;0DAC5G,8OAAC;gDAAK,WAAU;0DAA4B;;;;;;;;;;;;kDAG9C,8OAAC;wCAAM,WAAU;;0DACf,8OAAC;gDAAM,MAAK;gDAAW,WAAU;gDAAyC,GAAG,SAAS,eAAe;;;;;;0DACrG,8OAAC;gDAAK,WAAU;0DAA4B;;;;;;;;;;;;kDAG9C,8OAAC;wCAAM,WAAU;;0DACf,8OAAC;gDAAM,MAAK;gDAAW,WAAU;gDAAyC,GAAG,SAAS,iBAAiB;;;;;;0DACvG,8OAAC;gDAAK,WAAU;0DAA4B;;;;;;;;;;;;kDAG9C,8OAAC;wCAAM,WAAU;;0DACf,8OAAC;gDAAM,MAAK;gDAAW,WAAU;gDAAyC,GAAG,SAAS,gBAAgB;;;;;;0DACtG,8OAAC;gDAAK,WAAU;0DAA4B;;;;;;;;;;;;kDAG9C,8OAAC;wCAAM,WAAU;;0DACf,8OAAC;gDAAM,MAAK;gDAAW,WAAU;gDAAyC,GAAG,SAAS,aAAa;;;;;;0DACnG,8OAAC;gDAAK,WAAU;0DAA4B;;;;;;;;;;;;kDAG9C,8OAAC;wCAAM,WAAU;;0DACf,8OAAC;gDAAM,MAAK;gDAAW,WAAU;gDAAyC,GAAG,SAAS,eAAe;;;;;;0DACrG,8OAAC;gDAAK,WAAU;0DAA4B;;;;;;;;;;;;;;;;;;;;;;;;oBAOnD,cAAc,+BACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAiD;;;;;;sDAClE,8OAAC;4CACC,MAAK;4CACL,WAAU;4CACT,GAAG,SAAS,iBAAiB;gDAC5B,KAAK;oDAAE,OAAO;oDAAM,SAAS;gDAA0B;gDACvD,KAAK;oDAAE,OAAO;oDAAQ,SAAS;gDAA2B;gDAC1D,eAAe;gDACf,UAAU,CAAC,QAAU,CAAC,MAAM,UAAU;4CACxC,EAAE;;;;;;sDAEJ,8OAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;8CAG5C,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAiD;;;;;;sDAClE,8OAAC;4CACC,MAAK;4CACL,WAAU;4CACT,GAAG,SAAS,kBAAkB;gDAC7B,KAAK;oDAAE,OAAO;oDAAG,SAAS;gDAAqB;gDAC/C,KAAK;oDAAE,OAAO;oDAAU,SAAS;gDAA2B;gDAC5D,eAAe;gDACf,UAAU,CAAC,QAAU,CAAC,MAAM,UAAU;4CACxC,EAAE;;;;;;sDAEJ,8OAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;8CAG5C,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAiD;;;;;;sDAClE,8OAAC;4CACC,MAAK;4CACL,WAAU;4CACT,GAAG,SAAS,iCAAiC;gDAC5C,KAAK;oDAAE,OAAO,CAAC;oDAAG,SAAS;gDAAsB;gDACjD,KAAK;oDAAE,OAAO;oDAAM,SAAS;gDAAuB;gDACpD,eAAe;gDACf,UAAU,CAAC,QAAU,CAAC,MAAM,UAAU;4CACxC,EAAE;;;;;;sDAEJ,8OAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;;;;;;;;;;;;oBAOjD,cAAc,4BACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAAiD;;;;;;0DAClE,8OAAC;gDACC,MAAK;gDACL,WAAU;gDACT,GAAG,SAAS,aAAa;oDACxB,KAAK;wDAAE,OAAO;wDAAG,SAAS;oDAAqB;oDAC/C,KAAK;wDAAE,OAAO;wDAAO,SAAS;oDAAwB;oDACtD,eAAe;oDACf,UAAU,CAAC,QAAU,CAAC,MAAM,UAAU;gDACxC,EAAE;;;;;;;;;;;;kDAIN,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAAiD;;;;;;0DAClE,8OAAC;gDACC,MAAK;gDACL,WAAU;gDACV,aAAY;gDACX,GAAG,SAAS,gBAAgB;;;;;;;;;;;;kDAIjC,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAAiD;;;;;;0DAClE,8OAAC;gDACC,MAAK;gDACL,WAAU;gDACT,GAAG,SAAS,cAAc;oDACzB,KAAK;wDAAE,OAAO;wDAAG,SAAS;oDAAqB;oDAC/C,KAAK;wDAAE,OAAO;wDAAO,SAAS;oDAAwB;oDACtD,eAAe;oDACf,UAAU,CAAC,QAAU,CAAC,MAAM,UAAU;gDACxC,EAAE;;;;;;;;;;;;;;;;;;0CAKR,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;;0DACf,8OAAC;gDAAM,MAAK;gDAAW,WAAU;gDAAyC,GAAG,SAAS,uBAAuB;;;;;;0DAC7G,8OAAC;gDAAK,WAAU;0DAA4B;;;;;;;;;;;;kDAG9C,8OAAC;wCAAM,WAAU;;0DACf,8OAAC;gDAAM,MAAK;gDAAW,WAAU;gDAAyC,GAAG,SAAS,cAAc;;;;;;0DACpG,8OAAC;gDAAK,WAAU;0DAA4B;;;;;;;;;;;;kDAG9C,8OAAC;wCAAM,WAAU;;0DACf,8OAAC;gDAAM,MAAK;gDAAW,WAAU;gDAAyC,GAAG,SAAS,eAAe;;;;;;0DACrG,8OAAC;gDAAK,WAAU;0DAA4B;;;;;;;;;;;;kDAG9C,8OAAC;wCAAM,WAAU;;0DACf,8OAAC;gDAAM,MAAK;gDAAW,WAAU;gDAAyC,GAAG,SAAS,aAAa;;;;;;0DACnG,8OAAC;gDAAK,WAAU;0DAA4B;;;;;;;;;;;;kDAG9C,8OAAC;wCAAM,WAAU;;0DACf,8OAAC;gDAAM,MAAK;gDAAW,WAAU;gDAAyC,GAAG,SAAS,oBAAoB;;;;;;0DAC1G,8OAAC;gDAAK,WAAU;0DAA4B;;;;;;;;;;;;;;;;;;0CAIhD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;4CAAoD,MAAK;4CAAe,SAAQ;sDAC7F,cAAA,8OAAC;gDAAK,UAAS;gDAAU,GAAE;gDAAoN,UAAS;;;;;;;;;;;sDAE1P,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAqC;;;;;;8DACnD,8OAAC;oDAAE,WAAU;8DAA0B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAWjD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,MAAK;gCACL,UAAU,aAAa,CAAC;gCACxB,WAAU;0CAET,0BACC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;wCAAuF;;;;;;2CAIxG;;;;;;0CAIJ,8OAAC;gCACC,MAAK;gCACL,SAAS,IAAM;gCACf,UAAU;gCACV,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;AAOX", "debugId": null}}, {"offset": {"line": 2386, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/BlocksConnect/web-panel-blocksconnect/src/components/BackupManager.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport { Server, buildAuthHeaders } from '../services/api';\n\nconst API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';\n\ninterface BackupManagerProps {\n  server: Server;\n  onBackupUpdated: () => void;\n}\n\ninterface BackupSchedule {\n  enabled: boolean;\n  frequency: 'hourly' | 'daily' | 'weekly';\n  time?: string; // For daily/weekly backups\n  dayOfWeek?: number; // For weekly backups (0-6, Sunday-Saturday)\n  maxBackups: number;\n}\n\ninterface BackupFile {\n  id: string;\n  name: string;\n  size: number;\n  created: string;\n  path: string;\n}\n\nexport default function BackupManager({ server, onBackupUpdated }: BackupManagerProps) {\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [success, setSuccess] = useState<string | null>(null);\n  const [backups, setBackups] = useState<BackupFile[]>([]);\n  const [schedule, setSchedule] = useState<BackupSchedule>({\n    enabled: false,\n    frequency: 'daily',\n    time: '02:00',\n    maxBackups: 7\n  });\n  const { getToken } = useAuth();\n\n  useEffect(() => {\n    fetchBackups();\n    fetchSchedule();\n  }, [server.id]);\n\n  const fetchBackups = async () => {\n    try {\n      const token = await getToken();\n      const headers = buildAuthHeaders(token);\n      const response = await fetch(`${API_URL}/minecraft/servers/${server.id}/backups`, {\n        headers\n      });\n      \n      if (response.ok) {\n        const data = await response.json();\n        setBackups(data.backups || []);\n      }\n    } catch (err) {\n      console.error('Error fetching backups:', err);\n    }\n  };\n\n  const fetchSchedule = async () => {\n    try {\n      const token = await getToken();\n      const headers = buildAuthHeaders(token);\n      const response = await fetch(`${API_URL}/minecraft/servers/${server.id}/backup-schedule`, {\n        headers\n      });\n      \n      if (response.ok) {\n        const data = await response.json();\n        setSchedule(data.schedule || schedule);\n      }\n    } catch (err) {\n      console.error('Error fetching backup schedule:', err);\n    }\n  };\n\n  const createBackup = async () => {\n    setIsLoading(true);\n    setError(null);\n    setSuccess(null);\n\n    try {\n      const token = await getToken();\n      const headers = buildAuthHeaders(token);\n      const response = await fetch(`${API_URL}/minecraft/servers/${server.id}/backup`, {\n        method: 'POST',\n        headers,\n        body: JSON.stringify({ manual: true })\n      });\n\n      if (response.ok) {\n        setSuccess('Backup created successfully!');\n        fetchBackups();\n        onBackupUpdated();\n        setTimeout(() => setSuccess(null), 3000);\n      } else {\n        const errorData = await response.json();\n        setError(errorData.error || 'Failed to create backup');\n      }\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'Failed to create backup');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const downloadBackup = async (backupId: string) => {\n    try {\n      const token = await getToken();\n      const headers = buildAuthHeaders(token);\n      const response = await fetch(`${API_URL}/minecraft/servers/${server.id}/backups/${backupId}/download`, {\n        headers\n      });\n\n      if (response.ok) {\n        const blob = await response.blob();\n        const url = window.URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = `${server.name}_backup_${backupId}.tar.gz`;\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        window.URL.revokeObjectURL(url);\n      } else {\n        setError('Failed to download backup');\n      }\n    } catch (err) {\n      setError('Failed to download backup');\n    }\n  };\n\n  const deleteBackup = async (backupId: string) => {\n    try {\n      const token = await getToken();\n      const headers = buildAuthHeaders(token);\n      const response = await fetch(`${API_URL}/minecraft/servers/${server.id}/backups/${backupId}`, {\n        method: 'DELETE',\n        headers\n      });\n\n      if (response.ok) {\n        setSuccess('Backup deleted successfully!');\n        fetchBackups();\n        setTimeout(() => setSuccess(null), 3000);\n      } else {\n        setError('Failed to delete backup');\n      }\n    } catch (err) {\n      setError('Failed to delete backup');\n    }\n  };\n\n  const updateSchedule = async () => {\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      const token = await getToken();\n      const headers = buildAuthHeaders(token);\n      const response = await fetch(`${API_URL}/minecraft/servers/${server.id}/backup-schedule`, {\n        method: 'PUT',\n        headers,\n        body: JSON.stringify({ schedule })\n      });\n\n      if (response.ok) {\n        setSuccess('Backup schedule updated successfully!');\n        setTimeout(() => setSuccess(null), 3000);\n      } else {\n        const errorData = await response.json();\n        setError(errorData.error || 'Failed to update schedule');\n      }\n    } catch (err) {\n      setError('Failed to update schedule');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const formatFileSize = (bytes: number) => {\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    if (bytes === 0) return '0 Bytes';\n    const i = Math.floor(Math.log(bytes) / Math.log(1024));\n    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];\n  };\n\n  return (\n    <div className=\"card overflow-hidden fade-in-fast\">\n      <div className=\"px-6 lg:px-8 py-6 border-b border-white/10 bg-gradient-to-r from-green-500/10 to-emerald-500/10\">\n        <h2 className=\"text-xl lg:text-2xl font-bold flex items-center gradient-text\">\n          <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 lg:h-6 lg:w-6 mr-3 text-green-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12\" />\n          </svg>\n          Backup Management\n        </h2>\n        <p className=\"text-gray-300 mt-2 font-light text-sm lg:text-base\">Manage server backups and scheduling</p>\n      </div>\n\n      <div className=\"p-6 lg:p-8 space-y-8\">\n        {/* Status Messages */}\n        {error && (\n          <div className=\"p-4 bg-red-500/20 border border-red-500/30 rounded-lg text-red-400 flex items-center\">\n            <svg className=\"h-5 w-5 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n            </svg>\n            {error}\n          </div>\n        )}\n\n        {success && (\n          <div className=\"p-4 bg-green-500/20 border border-green-500/30 rounded-lg text-green-400 flex items-center\">\n            <svg className=\"h-5 w-5 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n            </svg>\n            {success}\n          </div>\n        )}\n\n        {/* Manual Backup */}\n        <div className=\"space-y-4\">\n          <h3 className=\"text-lg font-semibold text-white\">Manual Backup</h3>\n          <div className=\"flex flex-col sm:flex-row gap-4\">\n            <button\n              onClick={createBackup}\n              disabled={isLoading}\n              className=\"flex-1 bg-gradient-to-r from-green-500 to-emerald-500 hover:from-green-600 hover:to-emerald-600 disabled:from-gray-600 disabled:to-gray-600 text-white px-6 py-3 rounded-lg font-semibold transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-green-500/20 disabled:hover:scale-100 disabled:hover:shadow-none disabled:cursor-not-allowed\"\n            >\n              {isLoading ? (\n                <div className=\"flex items-center justify-center\">\n                  <div className=\"animate-spin h-5 w-5 border-2 border-white/30 border-t-white rounded-full mr-2\"></div>\n                  Creating Backup...\n                </div>\n              ) : (\n                <>\n                  <svg className=\"h-5 w-5 mr-2 inline\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12\" />\n                  </svg>\n                  Create Backup Now\n                </>\n              )}\n            </button>\n          </div>\n        </div>\n\n        {/* Backup Schedule */}\n        <div className=\"space-y-4\">\n          <h3 className=\"text-lg font-semibold text-white\">Automatic Backup Schedule</h3>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <div>\n              <label className=\"flex items-center space-x-3 cursor-pointer mb-4\">\n                <input\n                  type=\"checkbox\"\n                  className=\"form-checkbox h-5 w-5 text-green-500\"\n                  checked={schedule.enabled}\n                  onChange={(e) => setSchedule(prev => ({ ...prev, enabled: e.target.checked }))}\n                />\n                <span className=\"text-gray-200 font-medium\">Enable Automatic Backups</span>\n              </label>\n\n              {schedule.enabled && (\n                <div className=\"space-y-4\">\n                  <div>\n                    <label className=\"block text-gray-200 text-sm font-semibold mb-3\">Frequency</label>\n                    <select\n                      className=\"input-field w-full\"\n                      value={schedule.frequency}\n                      onChange={(e) => setSchedule(prev => ({ ...prev, frequency: e.target.value as any }))}\n                    >\n                      <option value=\"hourly\">Every Hour</option>\n                      <option value=\"daily\">Daily</option>\n                      <option value=\"weekly\">Weekly</option>\n                    </select>\n                  </div>\n\n                  {schedule.frequency !== 'hourly' && (\n                    <div>\n                      <label className=\"block text-gray-200 text-sm font-semibold mb-3\">Time</label>\n                      <input\n                        type=\"time\"\n                        className=\"input-field w-full\"\n                        value={schedule.time}\n                        onChange={(e) => setSchedule(prev => ({ ...prev, time: e.target.value }))}\n                      />\n                    </div>\n                  )}\n\n                  {schedule.frequency === 'weekly' && (\n                    <div>\n                      <label className=\"block text-gray-200 text-sm font-semibold mb-3\">Day of Week</label>\n                      <select\n                        className=\"input-field w-full\"\n                        value={schedule.dayOfWeek || 0}\n                        onChange={(e) => setSchedule(prev => ({ ...prev, dayOfWeek: parseInt(e.target.value) }))}\n                      >\n                        <option value={0}>Sunday</option>\n                        <option value={1}>Monday</option>\n                        <option value={2}>Tuesday</option>\n                        <option value={3}>Wednesday</option>\n                        <option value={4}>Thursday</option>\n                        <option value={5}>Friday</option>\n                        <option value={6}>Saturday</option>\n                      </select>\n                    </div>\n                  )}\n                </div>\n              )}\n            </div>\n\n            <div>\n              <div>\n                <label className=\"block text-gray-200 text-sm font-semibold mb-3\">Max Backups to Keep</label>\n                <input\n                  type=\"number\"\n                  className=\"input-field w-full\"\n                  min=\"1\"\n                  max=\"30\"\n                  value={schedule.maxBackups}\n                  onChange={(e) => setSchedule(prev => ({ ...prev, maxBackups: parseInt(e.target.value) }))}\n                />\n                <p className=\"text-gray-400 text-xs mt-1\">Older backups will be automatically deleted</p>\n              </div>\n\n              <button\n                onClick={updateSchedule}\n                disabled={isLoading}\n                className=\"mt-4 w-full bg-blue-500 hover:bg-blue-600 disabled:bg-gray-600 text-white px-4 py-2 rounded-lg font-semibold transition-all duration-300 disabled:cursor-not-allowed\"\n              >\n                Update Schedule\n              </button>\n            </div>\n          </div>\n        </div>\n\n        {/* Backup List */}\n        <div className=\"space-y-4\">\n          <h3 className=\"text-lg font-semibold text-white\">Existing Backups</h3>\n          {backups.length === 0 ? (\n            <div className=\"text-center py-8\">\n              <div className=\"w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br from-green-500/20 to-emerald-500/20 flex items-center justify-center\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-8 w-8 text-green-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5} d=\"M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12\" />\n                </svg>\n              </div>\n              <h4 className=\"text-lg font-semibold text-white mb-2\">No backups found</h4>\n              <p className=\"text-gray-400 font-light\">Create your first backup to get started.</p>\n            </div>\n          ) : (\n            <div className=\"space-y-3\">\n              {backups.map((backup) => (\n                <div key={backup.id} className=\"bg-white/5 rounded-lg p-4 flex items-center justify-between\">\n                  <div>\n                    <h4 className=\"text-white font-medium\">{backup.name}</h4>\n                    <p className=\"text-gray-400 text-sm\">\n                      {formatFileSize(backup.size)} • {new Date(backup.created).toLocaleString()}\n                    </p>\n                  </div>\n                  <div className=\"flex gap-2\">\n                    <button\n                      onClick={() => downloadBackup(backup.id)}\n                      className=\"bg-blue-500/20 hover:bg-blue-500/30 border border-blue-500/30 hover:border-blue-500/50 text-blue-400 hover:text-blue-300 px-3 py-2 rounded-lg text-sm font-semibold transition-all duration-300 hover:scale-105\"\n                    >\n                      Download\n                    </button>\n                    <button\n                      onClick={() => deleteBackup(backup.id)}\n                      className=\"bg-red-500/20 hover:bg-red-500/30 border border-red-500/30 hover:border-red-500/50 text-red-400 hover:text-red-300 px-3 py-2 rounded-lg text-sm font-semibold transition-all duration-300 hover:scale-105\"\n                    >\n                      Delete\n                    </button>\n                  </div>\n                </div>\n              ))}\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMA,MAAM,UAAU,iEAAmC;AAuBpC,SAAS,cAAc,EAAE,MAAM,EAAE,eAAe,EAAsB;IACnF,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IACvD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;QACvD,SAAS;QACT,WAAW;QACX,MAAM;QACN,YAAY;IACd;IACA,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAE3B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;QACA;IACF,GAAG;QAAC,OAAO,EAAE;KAAC;IAEd,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,QAAQ,MAAM;YACpB,MAAM,UAAU,CAAA,GAAA,sHAAA,CAAA,mBAAgB,AAAD,EAAE;YACjC,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,mBAAmB,EAAE,OAAO,EAAE,CAAC,QAAQ,CAAC,EAAE;gBAChF;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,WAAW,KAAK,OAAO,IAAI,EAAE;YAC/B;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;IACF;IAEA,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,QAAQ,MAAM;YACpB,MAAM,UAAU,CAAA,GAAA,sHAAA,CAAA,mBAAgB,AAAD,EAAE;YACjC,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,mBAAmB,EAAE,OAAO,EAAE,CAAC,gBAAgB,CAAC,EAAE;gBACxF;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,YAAY,KAAK,QAAQ,IAAI;YAC/B;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,mCAAmC;QACnD;IACF;IAEA,MAAM,eAAe;QACnB,aAAa;QACb,SAAS;QACT,WAAW;QAEX,IAAI;YACF,MAAM,QAAQ,MAAM;YACpB,MAAM,UAAU,CAAA,GAAA,sHAAA,CAAA,mBAAgB,AAAD,EAAE;YACjC,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,mBAAmB,EAAE,OAAO,EAAE,CAAC,OAAO,CAAC,EAAE;gBAC/E,QAAQ;gBACR;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE,QAAQ;gBAAK;YACtC;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,WAAW;gBACX;gBACA;gBACA,WAAW,IAAM,WAAW,OAAO;YACrC,OAAO;gBACL,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,SAAS,UAAU,KAAK,IAAI;YAC9B;QACF,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,iBAAiB,OAAO;QAC5B,IAAI;YACF,MAAM,QAAQ,MAAM;YACpB,MAAM,UAAU,CAAA,GAAA,sHAAA,CAAA,mBAAgB,AAAD,EAAE;YACjC,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,mBAAmB,EAAE,OAAO,EAAE,CAAC,SAAS,EAAE,SAAS,SAAS,CAAC,EAAE;gBACrG;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC;gBACvC,MAAM,IAAI,SAAS,aAAa,CAAC;gBACjC,EAAE,IAAI,GAAG;gBACT,EAAE,QAAQ,GAAG,GAAG,OAAO,IAAI,CAAC,QAAQ,EAAE,SAAS,OAAO,CAAC;gBACvD,SAAS,IAAI,CAAC,WAAW,CAAC;gBAC1B,EAAE,KAAK;gBACP,SAAS,IAAI,CAAC,WAAW,CAAC;gBAC1B,OAAO,GAAG,CAAC,eAAe,CAAC;YAC7B,OAAO;gBACL,SAAS;YACX;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;QACX;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,MAAM,QAAQ,MAAM;YACpB,MAAM,UAAU,CAAA,GAAA,sHAAA,CAAA,mBAAgB,AAAD,EAAE;YACjC,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,mBAAmB,EAAE,OAAO,EAAE,CAAC,SAAS,EAAE,UAAU,EAAE;gBAC5F,QAAQ;gBACR;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,WAAW;gBACX;gBACA,WAAW,IAAM,WAAW,OAAO;YACrC,OAAO;gBACL,SAAS;YACX;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;QACX;IACF;IAEA,MAAM,iBAAiB;QACrB,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,QAAQ,MAAM;YACpB,MAAM,UAAU,CAAA,GAAA,sHAAA,CAAA,mBAAgB,AAAD,EAAE;YACjC,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,mBAAmB,EAAE,OAAO,EAAE,CAAC,gBAAgB,CAAC,EAAE;gBACxF,QAAQ;gBACR;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAS;YAClC;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,WAAW;gBACX,WAAW,IAAM,WAAW,OAAO;YACrC,OAAO;gBACL,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,SAAS,UAAU,KAAK,IAAI;YAC9B;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,QAAQ;YAAC;YAAS;YAAM;YAAM;SAAK;QACzC,IAAI,UAAU,GAAG,OAAO;QACxB,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAChD,OAAO,KAAK,KAAK,CAAC,QAAQ,KAAK,GAAG,CAAC,MAAM,KAAK,OAAO,MAAM,MAAM,KAAK,CAAC,EAAE;IAC3E;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC;gCAAI,OAAM;gCAA6B,WAAU;gCAA4C,MAAK;gCAAO,SAAQ;gCAAY,QAAO;0CACnI,cAAA,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;4BACjE;;;;;;;kCAGR,8OAAC;wBAAE,WAAU;kCAAqD;;;;;;;;;;;;0BAGpE,8OAAC;gBAAI,WAAU;;oBAEZ,uBACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;gCAAe,MAAK;gCAAe,SAAQ;0CACxD,cAAA,8OAAC;oCAAK,UAAS;oCAAU,GAAE;oCAAoH,UAAS;;;;;;;;;;;4BAEzJ;;;;;;;oBAIJ,yBACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;gCAAe,MAAK;gCAAe,SAAQ;0CACxD,cAAA,8OAAC;oCAAK,UAAS;oCAAU,GAAE;oCAAwI,UAAS;;;;;;;;;;;4BAE7K;;;;;;;kCAKL,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,SAAS;oCACT,UAAU;oCACV,WAAU;8CAET,0BACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;;;;;4CAAuF;;;;;;6DAIxG;;0DACE,8OAAC;gDAAI,WAAU;gDAAsB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAC7E,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;4CACjE;;;;;;;;;;;;;;;;;;;kCAShB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;;kEACf,8OAAC;wDACC,MAAK;wDACL,WAAU;wDACV,SAAS,SAAS,OAAO;wDACzB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,SAAS,EAAE,MAAM,CAAC,OAAO;gEAAC,CAAC;;;;;;kEAE9E,8OAAC;wDAAK,WAAU;kEAA4B;;;;;;;;;;;;4CAG7C,SAAS,OAAO,kBACf,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAAiD;;;;;;0EAClE,8OAAC;gEACC,WAAU;gEACV,OAAO,SAAS,SAAS;gEACzB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4EAAE,GAAG,IAAI;4EAAE,WAAW,EAAE,MAAM,CAAC,KAAK;wEAAQ,CAAC;;kFAEnF,8OAAC;wEAAO,OAAM;kFAAS;;;;;;kFACvB,8OAAC;wEAAO,OAAM;kFAAQ;;;;;;kFACtB,8OAAC;wEAAO,OAAM;kFAAS;;;;;;;;;;;;;;;;;;oDAI1B,SAAS,SAAS,KAAK,0BACtB,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAAiD;;;;;;0EAClE,8OAAC;gEACC,MAAK;gEACL,WAAU;gEACV,OAAO,SAAS,IAAI;gEACpB,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4EAAE,GAAG,IAAI;4EAAE,MAAM,EAAE,MAAM,CAAC,KAAK;wEAAC,CAAC;;;;;;;;;;;;oDAK5E,SAAS,SAAS,KAAK,0BACtB,8OAAC;;0EACC,8OAAC;gEAAM,WAAU;0EAAiD;;;;;;0EAClE,8OAAC;gEACC,WAAU;gEACV,OAAO,SAAS,SAAS,IAAI;gEAC7B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;4EAAE,GAAG,IAAI;4EAAE,WAAW,SAAS,EAAE,MAAM,CAAC,KAAK;wEAAE,CAAC;;kFAEtF,8OAAC;wEAAO,OAAO;kFAAG;;;;;;kFAClB,8OAAC;wEAAO,OAAO;kFAAG;;;;;;kFAClB,8OAAC;wEAAO,OAAO;kFAAG;;;;;;kFAClB,8OAAC;wEAAO,OAAO;kFAAG;;;;;;kFAClB,8OAAC;wEAAO,OAAO;kFAAG;;;;;;kFAClB,8OAAC;wEAAO,OAAO;kFAAG;;;;;;kFAClB,8OAAC;wEAAO,OAAO;kFAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAQ9B,8OAAC;;0DACC,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAAiD;;;;;;kEAClE,8OAAC;wDACC,MAAK;wDACL,WAAU;wDACV,KAAI;wDACJ,KAAI;wDACJ,OAAO,SAAS,UAAU;wDAC1B,UAAU,CAAC,IAAM,YAAY,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,YAAY,SAAS,EAAE,MAAM,CAAC,KAAK;gEAAE,CAAC;;;;;;kEAEzF,8OAAC;wDAAE,WAAU;kEAA6B;;;;;;;;;;;;0DAG5C,8OAAC;gDACC,SAAS;gDACT,UAAU;gDACV,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;kCAQP,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;4BAChD,QAAQ,MAAM,KAAK,kBAClB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,OAAM;4CAA6B,WAAU;4CAAyB,MAAK;4CAAO,SAAQ;4CAAY,QAAO;sDAChH,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAK,GAAE;;;;;;;;;;;;;;;;kDAG3E,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;kDACtD,8OAAC;wCAAE,WAAU;kDAA2B;;;;;;;;;;;qDAG1C,8OAAC;gCAAI,WAAU;0CACZ,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC;wCAAoB,WAAU;;0DAC7B,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA0B,OAAO,IAAI;;;;;;kEACnD,8OAAC;wDAAE,WAAU;;4DACV,eAAe,OAAO,IAAI;4DAAE;4DAAI,IAAI,KAAK,OAAO,OAAO,EAAE,cAAc;;;;;;;;;;;;;0DAG5E,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,SAAS,IAAM,eAAe,OAAO,EAAE;wDACvC,WAAU;kEACX;;;;;;kEAGD,8OAAC;wDACC,SAAS,IAAM,aAAa,OAAO,EAAE;wDACrC,WAAU;kEACX;;;;;;;;;;;;;uCAjBK,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BnC", "debugId": null}}, {"offset": {"line": 3198, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/BlocksConnect/web-panel-blocksconnect/src/components/PlayerManager.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport { Server, buildAuthHeaders } from '../services/api';\n\nconst API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';\n\ninterface PlayerManagerProps {\n  server: Server;\n  onPlayerUpdated: () => void;\n}\n\ninterface Player {\n  uuid: string;\n  username: string;\n  lastSeen?: string;\n  isOnline: boolean;\n  isOp: boolean;\n  isBanned: boolean;\n  isWhitelisted: boolean;\n}\n\ninterface PlayerAction {\n  type: 'kick' | 'ban' | 'unban' | 'op' | 'deop' | 'whitelist' | 'unwhitelist';\n  reason?: string;\n}\n\nexport default function PlayerManager({ server, onPlayerUpdated }: PlayerManagerProps) {\n  const [players, setPlayers] = useState<Player[]>([]);\n  const [onlinePlayers, setOnlinePlayers] = useState<Player[]>([]);\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [success, setSuccess] = useState<string | null>(null);\n  const [newPlayerUsername, setNewPlayerUsername] = useState('');\n  const [activeTab, setActiveTab] = useState<'online' | 'whitelist' | 'banned' | 'ops'>('online');\n  const { getToken } = useAuth();\n\n  useEffect(() => {\n    fetchPlayers();\n    // Refresh online players every 30 seconds\n    const interval = setInterval(fetchOnlinePlayers, 30000);\n    return () => clearInterval(interval);\n  }, [server.id]);\n\n  const fetchPlayers = async () => {\n    try {\n      const token = await getToken();\n      const headers = buildAuthHeaders(token);\n      const response = await fetch(`${API_URL}/minecraft/servers/${server.id}/players`, {\n        headers\n      });\n      \n      if (response.ok) {\n        const data = await response.json();\n        setPlayers(data.players || []);\n        setOnlinePlayers(data.onlinePlayers || []);\n      }\n    } catch (err) {\n      console.error('Error fetching players:', err);\n    }\n  };\n\n  const fetchOnlinePlayers = async () => {\n    try {\n      const token = await getToken();\n      const headers = buildAuthHeaders(token);\n      const response = await fetch(`${API_URL}/minecraft/servers/${server.id}/players`, {\n        headers\n      });\n      \n      if (response.ok) {\n        const data = await response.json();\n        setOnlinePlayers(data.players || []);\n      }\n    } catch (err) {\n      console.error('Error fetching online players:', err);\n    }\n  };\n\n  const executePlayerAction = async (username: string, action: PlayerAction) => {\n    setIsLoading(true);\n    setError(null);\n    setSuccess(null);\n\n    try {\n      const token = await getToken();\n      const headers = buildAuthHeaders(token);\n      const response = await fetch(`${API_URL}/minecraft/servers/${server.id}/players/${username}/action`, {\n        method: 'POST',\n        headers,\n        body: JSON.stringify(action)\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        setSuccess(data.message || `Action ${action.type} executed successfully`);\n        fetchPlayers();\n        onPlayerUpdated();\n        setTimeout(() => setSuccess(null), 3000);\n      } else {\n        const errorData = await response.json();\n        setError(errorData.error || `Failed to execute ${action.type}`);\n      }\n    } catch (err) {\n      setError(err instanceof Error ? err.message : `Failed to execute ${action.type}`);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const addToWhitelist = async () => {\n    if (!newPlayerUsername.trim()) {\n      setError('Please enter a username');\n      return;\n    }\n\n    await executePlayerAction(newPlayerUsername.trim(), { type: 'whitelist' });\n    setNewPlayerUsername('');\n  };\n\n  const getFilteredPlayers = () => {\n    switch (activeTab) {\n      case 'online':\n        return onlinePlayers;\n      case 'whitelist':\n        return players.filter(p => p.isWhitelisted);\n      case 'banned':\n        return players.filter(p => p.isBanned);\n      case 'ops':\n        return players.filter(p => p.isOp);\n      default:\n        return [];\n    }\n  };\n\n  const tabs = [\n    { id: 'online', label: 'Online Players', icon: '🟢', count: onlinePlayers.length },\n    { id: 'whitelist', label: 'Whitelist', icon: '✅', count: players.filter(p => p.isWhitelisted).length },\n    { id: 'banned', label: 'Banned', icon: '🚫', count: players.filter(p => p.isBanned).length },\n    { id: 'ops', label: 'Operators', icon: '👑', count: players.filter(p => p.isOp).length }\n  ] as const;\n\n  return (\n    <div className=\"card overflow-hidden fade-in-fast\">\n      <div className=\"px-6 lg:px-8 py-6 border-b border-white/10 bg-gradient-to-r from-indigo-500/10 to-purple-500/10\">\n        <h2 className=\"text-xl lg:text-2xl font-bold flex items-center gradient-text\">\n          <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 lg:h-6 lg:w-6 mr-3 text-indigo-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z\" />\n          </svg>\n          Player Management\n        </h2>\n        <p className=\"text-gray-300 mt-2 font-light text-sm lg:text-base\">Manage players, whitelist, and permissions</p>\n      </div>\n\n      {/* Tab Navigation */}\n      <div className=\"border-b border-white/10\">\n        <nav className=\"flex space-x-0 overflow-x-auto\">\n          {tabs.map((tab) => (\n            <button\n              key={tab.id}\n              onClick={() => setActiveTab(tab.id)}\n              className={`flex items-center px-4 lg:px-6 py-4 text-sm font-medium border-b-2 transition-colors whitespace-nowrap ${\n                activeTab === tab.id\n                  ? 'border-indigo-500 text-indigo-400 bg-indigo-500/10'\n                  : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'\n              }`}\n            >\n              <span className=\"mr-2\">{tab.icon}</span>\n              {tab.label}\n              <span className=\"ml-2 bg-white/10 text-xs px-2 py-1 rounded-full\">{tab.count}</span>\n            </button>\n          ))}\n        </nav>\n      </div>\n\n      <div className=\"p-6 lg:p-8\">\n        {/* Status Messages */}\n        {error && (\n          <div className=\"mb-6 p-4 bg-red-500/20 border border-red-500/30 rounded-lg text-red-400 flex items-center\">\n            <svg className=\"h-5 w-5 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n            </svg>\n            {error}\n          </div>\n        )}\n\n        {success && (\n          <div className=\"mb-6 p-4 bg-green-500/20 border border-green-500/30 rounded-lg text-green-400 flex items-center\">\n            <svg className=\"h-5 w-5 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n            </svg>\n            {success}\n          </div>\n        )}\n\n        {/* Add to Whitelist */}\n        {activeTab === 'whitelist' && (\n          <div className=\"mb-6 p-4 bg-white/5 rounded-lg\">\n            <h3 className=\"text-lg font-semibold text-white mb-4\">Add Player to Whitelist</h3>\n            <div className=\"flex gap-4\">\n              <input\n                type=\"text\"\n                className=\"input-field flex-1\"\n                placeholder=\"Enter username\"\n                value={newPlayerUsername}\n                onChange={(e) => setNewPlayerUsername(e.target.value)}\n                onKeyPress={(e) => e.key === 'Enter' && addToWhitelist()}\n              />\n              <button\n                onClick={addToWhitelist}\n                disabled={isLoading || !newPlayerUsername.trim()}\n                className=\"bg-indigo-500 hover:bg-indigo-600 disabled:bg-gray-600 text-white px-6 py-2 rounded-lg font-semibold transition-all duration-300 disabled:cursor-not-allowed\"\n              >\n                Add\n              </button>\n            </div>\n          </div>\n        )}\n\n        {/* Player List */}\n        <div className=\"space-y-3\">\n          {getFilteredPlayers().length === 0 ? (\n            <div className=\"text-center py-8\">\n              <div className=\"w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br from-indigo-500/20 to-purple-500/20 flex items-center justify-center\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-8 w-8 text-indigo-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5} d=\"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z\" />\n                </svg>\n              </div>\n              <h4 className=\"text-lg font-semibold text-white mb-2\">No players found</h4>\n              <p className=\"text-gray-400 font-light\">\n                {activeTab === 'online' && 'No players are currently online.'}\n                {activeTab === 'whitelist' && 'No players in whitelist.'}\n                {activeTab === 'banned' && 'No banned players.'}\n                {activeTab === 'ops' && 'No operators assigned.'}\n              </p>\n            </div>\n          ) : (\n            getFilteredPlayers().map((player) => (\n              <div key={player.uuid} className=\"bg-white/5 rounded-lg p-4\">\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center space-x-4\">\n                    <div className=\"w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-500 rounded-lg flex items-center justify-center\">\n                      <span className=\"text-white font-bold text-lg\">\n                        {player.username.charAt(0).toUpperCase()}\n                      </span>\n                    </div>\n                    <div>\n                      <h4 className=\"text-white font-medium flex items-center\">\n                        {player.username}\n                        {player.isOnline && <span className=\"ml-2 w-2 h-2 bg-green-400 rounded-full\"></span>}\n                        {player.isOp && <span className=\"ml-2 text-yellow-400\">👑</span>}\n                      </h4>\n                      <p className=\"text-gray-400 text-sm\">\n                        {player.isOnline ? 'Online' : player.lastSeen ? `Last seen: ${new Date(player.lastSeen).toLocaleDateString()}` : 'Never seen'}\n                      </p>\n                    </div>\n                  </div>\n\n                  <div className=\"flex gap-2\">\n                    {activeTab === 'online' && (\n                      <>\n                        <button\n                          onClick={() => executePlayerAction(player.username, { type: 'kick', reason: 'Kicked by admin' })}\n                          disabled={isLoading}\n                          className=\"bg-yellow-500/20 hover:bg-yellow-500/30 border border-yellow-500/30 hover:border-yellow-500/50 text-yellow-400 hover:text-yellow-300 px-3 py-2 rounded-lg text-sm font-semibold transition-all duration-300 hover:scale-105\"\n                        >\n                          Kick\n                        </button>\n                        <button\n                          onClick={() => executePlayerAction(player.username, { type: 'ban', reason: 'Banned by admin' })}\n                          disabled={isLoading}\n                          className=\"bg-red-500/20 hover:bg-red-500/30 border border-red-500/30 hover:border-red-500/50 text-red-400 hover:text-red-300 px-3 py-2 rounded-lg text-sm font-semibold transition-all duration-300 hover:scale-105\"\n                        >\n                          Ban\n                        </button>\n                      </>\n                    )}\n\n                    {activeTab === 'whitelist' && (\n                      <button\n                        onClick={() => executePlayerAction(player.username, { type: 'unwhitelist' })}\n                        disabled={isLoading}\n                        className=\"bg-red-500/20 hover:bg-red-500/30 border border-red-500/30 hover:border-red-500/50 text-red-400 hover:text-red-300 px-3 py-2 rounded-lg text-sm font-semibold transition-all duration-300 hover:scale-105\"\n                      >\n                        Remove\n                      </button>\n                    )}\n\n                    {activeTab === 'banned' && (\n                      <button\n                        onClick={() => executePlayerAction(player.username, { type: 'unban' })}\n                        disabled={isLoading}\n                        className=\"bg-green-500/20 hover:bg-green-500/30 border border-green-500/30 hover:border-green-500/50 text-green-400 hover:text-green-300 px-3 py-2 rounded-lg text-sm font-semibold transition-all duration-300 hover:scale-105\"\n                      >\n                        Unban\n                      </button>\n                    )}\n\n                    {activeTab === 'ops' && (\n                      <button\n                        onClick={() => executePlayerAction(player.username, { type: 'deop' })}\n                        disabled={isLoading}\n                        className=\"bg-red-500/20 hover:bg-red-500/30 border border-red-500/30 hover:border-red-500/50 text-red-400 hover:text-red-300 px-3 py-2 rounded-lg text-sm font-semibold transition-all duration-300 hover:scale-105\"\n                      >\n                        Remove OP\n                      </button>\n                    )}\n\n                    {!player.isOp && activeTab !== 'ops' && (\n                      <button\n                        onClick={() => executePlayerAction(player.username, { type: 'op' })}\n                        disabled={isLoading}\n                        className=\"bg-yellow-500/20 hover:bg-yellow-500/30 border border-yellow-500/30 hover:border-yellow-500/50 text-yellow-400 hover:text-yellow-300 px-3 py-2 rounded-lg text-sm font-semibold transition-all duration-300 hover:scale-105\"\n                      >\n                        Make OP\n                      </button>\n                    )}\n                  </div>\n                </div>\n              </div>\n            ))\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMA,MAAM,UAAU,iEAAmC;AAsBpC,SAAS,cAAc,EAAE,MAAM,EAAE,eAAe,EAAsB;IACnF,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAC/D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACtD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA6C;IACtF,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAE3B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;QACA,0CAA0C;QAC1C,MAAM,WAAW,YAAY,oBAAoB;QACjD,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC,OAAO,EAAE;KAAC;IAEd,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,QAAQ,MAAM;YACpB,MAAM,UAAU,CAAA,GAAA,sHAAA,CAAA,mBAAgB,AAAD,EAAE;YACjC,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,mBAAmB,EAAE,OAAO,EAAE,CAAC,QAAQ,CAAC,EAAE;gBAChF;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,WAAW,KAAK,OAAO,IAAI,EAAE;gBAC7B,iBAAiB,KAAK,aAAa,IAAI,EAAE;YAC3C;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;IACF;IAEA,MAAM,qBAAqB;QACzB,IAAI;YACF,MAAM,QAAQ,MAAM;YACpB,MAAM,UAAU,CAAA,GAAA,sHAAA,CAAA,mBAAgB,AAAD,EAAE;YACjC,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,mBAAmB,EAAE,OAAO,EAAE,CAAC,QAAQ,CAAC,EAAE;gBAChF;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,iBAAiB,KAAK,OAAO,IAAI,EAAE;YACrC;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,kCAAkC;QAClD;IACF;IAEA,MAAM,sBAAsB,OAAO,UAAkB;QACnD,aAAa;QACb,SAAS;QACT,WAAW;QAEX,IAAI;YACF,MAAM,QAAQ,MAAM;YACpB,MAAM,UAAU,CAAA,GAAA,sHAAA,CAAA,mBAAgB,AAAD,EAAE;YACjC,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,mBAAmB,EAAE,OAAO,EAAE,CAAC,SAAS,EAAE,SAAS,OAAO,CAAC,EAAE;gBACnG,QAAQ;gBACR;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,WAAW,KAAK,OAAO,IAAI,CAAC,OAAO,EAAE,OAAO,IAAI,CAAC,sBAAsB,CAAC;gBACxE;gBACA;gBACA,WAAW,IAAM,WAAW,OAAO;YACrC,OAAO;gBACL,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,SAAS,UAAU,KAAK,IAAI,CAAC,kBAAkB,EAAE,OAAO,IAAI,EAAE;YAChE;QACF,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG,CAAC,kBAAkB,EAAE,OAAO,IAAI,EAAE;QAClF,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,CAAC,kBAAkB,IAAI,IAAI;YAC7B,SAAS;YACT;QACF;QAEA,MAAM,oBAAoB,kBAAkB,IAAI,IAAI;YAAE,MAAM;QAAY;QACxE,qBAAqB;IACvB;IAEA,MAAM,qBAAqB;QACzB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,aAAa;YAC5C,KAAK;gBACH,OAAO,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ;YACvC,KAAK;gBACH,OAAO,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI;YACnC;gBACE,OAAO,EAAE;QACb;IACF;IAEA,MAAM,OAAO;QACX;YAAE,IAAI;YAAU,OAAO;YAAkB,MAAM;YAAM,OAAO,cAAc,MAAM;QAAC;QACjF;YAAE,IAAI;YAAa,OAAO;YAAa,MAAM;YAAK,OAAO,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,aAAa,EAAE,MAAM;QAAC;QACrG;YAAE,IAAI;YAAU,OAAO;YAAU,MAAM;YAAM,OAAO,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,EAAE,MAAM;QAAC;QAC3F;YAAE,IAAI;YAAO,OAAO;YAAa,MAAM;YAAM,OAAO,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,EAAE,MAAM;QAAC;KACxF;IAED,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC;gCAAI,OAAM;gCAA6B,WAAU;gCAA6C,MAAK;gCAAO,SAAQ;gCAAY,QAAO;0CACpI,cAAA,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;4BACjE;;;;;;;kCAGR,8OAAC;wBAAE,WAAU;kCAAqD;;;;;;;;;;;;0BAIpE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACZ,KAAK,GAAG,CAAC,CAAC,oBACT,8OAAC;4BAEC,SAAS,IAAM,aAAa,IAAI,EAAE;4BAClC,WAAW,CAAC,uGAAuG,EACjH,cAAc,IAAI,EAAE,GAChB,uDACA,8EACJ;;8CAEF,8OAAC;oCAAK,WAAU;8CAAQ,IAAI,IAAI;;;;;;gCAC/B,IAAI,KAAK;8CACV,8OAAC;oCAAK,WAAU;8CAAmD,IAAI,KAAK;;;;;;;2BAVvE,IAAI,EAAE;;;;;;;;;;;;;;;0BAgBnB,8OAAC;gBAAI,WAAU;;oBAEZ,uBACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;gCAAe,MAAK;gCAAe,SAAQ;0CACxD,cAAA,8OAAC;oCAAK,UAAS;oCAAU,GAAE;oCAAoH,UAAS;;;;;;;;;;;4BAEzJ;;;;;;;oBAIJ,yBACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;gCAAe,MAAK;gCAAe,SAAQ;0CACxD,cAAA,8OAAC;oCAAK,UAAS;oCAAU,GAAE;oCAAwI,UAAS;;;;;;;;;;;4BAE7K;;;;;;;oBAKJ,cAAc,6BACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CACtD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,MAAK;wCACL,WAAU;wCACV,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,qBAAqB,EAAE,MAAM,CAAC,KAAK;wCACpD,YAAY,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW;;;;;;kDAE1C,8OAAC;wCACC,SAAS;wCACT,UAAU,aAAa,CAAC,kBAAkB,IAAI;wCAC9C,WAAU;kDACX;;;;;;;;;;;;;;;;;;kCAQP,8OAAC;wBAAI,WAAU;kCACZ,qBAAqB,MAAM,KAAK,kBAC/B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,OAAM;wCAA6B,WAAU;wCAA0B,MAAK;wCAAO,SAAQ;wCAAY,QAAO;kDACjH,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAK,GAAE;;;;;;;;;;;;;;;;8CAG3E,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,8OAAC;oCAAE,WAAU;;wCACV,cAAc,YAAY;wCAC1B,cAAc,eAAe;wCAC7B,cAAc,YAAY;wCAC1B,cAAc,SAAS;;;;;;;;;;;;mCAI5B,qBAAqB,GAAG,CAAC,CAAC,uBACxB,8OAAC;gCAAsB,WAAU;0CAC/B,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEACb,OAAO,QAAQ,CAAC,MAAM,CAAC,GAAG,WAAW;;;;;;;;;;;8DAG1C,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;;gEACX,OAAO,QAAQ;gEACf,OAAO,QAAQ,kBAAI,8OAAC;oEAAK,WAAU;;;;;;gEACnC,OAAO,IAAI,kBAAI,8OAAC;oEAAK,WAAU;8EAAuB;;;;;;;;;;;;sEAEzD,8OAAC;4DAAE,WAAU;sEACV,OAAO,QAAQ,GAAG,WAAW,OAAO,QAAQ,GAAG,CAAC,WAAW,EAAE,IAAI,KAAK,OAAO,QAAQ,EAAE,kBAAkB,IAAI,GAAG;;;;;;;;;;;;;;;;;;sDAKvH,8OAAC;4CAAI,WAAU;;gDACZ,cAAc,0BACb;;sEACE,8OAAC;4DACC,SAAS,IAAM,oBAAoB,OAAO,QAAQ,EAAE;oEAAE,MAAM;oEAAQ,QAAQ;gEAAkB;4DAC9F,UAAU;4DACV,WAAU;sEACX;;;;;;sEAGD,8OAAC;4DACC,SAAS,IAAM,oBAAoB,OAAO,QAAQ,EAAE;oEAAE,MAAM;oEAAO,QAAQ;gEAAkB;4DAC7F,UAAU;4DACV,WAAU;sEACX;;;;;;;;gDAMJ,cAAc,6BACb,8OAAC;oDACC,SAAS,IAAM,oBAAoB,OAAO,QAAQ,EAAE;4DAAE,MAAM;wDAAc;oDAC1E,UAAU;oDACV,WAAU;8DACX;;;;;;gDAKF,cAAc,0BACb,8OAAC;oDACC,SAAS,IAAM,oBAAoB,OAAO,QAAQ,EAAE;4DAAE,MAAM;wDAAQ;oDACpE,UAAU;oDACV,WAAU;8DACX;;;;;;gDAKF,cAAc,uBACb,8OAAC;oDACC,SAAS,IAAM,oBAAoB,OAAO,QAAQ,EAAE;4DAAE,MAAM;wDAAO;oDACnE,UAAU;oDACV,WAAU;8DACX;;;;;;gDAKF,CAAC,OAAO,IAAI,IAAI,cAAc,uBAC7B,8OAAC;oDACC,SAAS,IAAM,oBAAoB,OAAO,QAAQ,EAAE;4DAAE,MAAM;wDAAK;oDACjE,UAAU;oDACV,WAAU;8DACX;;;;;;;;;;;;;;;;;;+BA3EC,OAAO,IAAI;;;;;;;;;;;;;;;;;;;;;;AAwFnC", "debugId": null}}, {"offset": {"line": 3787, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/BlocksConnect/web-panel-blocksconnect/src/components/ServerMonitoring.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useRef } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport { Server, buildAuthHeaders } from '../services/api';\n\nconst API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';\n\ninterface ServerMonitoringProps {\n  server: Server;\n}\n\ninterface ServerStats {\n  cpu: number;\n  memory: {\n    used: number;\n    total: number;\n    percentage: number;\n  };\n  disk: {\n    used: number;\n    total: number;\n    percentage: number;\n  };\n  network: {\n    bytesIn: number;\n    bytesOut: number;\n  };\n  players: {\n    online: number;\n    max: number;\n    list: string[];\n  };\n  tps: number;\n  uptime: number;\n  worldSize: number;\n}\n\ninterface ChartData {\n  timestamp: number;\n  cpu: number;\n  memory: number;\n  players: number;\n  tps: number;\n}\n\nexport default function ServerMonitoring({ server }: ServerMonitoringProps) {\n  const [stats, setStats] = useState<ServerStats | null>(null);\n  const [chartData, setChartData] = useState<ChartData[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [autoRefresh, setAutoRefresh] = useState(true);\n  const { getToken } = useAuth();\n  const intervalRef = useRef<NodeJS.Timeout | null>(null);\n\n  useEffect(() => {\n    fetchStats();\n    \n    if (autoRefresh) {\n      intervalRef.current = setInterval(fetchStats, 5000); // Update every 5 seconds\n    }\n\n    return () => {\n      if (intervalRef.current) {\n        clearInterval(intervalRef.current);\n      }\n    };\n  }, [server.id, autoRefresh]);\n\n  const fetchStats = async () => {\n    try {\n      const token = await getToken();\n      const headers = buildAuthHeaders(token);\n      const response = await fetch(`${API_URL}/minecraft/servers/${server.id}/stats`, {\n        headers\n      });\n      \n      if (response.ok) {\n        const data = await response.json();\n        setStats(data.stats);\n        \n        // Add to chart data (keep last 20 points)\n        const newDataPoint: ChartData = {\n          timestamp: Date.now(),\n          cpu: data.stats.cpu,\n          memory: data.stats.memory.percentage,\n          players: data.stats.players.online,\n          tps: data.stats.tps\n        };\n        \n        setChartData(prev => {\n          const updated = [...prev, newDataPoint];\n          return updated.slice(-20); // Keep only last 20 points\n        });\n        \n        setError(null);\n      } else {\n        setError('Failed to fetch server statistics');\n      }\n    } catch (err) {\n      setError('Failed to fetch server statistics');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const formatBytes = (bytes: number) => {\n    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];\n    if (bytes === 0) return '0 Bytes';\n    const i = Math.floor(Math.log(bytes) / Math.log(1024));\n    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];\n  };\n\n  const formatUptime = (seconds: number) => {\n    const days = Math.floor(seconds / 86400);\n    const hours = Math.floor((seconds % 86400) / 3600);\n    const minutes = Math.floor((seconds % 3600) / 60);\n    \n    if (days > 0) return `${days}d ${hours}h ${minutes}m`;\n    if (hours > 0) return `${hours}h ${minutes}m`;\n    return `${minutes}m`;\n  };\n\n  const getStatusColor = (percentage: number) => {\n    if (percentage < 50) return 'text-green-400';\n    if (percentage < 80) return 'text-yellow-400';\n    return 'text-red-400';\n  };\n\n  const getTPSColor = (tps: number) => {\n    if (tps >= 19) return 'text-green-400';\n    if (tps >= 15) return 'text-yellow-400';\n    return 'text-red-400';\n  };\n\n  if (isLoading && !stats) {\n    return (\n      <div className=\"card overflow-hidden fade-in-fast\">\n        <div className=\"px-6 lg:px-8 py-6 border-b border-white/10 bg-gradient-to-r from-cyan-500/10 to-blue-500/10\">\n          <h2 className=\"text-xl lg:text-2xl font-bold flex items-center gradient-text\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 lg:h-6 lg:w-6 mr-3 text-cyan-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n            </svg>\n            Server Monitoring\n          </h2>\n          <p className=\"text-gray-300 mt-2 font-light text-sm lg:text-base\">Real-time server performance metrics</p>\n        </div>\n        <div className=\"p-8 text-center\">\n          <div className=\"animate-spin h-8 w-8 border-4 border-cyan-500/30 border-t-cyan-500 rounded-full mx-auto mb-4\"></div>\n          <p className=\"text-gray-400\">Loading server statistics...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (error && !stats) {\n    return (\n      <div className=\"card overflow-hidden fade-in-fast\">\n        <div className=\"px-6 lg:px-8 py-6 border-b border-white/10 bg-gradient-to-r from-cyan-500/10 to-blue-500/10\">\n          <h2 className=\"text-xl lg:text-2xl font-bold flex items-center gradient-text\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 lg:h-6 lg:w-6 mr-3 text-cyan-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n            </svg>\n            Server Monitoring\n          </h2>\n        </div>\n        <div className=\"p-8 text-center\">\n          <div className=\"w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br from-red-500/20 to-red-600/20 flex items-center justify-center\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-8 w-8 text-red-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\" />\n            </svg>\n          </div>\n          <h4 className=\"text-lg font-semibold text-white mb-2\">Monitoring Unavailable</h4>\n          <p className=\"text-gray-400 font-light mb-4\">{error}</p>\n          <button\n            onClick={fetchStats}\n            className=\"bg-cyan-500 hover:bg-cyan-600 text-white px-4 py-2 rounded-lg font-semibold transition-all duration-300\"\n          >\n            Retry\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"card overflow-hidden fade-in-fast\">\n      <div className=\"px-6 lg:px-8 py-6 border-b border-white/10 bg-gradient-to-r from-cyan-500/10 to-blue-500/10\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h2 className=\"text-xl lg:text-2xl font-bold flex items-center gradient-text\">\n              <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 lg:h-6 lg:w-6 mr-3 text-cyan-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n              </svg>\n              Server Monitoring\n            </h2>\n            <p className=\"text-gray-300 mt-2 font-light text-sm lg:text-base\">Real-time server performance metrics</p>\n          </div>\n          <div className=\"flex items-center gap-4\">\n            <label className=\"flex items-center space-x-2 cursor-pointer\">\n              <input\n                type=\"checkbox\"\n                className=\"form-checkbox h-4 w-4 text-cyan-500\"\n                checked={autoRefresh}\n                onChange={(e) => setAutoRefresh(e.target.checked)}\n              />\n              <span className=\"text-gray-300 text-sm\">Auto-refresh</span>\n            </label>\n            <button\n              onClick={fetchStats}\n              className=\"bg-cyan-500/20 hover:bg-cyan-500/30 border border-cyan-500/30 hover:border-cyan-500/50 text-cyan-400 hover:text-cyan-300 px-3 py-2 rounded-lg text-sm font-semibold transition-all duration-300 hover:scale-105\"\n            >\n              Refresh\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {stats && (\n        <div className=\"p-6 lg:p-8 space-y-8\">\n          {/* Key Metrics */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n            <div className=\"bg-gradient-to-br from-green-500/10 to-emerald-500/10 rounded-lg p-4 border border-green-500/20\">\n              <div className=\"flex items-center justify-between mb-2\">\n                <h3 className=\"text-green-300 font-semibold\">Players Online</h3>\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-green-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z\" />\n                </svg>\n              </div>\n              <p className=\"text-2xl font-bold text-white\">{stats.players.online}/{stats.players.max}</p>\n              <p className=\"text-green-400 text-sm\">{Math.round((stats.players.online / stats.players.max) * 100)}% capacity</p>\n            </div>\n\n            <div className=\"bg-gradient-to-br from-blue-500/10 to-cyan-500/10 rounded-lg p-4 border border-blue-500/20\">\n              <div className=\"flex items-center justify-between mb-2\">\n                <h3 className=\"text-blue-300 font-semibold\">CPU Usage</h3>\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-blue-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z\" />\n                </svg>\n              </div>\n              <p className={`text-2xl font-bold ${getStatusColor(stats.cpu)}`}>{stats.cpu.toFixed(1)}%</p>\n              <div className=\"w-full bg-gray-700 rounded-full h-2 mt-2\">\n                <div className={`h-2 rounded-full ${stats.cpu < 50 ? 'bg-green-500' : stats.cpu < 80 ? 'bg-yellow-500' : 'bg-red-500'}`} style={{ width: `${Math.min(stats.cpu, 100)}%` }}></div>\n              </div>\n            </div>\n\n            <div className=\"bg-gradient-to-br from-purple-500/10 to-pink-500/10 rounded-lg p-4 border border-purple-500/20\">\n              <div className=\"flex items-center justify-between mb-2\">\n                <h3 className=\"text-purple-300 font-semibold\">Memory</h3>\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-purple-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4\" />\n                </svg>\n              </div>\n              <p className={`text-2xl font-bold ${getStatusColor(stats.memory.percentage)}`}>{stats.memory.percentage.toFixed(1)}%</p>\n              <p className=\"text-purple-400 text-sm\">{formatBytes(stats.memory.used)} / {formatBytes(stats.memory.total)}</p>\n            </div>\n\n            <div className=\"bg-gradient-to-br from-yellow-500/10 to-orange-500/10 rounded-lg p-4 border border-yellow-500/20\">\n              <div className=\"flex items-center justify-between mb-2\">\n                <h3 className=\"text-yellow-300 font-semibold\">TPS</h3>\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-yellow-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                </svg>\n              </div>\n              <p className={`text-2xl font-bold ${getTPSColor(stats.tps)}`}>{stats.tps.toFixed(1)}</p>\n              <p className=\"text-yellow-400 text-sm\">Ticks per second</p>\n            </div>\n          </div>\n\n          {/* Additional Stats */}\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n            <div className=\"bg-white/5 rounded-lg p-4\">\n              <h3 className=\"text-white font-semibold mb-3\">Server Info</h3>\n              <div className=\"space-y-2 text-sm\">\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-400\">Uptime:</span>\n                  <span className=\"text-white\">{formatUptime(stats.uptime)}</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-400\">World Size:</span>\n                  <span className=\"text-white\">{formatBytes(stats.worldSize)}</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-400\">Disk Usage:</span>\n                  <span className={getStatusColor(stats.disk.percentage)}>{stats.disk.percentage.toFixed(1)}%</span>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"bg-white/5 rounded-lg p-4\">\n              <h3 className=\"text-white font-semibold mb-3\">Network</h3>\n              <div className=\"space-y-2 text-sm\">\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-400\">Bytes In:</span>\n                  <span className=\"text-white\">{formatBytes(stats.network.bytesIn)}</span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-gray-400\">Bytes Out:</span>\n                  <span className=\"text-white\">{formatBytes(stats.network.bytesOut)}</span>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"bg-white/5 rounded-lg p-4\">\n              <h3 className=\"text-white font-semibold mb-3\">Online Players</h3>\n              <div className=\"space-y-1 text-sm max-h-20 overflow-y-auto\">\n                {stats.players.list.length === 0 ? (\n                  <p className=\"text-gray-400\">No players online</p>\n                ) : (\n                  stats.players.list.map((player, index) => (\n                    <div key={index} className=\"text-white\">{player}</div>\n                  ))\n                )}\n              </div>\n            </div>\n          </div>\n\n          {/* Simple Chart Placeholder */}\n          {chartData.length > 1 && (\n            <div className=\"bg-white/5 rounded-lg p-4\">\n              <h3 className=\"text-white font-semibold mb-4\">Performance History</h3>\n              <div className=\"text-center py-8\">\n                <p className=\"text-gray-400\">Chart visualization would be implemented here</p>\n                <p className=\"text-gray-500 text-sm mt-2\">Showing CPU, Memory, and TPS over time</p>\n              </div>\n            </div>\n          )}\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMA,MAAM,UAAU,iEAAmC;AAwCpC,SAAS,iBAAiB,EAAE,MAAM,EAAyB;IACxE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,EAAE;IAC1D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC3B,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAyB;IAElD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;QAEA,IAAI,aAAa;YACf,YAAY,OAAO,GAAG,YAAY,YAAY,OAAO,yBAAyB;QAChF;QAEA,OAAO;YACL,IAAI,YAAY,OAAO,EAAE;gBACvB,cAAc,YAAY,OAAO;YACnC;QACF;IACF,GAAG;QAAC,OAAO,EAAE;QAAE;KAAY;IAE3B,MAAM,aAAa;QACjB,IAAI;YACF,MAAM,QAAQ,MAAM;YACpB,MAAM,UAAU,CAAA,GAAA,sHAAA,CAAA,mBAAgB,AAAD,EAAE;YACjC,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,mBAAmB,EAAE,OAAO,EAAE,CAAC,MAAM,CAAC,EAAE;gBAC9E;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,SAAS,KAAK,KAAK;gBAEnB,0CAA0C;gBAC1C,MAAM,eAA0B;oBAC9B,WAAW,KAAK,GAAG;oBACnB,KAAK,KAAK,KAAK,CAAC,GAAG;oBACnB,QAAQ,KAAK,KAAK,CAAC,MAAM,CAAC,UAAU;oBACpC,SAAS,KAAK,KAAK,CAAC,OAAO,CAAC,MAAM;oBAClC,KAAK,KAAK,KAAK,CAAC,GAAG;gBACrB;gBAEA,aAAa,CAAA;oBACX,MAAM,UAAU;2BAAI;wBAAM;qBAAa;oBACvC,OAAO,QAAQ,KAAK,CAAC,CAAC,KAAK,2BAA2B;gBACxD;gBAEA,SAAS;YACX,OAAO;gBACL,SAAS;YACX;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,cAAc,CAAC;QACnB,MAAM,QAAQ;YAAC;YAAS;YAAM;YAAM;YAAM;SAAK;QAC/C,IAAI,UAAU,GAAG,OAAO;QACxB,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAChD,OAAO,KAAK,KAAK,CAAC,QAAQ,KAAK,GAAG,CAAC,MAAM,KAAK,OAAO,MAAM,MAAM,KAAK,CAAC,EAAE;IAC3E;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,OAAO,KAAK,KAAK,CAAC,UAAU;QAClC,MAAM,QAAQ,KAAK,KAAK,CAAC,AAAC,UAAU,QAAS;QAC7C,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,UAAU,OAAQ;QAE9C,IAAI,OAAO,GAAG,OAAO,GAAG,KAAK,EAAE,EAAE,MAAM,EAAE,EAAE,QAAQ,CAAC,CAAC;QACrD,IAAI,QAAQ,GAAG,OAAO,GAAG,MAAM,EAAE,EAAE,QAAQ,CAAC,CAAC;QAC7C,OAAO,GAAG,QAAQ,CAAC,CAAC;IACtB;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,aAAa,IAAI,OAAO;QAC5B,IAAI,aAAa,IAAI,OAAO;QAC5B,OAAO;IACT;IAEA,MAAM,cAAc,CAAC;QACnB,IAAI,OAAO,IAAI,OAAO;QACtB,IAAI,OAAO,IAAI,OAAO;QACtB,OAAO;IACT;IAEA,IAAI,aAAa,CAAC,OAAO;QACvB,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC;oCAAI,OAAM;oCAA6B,WAAU;oCAA2C,MAAK;oCAAO,SAAQ;oCAAY,QAAO;8CAClI,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;gCACjE;;;;;;;sCAGR,8OAAC;4BAAE,WAAU;sCAAqD;;;;;;;;;;;;8BAEpE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;;;;;;;IAIrC;IAEA,IAAI,SAAS,CAAC,OAAO;QACnB,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAG,WAAU;;0CACZ,8OAAC;gCAAI,OAAM;gCAA6B,WAAU;gCAA2C,MAAK;gCAAO,SAAQ;gCAAY,QAAO;0CAClI,cAAA,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;4BACjE;;;;;;;;;;;;8BAIV,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,OAAM;gCAA6B,WAAU;gCAAuB,MAAK;gCAAO,SAAQ;gCAAY,QAAO;0CAC9G,cAAA,8OAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;sCAGzE,8OAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,8OAAC;4BAAE,WAAU;sCAAiC;;;;;;sCAC9C,8OAAC;4BACC,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;;;;;;;IAMT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;4CAAI,OAAM;4CAA6B,WAAU;4CAA2C,MAAK;4CAAO,SAAQ;4CAAY,QAAO;sDAClI,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;wCACjE;;;;;;;8CAGR,8OAAC;oCAAE,WAAU;8CAAqD;;;;;;;;;;;;sCAEpE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAM,WAAU;;sDACf,8OAAC;4CACC,MAAK;4CACL,WAAU;4CACV,SAAS;4CACT,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,OAAO;;;;;;sDAElD,8OAAC;4CAAK,WAAU;sDAAwB;;;;;;;;;;;;8CAE1C,8OAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;YAON,uBACC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA+B;;;;;;0DAC7C,8OAAC;gDAAI,OAAM;gDAA6B,WAAU;gDAAyB,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DAChH,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;;kDAGzE,8OAAC;wCAAE,WAAU;;4CAAiC,MAAM,OAAO,CAAC,MAAM;4CAAC;4CAAE,MAAM,OAAO,CAAC,GAAG;;;;;;;kDACtF,8OAAC;wCAAE,WAAU;;4CAA0B,KAAK,KAAK,CAAC,AAAC,MAAM,OAAO,CAAC,MAAM,GAAG,MAAM,OAAO,CAAC,GAAG,GAAI;4CAAK;;;;;;;;;;;;;0CAGtG,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA8B;;;;;;0DAC5C,8OAAC;gDAAI,OAAM;gDAA6B,WAAU;gDAAwB,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DAC/G,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;;kDAGzE,8OAAC;wCAAE,WAAW,CAAC,mBAAmB,EAAE,eAAe,MAAM,GAAG,GAAG;;4CAAG,MAAM,GAAG,CAAC,OAAO,CAAC;4CAAG;;;;;;;kDACvF,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAW,CAAC,iBAAiB,EAAE,MAAM,GAAG,GAAG,KAAK,iBAAiB,MAAM,GAAG,GAAG,KAAK,kBAAkB,cAAc;4CAAE,OAAO;gDAAE,OAAO,GAAG,KAAK,GAAG,CAAC,MAAM,GAAG,EAAE,KAAK,CAAC,CAAC;4CAAC;;;;;;;;;;;;;;;;;0CAI5K,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAgC;;;;;;0DAC9C,8OAAC;gDAAI,OAAM;gDAA6B,WAAU;gDAA0B,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DACjH,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;;kDAGzE,8OAAC;wCAAE,WAAW,CAAC,mBAAmB,EAAE,eAAe,MAAM,MAAM,CAAC,UAAU,GAAG;;4CAAG,MAAM,MAAM,CAAC,UAAU,CAAC,OAAO,CAAC;4CAAG;;;;;;;kDACnH,8OAAC;wCAAE,WAAU;;4CAA2B,YAAY,MAAM,MAAM,CAAC,IAAI;4CAAE;4CAAI,YAAY,MAAM,MAAM,CAAC,KAAK;;;;;;;;;;;;;0CAG3G,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAgC;;;;;;0DAC9C,8OAAC;gDAAI,OAAM;gDAA6B,WAAU;gDAA0B,MAAK;gDAAO,SAAQ;gDAAY,QAAO;0DACjH,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;;;;;;;kDAGzE,8OAAC;wCAAE,WAAW,CAAC,mBAAmB,EAAE,YAAY,MAAM,GAAG,GAAG;kDAAG,MAAM,GAAG,CAAC,OAAO,CAAC;;;;;;kDACjF,8OAAC;wCAAE,WAAU;kDAA0B;;;;;;;;;;;;;;;;;;kCAK3C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAgC;;;;;;kDAC9C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,8OAAC;wDAAK,WAAU;kEAAc,aAAa,MAAM,MAAM;;;;;;;;;;;;0DAEzD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,8OAAC;wDAAK,WAAU;kEAAc,YAAY,MAAM,SAAS;;;;;;;;;;;;0DAE3D,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,8OAAC;wDAAK,WAAW,eAAe,MAAM,IAAI,CAAC,UAAU;;4DAAI,MAAM,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;4DAAG;;;;;;;;;;;;;;;;;;;;;;;;;0CAKhG,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAgC;;;;;;kDAC9C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,8OAAC;wDAAK,WAAU;kEAAc,YAAY,MAAM,OAAO,CAAC,OAAO;;;;;;;;;;;;0DAEjE,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,8OAAC;wDAAK,WAAU;kEAAc,YAAY,MAAM,OAAO,CAAC,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;0CAKtE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAAgC;;;;;;kDAC9C,8OAAC;wCAAI,WAAU;kDACZ,MAAM,OAAO,CAAC,IAAI,CAAC,MAAM,KAAK,kBAC7B,8OAAC;4CAAE,WAAU;sDAAgB;;;;;mDAE7B,MAAM,OAAO,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,QAAQ,sBAC9B,8OAAC;gDAAgB,WAAU;0DAAc;+CAA/B;;;;;;;;;;;;;;;;;;;;;;oBAQnB,UAAU,MAAM,GAAG,mBAClB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAgC;;;;;;0CAC9C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAAgB;;;;;;kDAC7B,8OAAC;wCAAE,WAAU;kDAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ1D", "debugId": null}}, {"offset": {"line": 4771, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/BlocksConnect/web-panel-blocksconnect/src/components/FileManager.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { useAuth } from '../contexts/AuthContext';\nimport { Server, buildAuthHeaders } from '../services/api';\n\nconst API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';\n\ninterface FileManagerProps {\n  server: Server;\n}\n\ninterface FileItem {\n  name: string;\n  type: 'file' | 'directory';\n  size: number;\n  modified: string;\n  path: string;\n  permissions: string;\n}\n\ninterface BreadcrumbItem {\n  name: string;\n  path: string;\n}\n\nexport default function FileManager({ server }: FileManagerProps) {\n  const [files, setFiles] = useState<FileItem[]>([]);\n  const [currentPath, setCurrentPath] = useState('/');\n  const [breadcrumbs, setBreadcrumbs] = useState<BreadcrumbItem[]>([{ name: 'Root', path: '/' }]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [success, setSuccess] = useState<string | null>(null);\n  const [selectedFile, setSelectedFile] = useState<FileItem | null>(null);\n  const [showUpload, setShowUpload] = useState(false);\n  const [uploadFile, setUploadFile] = useState<File | null>(null);\n  const [newFolderName, setNewFolderName] = useState('');\n  const [showNewFolder, setShowNewFolder] = useState(false);\n  const { getToken } = useAuth();\n\n  useEffect(() => {\n    fetchFiles(currentPath);\n  }, [currentPath, server.id]);\n\n  const fetchFiles = async (path: string) => {\n    setIsLoading(true);\n    setError(null);\n\n    try {\n      const token = await getToken();\n      const headers = buildAuthHeaders(token);\n      const response = await fetch(`${API_URL}/minecraft/servers/${server.id}/files?path=${encodeURIComponent(path)}`, {\n        headers\n      });\n      \n      if (response.ok) {\n        const data = await response.json();\n        setFiles(data.files || []);\n        updateBreadcrumbs(path);\n      } else {\n        setError('Failed to load files');\n      }\n    } catch (err) {\n      setError('Failed to load files');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const updateBreadcrumbs = (path: string) => {\n    const parts = path.split('/').filter(part => part !== '');\n    const breadcrumbs: BreadcrumbItem[] = [{ name: 'Root', path: '/' }];\n    \n    let currentPath = '';\n    parts.forEach(part => {\n      currentPath += '/' + part;\n      breadcrumbs.push({ name: part, path: currentPath });\n    });\n    \n    setBreadcrumbs(breadcrumbs);\n  };\n\n  const navigateToPath = (path: string) => {\n    setCurrentPath(path);\n    setSelectedFile(null);\n  };\n\n  const handleFileClick = (file: FileItem) => {\n    if (file.type === 'directory') {\n      navigateToPath(file.path);\n    } else {\n      setSelectedFile(file);\n    }\n  };\n\n  const downloadFile = async (file: FileItem) => {\n    try {\n      const token = await getToken();\n      const headers = buildAuthHeaders(token);\n      const response = await fetch(`${API_URL}/minecraft/servers/${server.id}/files/download?path=${encodeURIComponent(file.path)}`, {\n        headers\n      });\n\n      if (response.ok) {\n        const blob = await response.blob();\n        const url = window.URL.createObjectURL(blob);\n        const a = document.createElement('a');\n        a.href = url;\n        a.download = file.name;\n        document.body.appendChild(a);\n        a.click();\n        document.body.removeChild(a);\n        window.URL.revokeObjectURL(url);\n      } else {\n        setError('Failed to download file');\n      }\n    } catch (err) {\n      setError('Failed to download file');\n    }\n  };\n\n  const deleteFile = async (file: FileItem) => {\n    if (!confirm(`Are you sure you want to delete ${file.name}?`)) return;\n\n    try {\n      const token = await getToken();\n      const headers = buildAuthHeaders(token);\n      const response = await fetch(`${API_URL}/minecraft/servers/${server.id}/files?path=${encodeURIComponent(file.path)}`, {\n        method: 'DELETE',\n        headers\n      });\n\n      if (response.ok) {\n        setSuccess(`${file.name} deleted successfully`);\n        fetchFiles(currentPath);\n        setTimeout(() => setSuccess(null), 3000);\n      } else {\n        setError('Failed to delete file');\n      }\n    } catch (err) {\n      setError('Failed to delete file');\n    }\n  };\n\n  const uploadFileToServer = async () => {\n    if (!uploadFile) return;\n\n    const formData = new FormData();\n    formData.append('file', uploadFile);\n    formData.append('path', currentPath);\n\n    try {\n      const token = await getToken();\n      const response = await fetch(`${API_URL}/minecraft/servers/${server.id}/files/upload`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${token}`\n        },\n        body: formData\n      });\n\n      if (response.ok) {\n        setSuccess('File uploaded successfully');\n        setShowUpload(false);\n        setUploadFile(null);\n        fetchFiles(currentPath);\n        setTimeout(() => setSuccess(null), 3000);\n      } else {\n        setError('Failed to upload file');\n      }\n    } catch (err) {\n      setError('Failed to upload file');\n    }\n  };\n\n  const createFolder = async () => {\n    if (!newFolderName.trim()) return;\n\n    try {\n      const token = await getToken();\n      const headers = buildAuthHeaders(token);\n      const response = await fetch(`${API_URL}/minecraft/servers/${server.id}/files/folder`, {\n        method: 'POST',\n        headers,\n        body: JSON.stringify({\n          path: currentPath,\n          name: newFolderName.trim()\n        })\n      });\n\n      if (response.ok) {\n        setSuccess('Folder created successfully');\n        setShowNewFolder(false);\n        setNewFolderName('');\n        fetchFiles(currentPath);\n        setTimeout(() => setSuccess(null), 3000);\n      } else {\n        setError('Failed to create folder');\n      }\n    } catch (err) {\n      setError('Failed to create folder');\n    }\n  };\n\n  const formatFileSize = (bytes: number) => {\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    if (bytes === 0) return '0 Bytes';\n    const i = Math.floor(Math.log(bytes) / Math.log(1024));\n    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];\n  };\n\n  const getFileIcon = (file: FileItem) => {\n    if (file.type === 'directory') {\n      return (\n        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-blue-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-5l-2-2H5a2 2 0 00-2 2z\" />\n        </svg>\n      );\n    }\n\n    const extension = file.name.split('.').pop()?.toLowerCase();\n    switch (extension) {\n      case 'txt':\n      case 'log':\n      case 'properties':\n      case 'yml':\n      case 'yaml':\n      case 'json':\n        return (\n          <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-gray-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n          </svg>\n        );\n      case 'jar':\n        return (\n          <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-orange-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10\" />\n          </svg>\n        );\n      default:\n        return (\n          <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-gray-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z\" />\n          </svg>\n        );\n    }\n  };\n\n  return (\n    <div className=\"card overflow-hidden fade-in-fast\">\n      <div className=\"px-6 lg:px-8 py-6 border-b border-white/10 bg-gradient-to-r from-orange-500/10 to-red-500/10\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h2 className=\"text-xl lg:text-2xl font-bold flex items-center gradient-text\">\n              <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 lg:h-6 lg:w-6 mr-3 text-orange-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-5l-2-2H5a2 2 0 00-2 2z\" />\n              </svg>\n              File Manager\n            </h2>\n            <p className=\"text-gray-300 mt-2 font-light text-sm lg:text-base\">Browse and manage server files</p>\n          </div>\n          <div className=\"flex gap-2\">\n            <button\n              onClick={() => setShowNewFolder(true)}\n              className=\"bg-blue-500/20 hover:bg-blue-500/30 border border-blue-500/30 hover:border-blue-500/50 text-blue-400 hover:text-blue-300 px-3 py-2 rounded-lg text-sm font-semibold transition-all duration-300 hover:scale-105\"\n            >\n              New Folder\n            </button>\n            <button\n              onClick={() => setShowUpload(true)}\n              className=\"bg-green-500/20 hover:bg-green-500/30 border border-green-500/30 hover:border-green-500/50 text-green-400 hover:text-green-300 px-3 py-2 rounded-lg text-sm font-semibold transition-all duration-300 hover:scale-105\"\n            >\n              Upload\n            </button>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"p-6 lg:p-8\">\n        {/* Status Messages */}\n        {error && (\n          <div className=\"mb-6 p-4 bg-red-500/20 border border-red-500/30 rounded-lg text-red-400 flex items-center\">\n            <svg className=\"h-5 w-5 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n            </svg>\n            {error}\n          </div>\n        )}\n\n        {success && (\n          <div className=\"mb-6 p-4 bg-green-500/20 border border-green-500/30 rounded-lg text-green-400 flex items-center\">\n            <svg className=\"h-5 w-5 mr-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n              <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n            </svg>\n            {success}\n          </div>\n        )}\n\n        {/* Breadcrumbs */}\n        <div className=\"mb-6\">\n          <nav className=\"flex\" aria-label=\"Breadcrumb\">\n            <ol className=\"flex items-center space-x-2\">\n              {breadcrumbs.map((crumb, index) => (\n                <li key={index} className=\"flex items-center\">\n                  {index > 0 && (\n                    <svg className=\"h-4 w-4 text-gray-400 mx-2\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                      <path fillRule=\"evenodd\" d=\"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z\" clipRule=\"evenodd\" />\n                    </svg>\n                  )}\n                  <button\n                    onClick={() => navigateToPath(crumb.path)}\n                    className={`text-sm font-medium transition-colors ${\n                      index === breadcrumbs.length - 1\n                        ? 'text-white cursor-default'\n                        : 'text-gray-400 hover:text-white'\n                    }`}\n                  >\n                    {crumb.name}\n                  </button>\n                </li>\n              ))}\n            </ol>\n          </nav>\n        </div>\n\n        {/* Upload Modal */}\n        {showUpload && (\n          <div className=\"fixed inset-0 bg-black/50 flex items-center justify-center z-50\">\n            <div className=\"bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4\">\n              <h3 className=\"text-lg font-semibold text-white mb-4\">Upload File</h3>\n              <div className=\"space-y-4\">\n                <div>\n                  <label className=\"block text-gray-200 text-sm font-semibold mb-2\">Select File</label>\n                  <input\n                    type=\"file\"\n                    onChange={(e) => setUploadFile(e.target.files?.[0] || null)}\n                    className=\"w-full text-gray-300 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-semibold file:bg-blue-500 file:text-white hover:file:bg-blue-600\"\n                  />\n                </div>\n                <div className=\"flex gap-4\">\n                  <button\n                    onClick={uploadFileToServer}\n                    disabled={!uploadFile}\n                    className=\"flex-1 bg-green-500 hover:bg-green-600 disabled:bg-gray-600 text-white px-4 py-2 rounded-lg font-semibold transition-all duration-300 disabled:cursor-not-allowed\"\n                  >\n                    Upload\n                  </button>\n                  <button\n                    onClick={() => {\n                      setShowUpload(false);\n                      setUploadFile(null);\n                    }}\n                    className=\"flex-1 bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-semibold transition-all duration-300\"\n                  >\n                    Cancel\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* New Folder Modal */}\n        {showNewFolder && (\n          <div className=\"fixed inset-0 bg-black/50 flex items-center justify-center z-50\">\n            <div className=\"bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4\">\n              <h3 className=\"text-lg font-semibold text-white mb-4\">Create New Folder</h3>\n              <div className=\"space-y-4\">\n                <div>\n                  <label className=\"block text-gray-200 text-sm font-semibold mb-2\">Folder Name</label>\n                  <input\n                    type=\"text\"\n                    className=\"input-field w-full\"\n                    placeholder=\"Enter folder name\"\n                    value={newFolderName}\n                    onChange={(e) => setNewFolderName(e.target.value)}\n                    onKeyPress={(e) => e.key === 'Enter' && createFolder()}\n                  />\n                </div>\n                <div className=\"flex gap-4\">\n                  <button\n                    onClick={createFolder}\n                    disabled={!newFolderName.trim()}\n                    className=\"flex-1 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-600 text-white px-4 py-2 rounded-lg font-semibold transition-all duration-300 disabled:cursor-not-allowed\"\n                  >\n                    Create\n                  </button>\n                  <button\n                    onClick={() => {\n                      setShowNewFolder(false);\n                      setNewFolderName('');\n                    }}\n                    className=\"flex-1 bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-semibold transition-all duration-300\"\n                  >\n                    Cancel\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* File List */}\n        {isLoading ? (\n          <div className=\"text-center py-8\">\n            <div className=\"animate-spin h-8 w-8 border-4 border-orange-500/30 border-t-orange-500 rounded-full mx-auto mb-4\"></div>\n            <p className=\"text-gray-400\">Loading files...</p>\n          </div>\n        ) : files.length === 0 ? (\n          <div className=\"text-center py-8\">\n            <div className=\"w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br from-orange-500/20 to-red-500/20 flex items-center justify-center\">\n              <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-8 w-8 text-orange-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5} d=\"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-5l-2-2H5a2 2 0 00-2 2z\" />\n              </svg>\n            </div>\n            <h4 className=\"text-lg font-semibold text-white mb-2\">Empty Directory</h4>\n            <p className=\"text-gray-400 font-light\">This directory contains no files or folders.</p>\n          </div>\n        ) : (\n          <div className=\"space-y-2\">\n            {files.map((file, index) => (\n              <div\n                key={index}\n                className=\"bg-white/5 hover:bg-white/10 rounded-lg p-4 transition-colors cursor-pointer\"\n                onClick={() => handleFileClick(file)}\n              >\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center space-x-3\">\n                    {getFileIcon(file)}\n                    <div>\n                      <h4 className=\"text-white font-medium\">{file.name}</h4>\n                      <p className=\"text-gray-400 text-sm\">\n                        {file.type === 'file' ? formatFileSize(file.size) : 'Directory'} • \n                        Modified {new Date(file.modified).toLocaleDateString()}\n                      </p>\n                    </div>\n                  </div>\n                  \n                  {file.type === 'file' && (\n                    <div className=\"flex gap-2\" onClick={(e) => e.stopPropagation()}>\n                      <button\n                        onClick={() => downloadFile(file)}\n                        className=\"bg-blue-500/20 hover:bg-blue-500/30 border border-blue-500/30 hover:border-blue-500/50 text-blue-400 hover:text-blue-300 px-3 py-2 rounded-lg text-sm font-semibold transition-all duration-300 hover:scale-105\"\n                      >\n                        Download\n                      </button>\n                      <button\n                        onClick={() => deleteFile(file)}\n                        className=\"bg-red-500/20 hover:bg-red-500/30 border border-red-500/30 hover:border-red-500/50 text-red-400 hover:text-red-300 px-3 py-2 rounded-lg text-sm font-semibold transition-all duration-300 hover:scale-105\"\n                      >\n                        Delete\n                      </button>\n                    </div>\n                  )}\n                </div>\n              </div>\n            ))}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAMA,MAAM,UAAU,iEAAmC;AAoBpC,SAAS,YAAY,EAAE,MAAM,EAAoB;IAC9D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;QAAC;YAAE,MAAM;YAAQ,MAAM;QAAI;KAAE;IAC9F,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACtD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IAClE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC1D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAE3B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;IACb,GAAG;QAAC;QAAa,OAAO,EAAE;KAAC;IAE3B,MAAM,aAAa,OAAO;QACxB,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,QAAQ,MAAM;YACpB,MAAM,UAAU,CAAA,GAAA,sHAAA,CAAA,mBAAgB,AAAD,EAAE;YACjC,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,mBAAmB,EAAE,OAAO,EAAE,CAAC,YAAY,EAAE,mBAAmB,OAAO,EAAE;gBAC/G;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,SAAS,KAAK,KAAK,IAAI,EAAE;gBACzB,kBAAkB;YACpB,OAAO;gBACL,SAAS;YACX;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,QAAQ,KAAK,KAAK,CAAC,KAAK,MAAM,CAAC,CAAA,OAAQ,SAAS;QACtD,MAAM,cAAgC;YAAC;gBAAE,MAAM;gBAAQ,MAAM;YAAI;SAAE;QAEnE,IAAI,cAAc;QAClB,MAAM,OAAO,CAAC,CAAA;YACZ,eAAe,MAAM;YACrB,YAAY,IAAI,CAAC;gBAAE,MAAM;gBAAM,MAAM;YAAY;QACnD;QAEA,eAAe;IACjB;IAEA,MAAM,iBAAiB,CAAC;QACtB,eAAe;QACf,gBAAgB;IAClB;IAEA,MAAM,kBAAkB,CAAC;QACvB,IAAI,KAAK,IAAI,KAAK,aAAa;YAC7B,eAAe,KAAK,IAAI;QAC1B,OAAO;YACL,gBAAgB;QAClB;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,IAAI;YACF,MAAM,QAAQ,MAAM;YACpB,MAAM,UAAU,CAAA,GAAA,sHAAA,CAAA,mBAAgB,AAAD,EAAE;YACjC,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,mBAAmB,EAAE,OAAO,EAAE,CAAC,qBAAqB,EAAE,mBAAmB,KAAK,IAAI,GAAG,EAAE;gBAC7H;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC;gBACvC,MAAM,IAAI,SAAS,aAAa,CAAC;gBACjC,EAAE,IAAI,GAAG;gBACT,EAAE,QAAQ,GAAG,KAAK,IAAI;gBACtB,SAAS,IAAI,CAAC,WAAW,CAAC;gBAC1B,EAAE,KAAK;gBACP,SAAS,IAAI,CAAC,WAAW,CAAC;gBAC1B,OAAO,GAAG,CAAC,eAAe,CAAC;YAC7B,OAAO;gBACL,SAAS;YACX;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;QACX;IACF;IAEA,MAAM,aAAa,OAAO;QACxB,IAAI,CAAC,QAAQ,CAAC,gCAAgC,EAAE,KAAK,IAAI,CAAC,CAAC,CAAC,GAAG;QAE/D,IAAI;YACF,MAAM,QAAQ,MAAM;YACpB,MAAM,UAAU,CAAA,GAAA,sHAAA,CAAA,mBAAgB,AAAD,EAAE;YACjC,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,mBAAmB,EAAE,OAAO,EAAE,CAAC,YAAY,EAAE,mBAAmB,KAAK,IAAI,GAAG,EAAE;gBACpH,QAAQ;gBACR;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,WAAW,GAAG,KAAK,IAAI,CAAC,qBAAqB,CAAC;gBAC9C,WAAW;gBACX,WAAW,IAAM,WAAW,OAAO;YACrC,OAAO;gBACL,SAAS;YACX;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;QACX;IACF;IAEA,MAAM,qBAAqB;QACzB,IAAI,CAAC,YAAY;QAEjB,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ;QACxB,SAAS,MAAM,CAAC,QAAQ;QAExB,IAAI;YACF,MAAM,QAAQ,MAAM;YACpB,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,mBAAmB,EAAE,OAAO,EAAE,CAAC,aAAa,CAAC,EAAE;gBACrF,QAAQ;gBACR,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,OAAO;gBACpC;gBACA,MAAM;YACR;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,WAAW;gBACX,cAAc;gBACd,cAAc;gBACd,WAAW;gBACX,WAAW,IAAM,WAAW,OAAO;YACrC,OAAO;gBACL,SAAS;YACX;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;QACX;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,CAAC,cAAc,IAAI,IAAI;QAE3B,IAAI;YACF,MAAM,QAAQ,MAAM;YACpB,MAAM,UAAU,CAAA,GAAA,sHAAA,CAAA,mBAAgB,AAAD,EAAE;YACjC,MAAM,WAAW,MAAM,MAAM,GAAG,QAAQ,mBAAmB,EAAE,OAAO,EAAE,CAAC,aAAa,CAAC,EAAE;gBACrF,QAAQ;gBACR;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,MAAM;oBACN,MAAM,cAAc,IAAI;gBAC1B;YACF;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,WAAW;gBACX,iBAAiB;gBACjB,iBAAiB;gBACjB,WAAW;gBACX,WAAW,IAAM,WAAW,OAAO;YACrC,OAAO;gBACL,SAAS;YACX;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;QACX;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,QAAQ;YAAC;YAAS;YAAM;YAAM;SAAK;QACzC,IAAI,UAAU,GAAG,OAAO;QACxB,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAChD,OAAO,KAAK,KAAK,CAAC,QAAQ,KAAK,GAAG,CAAC,MAAM,KAAK,OAAO,MAAM,MAAM,KAAK,CAAC,EAAE;IAC3E;IAEA,MAAM,cAAc,CAAC;QACnB,IAAI,KAAK,IAAI,KAAK,aAAa;YAC7B,qBACE,8OAAC;gBAAI,OAAM;gBAA6B,WAAU;gBAAwB,MAAK;gBAAO,SAAQ;gBAAY,QAAO;0BAC/G,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;QAG3E;QAEA,MAAM,YAAY,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI;QAC9C,OAAQ;YACN,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,qBACE,8OAAC;oBAAI,OAAM;oBAA6B,WAAU;oBAAwB,MAAK;oBAAO,SAAQ;oBAAY,QAAO;8BAC/G,cAAA,8OAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;YAG3E,KAAK;gBACH,qBACE,8OAAC;oBAAI,OAAM;oBAA6B,WAAU;oBAA0B,MAAK;oBAAO,SAAQ;oBAAY,QAAO;8BACjH,cAAA,8OAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;YAG3E;gBACE,qBACE,8OAAC;oBAAI,OAAM;oBAA6B,WAAU;oBAAwB,MAAK;oBAAO,SAAQ;oBAAY,QAAO;8BAC/G,cAAA,8OAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;QAG7E;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;4CAAI,OAAM;4CAA6B,WAAU;4CAA6C,MAAK;4CAAO,SAAQ;4CAAY,QAAO;sDACpI,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;wCACjE;;;;;;;8CAGR,8OAAC;oCAAE,WAAU;8CAAqD;;;;;;;;;;;;sCAEpE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,SAAS,IAAM,iBAAiB;oCAChC,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCACC,SAAS,IAAM,cAAc;oCAC7B,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;0BAOP,8OAAC;gBAAI,WAAU;;oBAEZ,uBACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;gCAAe,MAAK;gCAAe,SAAQ;0CACxD,cAAA,8OAAC;oCAAK,UAAS;oCAAU,GAAE;oCAAoH,UAAS;;;;;;;;;;;4BAEzJ;;;;;;;oBAIJ,yBACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;gCAAe,MAAK;gCAAe,SAAQ;0CACxD,cAAA,8OAAC;oCAAK,UAAS;oCAAU,GAAE;oCAAwI,UAAS;;;;;;;;;;;4BAE7K;;;;;;;kCAKL,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;4BAAO,cAAW;sCAC/B,cAAA,8OAAC;gCAAG,WAAU;0CACX,YAAY,GAAG,CAAC,CAAC,OAAO,sBACvB,8OAAC;wCAAe,WAAU;;4CACvB,QAAQ,mBACP,8OAAC;gDAAI,WAAU;gDAA6B,MAAK;gDAAe,SAAQ;0DACtE,cAAA,8OAAC;oDAAK,UAAS;oDAAU,GAAE;oDAAqH,UAAS;;;;;;;;;;;0DAG7J,8OAAC;gDACC,SAAS,IAAM,eAAe,MAAM,IAAI;gDACxC,WAAW,CAAC,sCAAsC,EAChD,UAAU,YAAY,MAAM,GAAG,IAC3B,8BACA,kCACJ;0DAED,MAAM,IAAI;;;;;;;uCAdN;;;;;;;;;;;;;;;;;;;;oBAuBhB,4BACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAiD;;;;;;8DAClE,8OAAC;oDACC,MAAK;oDACL,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,IAAI;oDACtD,WAAU;;;;;;;;;;;;sDAGd,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,SAAS;oDACT,UAAU,CAAC;oDACX,WAAU;8DACX;;;;;;8DAGD,8OAAC;oDACC,SAAS;wDACP,cAAc;wDACd,cAAc;oDAChB;oDACA,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAUV,+BACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CACtD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAiD;;;;;;8DAClE,8OAAC;oDACC,MAAK;oDACL,WAAU;oDACV,aAAY;oDACZ,OAAO;oDACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;oDAChD,YAAY,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW;;;;;;;;;;;;sDAG5C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,SAAS;oDACT,UAAU,CAAC,cAAc,IAAI;oDAC7B,WAAU;8DACX;;;;;;8DAGD,8OAAC;oDACC,SAAS;wDACP,iBAAiB;wDACjB,iBAAiB;oDACnB;oDACA,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAUV,0BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;+BAE7B,MAAM,MAAM,KAAK,kBACnB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,OAAM;oCAA6B,WAAU;oCAA0B,MAAK;oCAAO,SAAQ;oCAAY,QAAO;8CACjH,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAK,GAAE;;;;;;;;;;;;;;;;0CAG3E,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CACtD,8OAAC;gCAAE,WAAU;0CAA2B;;;;;;;;;;;6CAG1C,8OAAC;wBAAI,WAAU;kCACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;gCAEC,WAAU;gCACV,SAAS,IAAM,gBAAgB;0CAE/B,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;gDACZ,YAAY;8DACb,8OAAC;;sEACC,8OAAC;4DAAG,WAAU;sEAA0B,KAAK,IAAI;;;;;;sEACjD,8OAAC;4DAAE,WAAU;;gEACV,KAAK,IAAI,KAAK,SAAS,eAAe,KAAK,IAAI,IAAI;gEAAY;gEACtD,IAAI,KAAK,KAAK,QAAQ,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;wCAKzD,KAAK,IAAI,KAAK,wBACb,8OAAC;4CAAI,WAAU;4CAAa,SAAS,CAAC,IAAM,EAAE,eAAe;;8DAC3D,8OAAC;oDACC,SAAS,IAAM,aAAa;oDAC5B,WAAU;8DACX;;;;;;8DAGD,8OAAC;oDACC,SAAS,IAAM,WAAW;oDAC1B,WAAU;8DACX;;;;;;;;;;;;;;;;;;+BA3BF;;;;;;;;;;;;;;;;;;;;;;AAwCrB", "debugId": null}}, {"offset": {"line": 5641, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/BlocksConnect/web-panel-blocksconnect/src/components/ProtectedRoute.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useAuth } from '../contexts/AuthContext';\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode;\n}\n\nexport default function ProtectedRoute({ children }: ProtectedRouteProps) {\n  const { user, isLoading } = useAuth();\n  const router = useRouter();\n\n  useEffect(() => {\n    if (!isLoading && !user) {\n      router.push('/login');\n    }\n  }, [user, isLoading, router]);\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-center fade-in-fast\">\n          <div className=\"relative mb-6\">\n            <div className=\"animate-spin h-16 w-16 border-4 border-blue-500/30 border-t-blue-500 rounded-full mx-auto\"></div>\n            <div className=\"absolute inset-0 animate-ping h-16 w-16 border-4 border-blue-500/20 rounded-full mx-auto\"></div>\n          </div>\n          <h3 className=\"text-xl font-semibold text-white mb-2\">Loading</h3>\n          <p className=\"text-gray-300 font-light\">Please wait...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (!user) {\n    return null; // Will redirect to login\n  }\n\n  return <>{children}</>;\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAUe,SAAS,eAAe,EAAE,QAAQ,EAAuB;IACtE,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAClC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,aAAa,CAAC,MAAM;YACvB,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAM;QAAW;KAAO;IAE5B,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;;;;;;;kCAEjB,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,8OAAC;wBAAE,WAAU;kCAA2B;;;;;;;;;;;;;;;;;IAIhD;IAEA,IAAI,CAAC,MAAM;QACT,OAAO,MAAM,yBAAyB;IACxC;IAEA,qBAAO;kBAAG;;AACZ", "debugId": null}}, {"offset": {"line": 5735, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/BlocksConnect/web-panel-blocksconnect/src/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useRef, useEffect } from 'react';\nimport Link from 'next/link';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useRouter, usePathname } from 'next/navigation';\nimport { QuickThemeToggle } from './ThemeSettings';\n\ninterface NavigationItem {\n  name: string;\n  href: string;\n  icon: React.ReactNode;\n  description: string;\n}\n\nexport default function AdminHeader() {\n  const { user, logout } = useAuth();\n  const router = useRouter();\n  const pathname = usePathname();\n  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const wrapperRef = useRef<HTMLDivElement>(null);\n\n  const handleLogout = () => {\n    logout();\n    router.push('/login');\n    setIsUserMenuOpen(false);\n  };\n\n  // Close the menu when you click anywhere *outside* the wrapper\n  useEffect(() => {\n    function onDocClick(e: MouseEvent) {\n      if (wrapperRef.current && !wrapperRef.current.contains(e.target as Node)) {\n        setIsUserMenuOpen(false);\n      }\n    }\n    document.addEventListener('mousedown', onDocClick);\n    return () => document.removeEventListener('mousedown', onDocClick);\n  }, []);\n\n  const navigationItems: NavigationItem[] = [\n    {\n      name: 'Dashboard',\n      href: '/',\n      description: 'Overview of all services',\n      icon: (\n        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z\" />\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 5a2 2 0 012-2h4a2 2 0 012 2v6a2 2 0 01-2 2H10a2 2 0 01-2-2V5z\" />\n        </svg>\n      ),\n    },\n    {\n      name: 'Minecraft',\n      href: '/minecraft',\n      description: 'Server management',\n      icon: (\n        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01\" />\n        </svg>\n      ),\n    },\n\n  ];\n\n  const isActiveRoute = (href: string) => {\n    if (href === '/') {\n      return pathname === '/';\n    }\n    return pathname.startsWith(href);\n  };\n\n  const getUserInitials = () => {\n    if (user?.displayName) {\n      return user.displayName\n        .split(' ')\n        .map(name => name[0])\n        .join('')\n        .toUpperCase()\n        .slice(0, 2);\n    }\n    if (user?.email) {\n      return user.email[0].toUpperCase();\n    }\n    return 'U';\n  };\n\n  return (\n    <header className=\"relative border-b border-white/10\">\n      {/* Background Gradients */}\n      <div className=\"absolute inset-0 bg-gradient-to-r from-blue-600/95 to-purple-600/95 backdrop-blur-md z-0\"></div>\n      <div className=\"absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 z-0\"></div>\n\n      {/* Animated Background Elements */}\n      <div className=\"absolute top-0 left-1/4 w-32 h-32 bg-blue-400/10 rounded-full blur-3xl animate-pulse z-0\"></div>\n      <div className=\"absolute top-0 right-1/4 w-24 h-24 bg-purple-400/10 rounded-full blur-2xl animate-pulse delay-1000 z-0\"></div>\n\n      <div className=\"relative z-10 max-w-7xl mx-auto\">\n        <div className=\"flex justify-between items-center px-4 sm:px-6 lg:px-8 py-4\">\n\n          {/* Logo and Brand */}\n          <div className=\"flex items-center fade-in-up\">\n            <Link href=\"/\" className=\"flex items-center group\">\n              <div className=\"w-12 h-12 rounded-xl flex items-center justify-center mr-4 bg-gradient-to-br from-blue-400/30 to-purple-400/30 backdrop-blur-sm border border-white/20 group-hover:scale-110 transition-all duration-300 group-hover:shadow-lg group-hover:shadow-blue-500/25\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-7 w-7 text-blue-200 group-hover:text-white transition-colors duration-300\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                  <path d=\"M3 3h18v18H3V3zm2 2v14h14V5H5zm2 2h10v10H7V7zm2 2v6h6V9H9z\"/>\n                  <path d=\"M10 10h1v1h-1v-1zm2 0h1v1h-1v-1zm-2 2h1v1h-1v-1zm2 0h1v1h-1v-1z\"/>\n                </svg>\n              </div>\n              <div>\n                <h1 className=\"text-2xl font-bold text-white group-hover:text-blue-200 transition-colors duration-300\">\n                  BlocksConnect\n                </h1>\n                <p className=\"text-sm text-blue-200/80 font-medium\">Admin Panel</p>\n              </div>\n            </Link>\n          </div>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden md:flex items-center space-x-1 fade-in-up delay-100\">\n            {navigationItems.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className={`relative px-4 py-2 rounded-lg font-medium transition-all duration-300 group ${\n                  isActiveRoute(item.href)\n                    ? 'text-white bg-white/20 shadow-lg'\n                    : 'text-blue-100 hover:text-white hover:bg-white/10'\n                }`}\n              >\n                <div className=\"flex items-center space-x-2\">\n                  {item.icon}\n                  <span>{item.name}</span>\n                </div>\n                {isActiveRoute(item.href) && (\n                  <div className=\"absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-blue-300 rounded-full\"></div>\n                )}\n              </Link>\n            ))}\n          </nav>\n\n          {/* User Menu and Mobile Menu Button */}\n          <div className=\"flex items-center space-x-4 fade-in-up delay-200\">\n            {/* Theme Toggle */}\n            <QuickThemeToggle />\n\n            {/* Mobile Menu Button */}\n            <button\n              onClick={(e) => {\n                e.stopPropagation();\n                setIsMobileMenuOpen(!isMobileMenuOpen);\n              }}\n              className=\"md:hidden p-2 rounded-lg text-white hover:bg-white/10 transition-colors duration-200\"\n            >\n              <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n              </svg>\n            </button>\n\n            {/* User Menu */}\n            <div className=\"relative\" ref={wrapperRef}>\n              <button\n                onClick={(e) => {\n                  e.stopPropagation();\n                  setIsUserMenuOpen(!isUserMenuOpen);\n                }}\n                className=\"flex items-center space-x-3 p-2 rounded-lg hover:bg-white/10 transition-all duration-200 group\"\n              >\n                <div className=\"w-10 h-10 rounded-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center text-white font-semibold text-sm shadow-lg group-hover:scale-105 transition-transform duration-200\">\n                  {getUserInitials()}\n                </div>\n                <div className=\"hidden sm:block text-left\">\n                  <p className=\"text-white font-medium text-sm\">\n                    {user?.displayName || 'User'}\n                  </p>\n                  <p className=\"text-blue-200/80 text-xs\">\n                    {user?.email}\n                  </p>\n                </div>\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className={`h-4 w-4 text-blue-200 transition-transform duration-200 ${isUserMenuOpen ? 'rotate-180' : ''}`} fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n                </svg>\n              </button>\n\n              {/* User Dropdown Menu */}\n              {isUserMenuOpen && (\n                <div\n                  onClick={(e) => e.stopPropagation()}\n                  className=\"absolute right-0 mt-2 w-64 bg-gray-900/95 backdrop-blur-md rounded-xl shadow-2xl border border-white/10 py-2 z-[100] fade-in-up\"\n                >\n                  <div className=\"px-4 py-3 border-b border-white/10\">\n                    <p className=\"text-white font-semibold\">{user?.displayName || 'User'}</p>\n                    <p className=\"text-gray-300 text-sm\">{user?.email}</p>\n                    <p className=\"text-gray-400 text-xs mt-1\">Administrator</p>\n                  </div>\n\n                  <div className=\"py-2\">\n                    <Link\n                      href=\"/account\"\n                      onClick={() => setIsUserMenuOpen(false)}\n                      className=\"flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-white/10 transition-colors duration-200\"\n                    >\n                      <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n                      </svg>\n                      Account Settings\n                    </Link>\n\n                    <button\n                      onClick={handleLogout}\n                      className=\"flex items-center w-full px-4 py-2 text-gray-300 hover:text-red-400 hover:bg-red-500/10 transition-colors duration-200\"\n                    >\n                      <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\n                      </svg>\n                      Sign Out\n                    </button>\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n\n        {/* Mobile Navigation Menu */}\n        {isMobileMenuOpen && (\n          <div className=\"md:hidden border-t border-white/10 bg-black/20 backdrop-blur-sm\">\n            <div className=\"px-4 py-4 space-y-2\">\n              {navigationItems.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  onClick={() => setIsMobileMenuOpen(false)}\n                  className={`flex items-center space-x-3 px-4 py-3 rounded-lg transition-all duration-200 ${\n                    isActiveRoute(item.href)\n                      ? 'text-white bg-white/20'\n                      : 'text-blue-100 hover:text-white hover:bg-white/10'\n                  }`}\n                >\n                  {item.icon}\n                  <div>\n                    <p className=\"font-medium\">{item.name}</p>\n                    <p className=\"text-xs text-blue-200/70\">{item.description}</p>\n                  </div>\n                </Link>\n              ))}\n            </div>\n          </div>\n        )}\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAee,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE1C,MAAM,eAAe;QACnB;QACA,OAAO,IAAI,CAAC;QACZ,kBAAkB;IACpB;IAEA,+DAA+D;IAC/D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS,WAAW,CAAa;YAC/B,IAAI,WAAW,OAAO,IAAI,CAAC,WAAW,OAAO,CAAC,QAAQ,CAAC,EAAE,MAAM,GAAW;gBACxE,kBAAkB;YACpB;QACF;QACA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;IACzD,GAAG,EAAE;IAEL,MAAM,kBAAoC;QACxC;YACE,MAAM;YACN,MAAM;YACN,aAAa;YACb,oBACE,8OAAC;gBAAI,OAAM;gBAA6B,WAAU;gBAAU,MAAK;gBAAO,SAAQ;gBAAY,QAAO;;kCACjG,8OAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;kCACrE,8OAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;;QAG3E;QACA;YACE,MAAM;YACN,MAAM;YACN,aAAa;YACb,oBACE,8OAAC;gBAAI,OAAM;gBAA6B,WAAU;gBAAU,MAAK;gBAAO,SAAQ;gBAAY,QAAO;0BACjG,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;QAG3E;KAED;IAED,MAAM,gBAAgB,CAAC;QACrB,IAAI,SAAS,KAAK;YAChB,OAAO,aAAa;QACtB;QACA,OAAO,SAAS,UAAU,CAAC;IAC7B;IAEA,MAAM,kBAAkB;QACtB,IAAI,MAAM,aAAa;YACrB,OAAO,KAAK,WAAW,CACpB,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,IAAI,CAAC,EAAE,EACnB,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;QACd;QACA,IAAI,MAAM,OAAO;YACf,OAAO,KAAK,KAAK,CAAC,EAAE,CAAC,WAAW;QAClC;QACA,OAAO;IACT;IAEA,qBACE,8OAAC;QAAO,WAAU;;0BAEhB,8OAAC;gBAAI,WAAU;;;;;;0BACf,8OAAC;gBAAI,WAAU;;;;;;0BAGf,8OAAC;gBAAI,WAAU;;;;;;0BACf,8OAAC;gBAAI,WAAU;;;;;;0BAEf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CAGb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;;sDACvB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,OAAM;gDAA6B,WAAU;gDAA8E,SAAQ;gDAAY,MAAK;;kEACvJ,8OAAC;wDAAK,GAAE;;;;;;kEACR,8OAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;;sDAGZ,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAyF;;;;;;8DAGvG,8OAAC;oDAAE,WAAU;8DAAuC;;;;;;;;;;;;;;;;;;;;;;;0CAM1D,8OAAC;gCAAI,WAAU;0CACZ,gBAAgB,GAAG,CAAC,CAAC,qBACpB,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAW,CAAC,4EAA4E,EACtF,cAAc,KAAK,IAAI,IACnB,qCACA,oDACJ;;0DAEF,8OAAC;gDAAI,WAAU;;oDACZ,KAAK,IAAI;kEACV,8OAAC;kEAAM,KAAK,IAAI;;;;;;;;;;;;4CAEjB,cAAc,KAAK,IAAI,mBACtB,8OAAC;gDAAI,WAAU;;;;;;;uCAbZ,KAAK,IAAI;;;;;;;;;;0CAoBpB,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC,mIAAA,CAAA,mBAAgB;;;;;kDAGjB,8OAAC;wCACC,SAAS,CAAC;4CACR,EAAE,eAAe;4CACjB,oBAAoB,CAAC;wCACvB;wCACA,WAAU;kDAEV,cAAA,8OAAC;4CAAI,OAAM;4CAA6B,WAAU;4CAAU,MAAK;4CAAO,SAAQ;4CAAY,QAAO;sDACjG,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;kDAKzE,8OAAC;wCAAI,WAAU;wCAAW,KAAK;;0DAC7B,8OAAC;gDACC,SAAS,CAAC;oDACR,EAAE,eAAe;oDACjB,kBAAkB,CAAC;gDACrB;gDACA,WAAU;;kEAEV,8OAAC;wDAAI,WAAU;kEACZ;;;;;;kEAEH,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAU;0EACV,MAAM,eAAe;;;;;;0EAExB,8OAAC;gEAAE,WAAU;0EACV,MAAM;;;;;;;;;;;;kEAGX,8OAAC;wDAAI,OAAM;wDAA6B,WAAW,CAAC,wDAAwD,EAAE,iBAAiB,eAAe,IAAI;wDAAE,MAAK;wDAAO,SAAQ;wDAAY,QAAO;kEACzL,cAAA,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;;;;;;4CAKxE,gCACC,8OAAC;gDACC,SAAS,CAAC,IAAM,EAAE,eAAe;gDACjC,WAAU;;kEAEV,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAU;0EAA4B,MAAM,eAAe;;;;;;0EAC9D,8OAAC;gEAAE,WAAU;0EAAyB,MAAM;;;;;;0EAC5C,8OAAC;gEAAE,WAAU;0EAA6B;;;;;;;;;;;;kEAG5C,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,4JAAA,CAAA,UAAI;gEACH,MAAK;gEACL,SAAS,IAAM,kBAAkB;gEACjC,WAAU;;kFAEV,8OAAC;wEAAI,OAAM;wEAA6B,WAAU;wEAAe,MAAK;wEAAO,SAAQ;wEAAY,QAAO;kFACtG,cAAA,8OAAC;4EAAK,eAAc;4EAAQ,gBAAe;4EAAQ,aAAa;4EAAG,GAAE;;;;;;;;;;;oEACjE;;;;;;;0EAIR,8OAAC;gEACC,SAAS;gEACT,WAAU;;kFAEV,8OAAC;wEAAI,OAAM;wEAA6B,WAAU;wEAAe,MAAK;wEAAO,SAAQ;wEAAY,QAAO;kFACtG,cAAA,8OAAC;4EAAK,eAAc;4EAAQ,gBAAe;4EAAQ,aAAa;4EAAG,GAAE;;;;;;;;;;;oEACjE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAWnB,kCACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACZ,gBAAgB,GAAG,CAAC,CAAC,qBACpB,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,SAAS,IAAM,oBAAoB;oCACnC,WAAW,CAAC,6EAA6E,EACvF,cAAc,KAAK,IAAI,IACnB,2BACA,oDACJ;;wCAED,KAAK,IAAI;sDACV,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAe,KAAK,IAAI;;;;;;8DACrC,8OAAC;oDAAE,WAAU;8DAA4B,KAAK,WAAW;;;;;;;;;;;;;mCAZtD,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsBhC", "debugId": null}}, {"offset": {"line": 6321, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/BlocksConnect/web-panel-blocksconnect/src/app/minecraft/server/%5Bid%5D/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, use } from 'react';\nimport ConfirmModal from '../../../../components/ConfirmModal';\nimport { useParams, useRouter } from 'next/navigation';\nimport { getServerDetails, startServer, stopServer, deleteServer, toggleBackup, downloadBackup, Server } from '../../../../services/api';\nimport { useAuth } from '../../../../contexts/AuthContext';\nimport ServerConsole from '../../../../components/ServerConsole';\nimport ServerPropertiesEditor from '../../../../components/ServerPropertiesEditor';\nimport BackupManager from '../../../../components/BackupManager';\nimport PlayerManager from '../../../../components/PlayerManager';\nimport ServerMonitoring from '../../../../components/ServerMonitoring';\nimport FileManager from '../../../../components/FileManager';\nimport ProtectedRoute from '../../../../components/ProtectedRoute';\nimport AdminHeader from '../../../../components/Header';\n\n\nexport default function ServerDetailsPage() {\n  const params = useParams();\n  const router = useRouter();\n  const serverId = params.id as string;\n  const { user, isLoading: authLoading, getToken } = useAuth();\n\n  const [server, setServer] = useState<Server | null>(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [deleteModalOpen, setDeleteModalOpen] = useState(false);\n  const [isDeleting, setIsDeleting] = useState(false);\n  const [activeTab, setActiveTab] = useState<'overview' | 'properties' | 'players' | 'backups' | 'files' | 'monitoring' | 'console'>('overview');\n\n  const fetchServerDetails = async () => {\n    // Don't fetch if auth is still loading or user is not authenticated\n    if (authLoading || !user) {\n      return;\n    }\n\n    try {\n      setIsLoading(true);\n      const token = await getToken()\n      const data = await getServerDetails(serverId, token);\n      setServer(data);\n      setError(null);\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'An unknown error occurred');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    if (serverId) {\n      fetchServerDetails();\n    }\n  }, [serverId, user, authLoading]);\n\n\n  const handleStartServer = async () => {\n    if (!server) return;\n\n    try {\n      const token = await getToken();\n      await startServer(server.id, token);\n      setServer({ ...server, status: 'running' });\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'An unknown error occurred');\n    }\n  };\n\n  const handleStopServer = async () => {\n    if (!server) return;\n\n    try {\n      const token = await getToken();\n      await stopServer(server.id, token);\n      setServer({ ...server, status: 'stopped' });\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'An unknown error occurred');\n    }\n  };\n\n  const handleDeleteServer = () => {\n    setDeleteModalOpen(true);\n  };\n\n  const confirmDeleteServer = async () => {\n    if (!server) return;\n    setIsDeleting(true);\n    try {\n      const token = await getToken();\n      await deleteServer(server.id, token);\n      router.push('/minecraft/dashboard');\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'An unknown error occurred');\n    } finally {\n      setIsDeleting(false);\n      setDeleteModalOpen(false);\n      router.push('/minecraft/dashboard');\n    }\n  };\n\n  const handleToggleBackup = async () => {\n    if (!server) return;\n\n    try {\n      const token = await getToken();\n      await toggleBackup(server.id, token);\n      // Refresh server details\n      const updatedServer = await getServerDetails(serverId, token);\n      setServer(updatedServer);\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'An unknown error occurred');\n    }\n  };\n\n  const handleDownloadBackup = async () => {\n    if (!server) return;\n\n    try {\n      const token = await getToken();\n      await downloadBackup(server.id, token);\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'An unknown error occurred');\n    }\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'running':\n        return 'text-green-400 bg-green-500/20 border-green-500/30';\n      case 'stopped':\n        return 'text-red-400 bg-red-500/20 border-red-500/30';\n      case 'starting':\n        return 'text-yellow-400 bg-yellow-500/20 border-yellow-500/30';\n      default:\n        return 'text-gray-400 bg-gray-500/20 border-gray-500/30';\n    }\n  };\n\n  if (authLoading || isLoading) {\n    return (\n      <ProtectedRoute>\n        <div className=\"min-h-screen text-white\">\n          <AdminHeader />\n          <div className=\"flex items-center justify-center py-20\">\n            <div className=\"text-center fade-in-fast\">\n              <div className=\"relative mb-6\">\n                <div className=\"animate-spin h-16 w-16 border-4 border-blue-500/30 border-t-blue-500 rounded-full mx-auto\"></div>\n                <div className=\"absolute inset-0 animate-ping h-16 w-16 border-4 border-blue-500/20 rounded-full mx-auto\"></div>\n              </div>\n              <h3 className=\"text-xl font-semibold text-white mb-2\">\n                {authLoading ? 'Authenticating...' : 'Loading Server Details'}\n              </h3>\n              <p className=\"text-gray-300 font-light\">\n                {authLoading ? 'Please wait while we verify your authentication...' : 'Please wait while we fetch the server information...'}\n              </p>\n            </div>\n          </div>\n        </div>\n      </ProtectedRoute>\n    );\n  }\n\n  if (error || !server) {\n    return (\n      <ProtectedRoute>\n        <div className=\"min-h-screen text-white\">\n          <AdminHeader />\n          <div className=\"container mx-auto py-12 px-4\">\n            <div className=\"card p-8 text-center fade-in-up\">\n              <div className=\"w-20 h-20 mx-auto mb-6 rounded-full bg-gradient-to-br from-red-500/20 to-red-600/20 flex items-center justify-center\">\n                <svg className=\"h-10 w-10 text-red-400\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n                </svg>\n              </div>\n              <h3 className=\"text-2xl font-bold text-white mb-4\">Server Not Found</h3>\n              <p className=\"text-red-400 mb-8\">{error || 'The requested server could not be found.'}</p>\n              <button\n                onClick={() => router.push('/minecraft/dashboard')}\n                className=\"btn-primary px-8 py-3 font-semibold\"\n              >\n                <svg className=\"h-5 w-5 mr-2 inline\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M10 19l-7-7m0 0l7-7m-7 7h18\" />\n                </svg>\n                Back to Dashboard\n              </button>\n            </div>\n          </div>\n        </div>\n      </ProtectedRoute>\n    );\n  }\n\n  return (\n    <ProtectedRoute>\n      <div className=\"min-h-screen text-white\">\n        <AdminHeader />\n\n        <main className=\"container mx-auto py-12 px-4 max-w-7xl\">\n          <div className=\"mb-8 fade-in-up\">\n            <button\n              onClick={() => router.push('/minecraft/dashboard')}\n              className=\"text-blue-400 hover:text-blue-300 mb-4 inline-flex items-center font-medium transition-colors\"\n            >\n              <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 mr-2\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                <path fillRule=\"evenodd\" d=\"M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z\" clipRule=\"evenodd\" />\n              </svg>\n              Back to Dashboard\n            </button>\n          </div>\n\n          {/* Server Info Card */}\n          <div className=\"card mb-8 fade-in-fast\">\n            <div className=\"px-8 py-6 border-b border-white/10 bg-gradient-to-r from-blue-500/10 to-purple-500/10\">\n              <div className=\"flex flex-col lg:flex-row lg:items-center justify-between gap-6\">\n                <div>\n                  <h1 className=\"text-4xl font-bold flex flex-col lg:flex-row lg:items-center gap-4\">\n                    {server.name}\n                   <span className={`px-4 py-2 rounded-full text-sm font-semibold border backdrop-blur-sm ${getStatusColor(server.status)} w-fit !text-white`}>\n                    {server.status.charAt(0).toUpperCase() + server.status.slice(1)}\n                  </span>\n                  </h1>\n                </div>\n                <div className=\"flex flex-wrap gap-3\">\n                  {server.status === 'stopped' ? (\n                    <button\n                      onClick={handleStartServer}\n                      className=\"bg-green-500/20 hover:bg-green-500/30 border border-green-500/30 hover:border-green-500/50 text-green-400 hover:text-green-300 px-6 py-3 rounded-lg font-semibold transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-green-500/20\"\n                    >\n                      <svg className=\"h-5 w-5 mr-2 inline\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z\" clipRule=\"evenodd\" />\n                      </svg>\n                      Start Server\n                    </button>\n                  ) : (\n                    <button\n                      onClick={handleStopServer}\n                      className=\"bg-red-500/20 hover:bg-red-500/30 border border-red-500/30 hover:border-red-500/50 text-red-400 hover:text-red-300 px-6 py-3 rounded-lg font-semibold transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-red-500/20\"\n                    >\n                      <svg className=\"h-5 w-5 mr-2 inline\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 00-1 1v4a1 1 0 001 1h4a1 1 0 001-1V8a1 1 0 00-1-1H8z\" clipRule=\"evenodd\" />\n                      </svg>\n                      Stop Server\n                    </button>\n                  )}\n                  <button\n                    onClick={handleDeleteServer}\n                    className=\"bg-red-500/20 hover:bg-red-500/30 border border-red-500/30 hover:border-red-500/50 text-red-400 hover:text-red-300 px-6 py-3 rounded-lg font-semibold transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-red-500/20\"\n                    disabled={isDeleting}\n                  >\n                    <svg className=\"h-5 w-5 mr-2 inline\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1-1H8a1 1 0 00-1 1v3M4 7h16\" />\n                    </svg>\n                    {isDeleting ? 'Deleting...' : 'Delete Server'}\n                  </button>\n      <ConfirmModal\n        open={deleteModalOpen}\n        title=\"Delete Server\"\n        message={server ? `Are you sure you want to delete the server \\\"${server.name}\\\"? This action cannot be undone.` : ''}\n        confirmText={isDeleting ? 'Deleting...' : 'Delete'}\n        cancelText=\"Cancel\"\n        onConfirm={confirmDeleteServer}\n        onCancel={() => setDeleteModalOpen(false)}\n      />\n                </div>\n\n              </div>\n              <div className=\"flex items-center gap-2\">\n                  Your server ip: <span className=\"text-white font-semibold\">play.blocksconnect.com:{server.port}</span>\n                  <button\n                    onClick={() => navigator.clipboard.writeText(`play.blocksconnect.com:${server.port}`)}\n                    className=\"text-blue-400 hover:text-blue-300 cursor-pointer transition-colors\"\n                  >\n                    Copy\n                  </button>\n                </div>\n            </div>\n\n            <div className=\"p-6\">\n              <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n                <div className=\"bg-blue-500/10 rounded-lg p-3 border border-blue-500/20\">\n                  <h3 className=\"text-blue-300 font-semibold mb-1\">Port</h3>\n                  <p className=\"text-xl font-bold text-white\">{server.port}</p>\n                </div>\n                <div className=\"bg-purple-500/10 rounded-lg p-3 border border-purple-500/20\">\n                  <h3 className=\"text-purple-300 font-semibold mb-1\">Version</h3>\n                  <p className=\"text-xl font-bold text-white\">{server.version}</p>\n                </div>\n                <div className=\"bg-indigo-500/10 rounded-lg p-3 border border-indigo-500/20\">\n                  <h3 className=\"text-indigo-300 font-semibold mb-1\">Memory</h3>\n                  <p className=\"text-xl font-bold text-white\">{server.memory}</p>\n                </div>\n                <div className=\"bg-emerald-500/10 rounded-lg p-3 border border-emerald-500/20\">\n                  <h3 className=\"text-emerald-300 font-semibold mb-1\">Backup</h3>\n                  <div className=\"flex flex-col space-y-2\">\n                    <p className=\"text-xl font-bold text-white\">{server.backup ? 'Enabled' : 'Disabled'}</p>\n                    <div className=\"flex flex-wrap gap-2\">\n                      <button\n                        onClick={handleToggleBackup}\n                        className={`px-3 py-1.5 rounded-lg text-sm font-semibold transition-all duration-300 hover:scale-105 ${\n                          server.backup\n                            ? 'bg-blue-500/20 hover:bg-blue-500/30 border border-blue-500/30 hover:border-blue-500/50 text-blue-400 hover:text-blue-300 hover:shadow-lg hover:shadow-blue-500/20'\n                            : 'bg-gray-500/20 hover:bg-gray-500/30 border border-gray-500/30 hover:border-gray-500/50 text-gray-400 hover:text-gray-300 hover:shadow-lg hover:shadow-gray-500/20'\n                        }`}\n                      >\n                        Toggle\n                      </button>\n                      {server.backup && (\n                        <button\n                          onClick={handleDownloadBackup}\n                          className=\"bg-purple-500/20 hover:bg-purple-500/30 border border-purple-500/30 hover:border-purple-500/50 text-purple-400 hover:text-purple-300 px-3 py-1.5 rounded-lg text-sm font-semibold transition-all duration-300 hover:scale-105 hover:shadow-lg hover:shadow-purple-500/20\"\n                        >\n                          Download\n                        </button>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Tab Navigation */}\n          <div className=\"mb-8\">\n            <div className=\"border-b border-white/10\">\n              <nav className=\"flex space-x-0 overflow-x-auto\">\n                {[\n                  { id: 'overview', label: 'Overview', icon: '📊' },\n                  { id: 'properties', label: 'Properties', icon: '⚙️' },\n                  { id: 'players', label: 'Players', icon: '👥' },\n                  { id: 'backups', label: 'Backups', icon: '💾' },\n                  { id: 'files', label: 'Files', icon: '📁' },\n                  { id: 'monitoring', label: 'Monitoring', icon: '📈' },\n                  { id: 'console', label: 'Console', icon: '💻' }\n                ].map((tab) => (\n                  <button\n                    key={tab.id}\n                    onClick={() => setActiveTab(tab.id as any)}\n                    className={`flex items-center px-4 lg:px-6 py-4 text-sm font-medium border-b-2 transition-colors whitespace-nowrap ${\n                      activeTab === tab.id\n                        ? 'border-blue-500 text-blue-400 bg-blue-500/10'\n                        : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'\n                    }`}\n                  >\n                    <span className=\"mr-2\">{tab.icon}</span>\n                    {tab.label}\n                  </button>\n                ))}\n              </nav>\n            </div>\n          </div>\n\n          {/* Tab Content */}\n          <div className=\"fade-in-fast\">\n            {activeTab === 'overview' && (\n              <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n                <ServerMonitoring server={server} />\n                <div className=\"space-y-8\">\n                  <BackupManager\n                    server={server}\n                    onBackupUpdated={() => fetchServerDetails()}\n                  />\n                </div>\n              </div>\n            )}\n\n            {activeTab === 'properties' && (\n              <ServerPropertiesEditor\n                server={server}\n                onPropertiesUpdated={() => fetchServerDetails()}\n              />\n            )}\n\n            {activeTab === 'players' && (\n              <PlayerManager\n                server={server}\n                onPlayerUpdated={() => fetchServerDetails()}\n              />\n            )}\n\n            {activeTab === 'backups' && (\n              <BackupManager\n                server={server}\n                onBackupUpdated={() => fetchServerDetails()}\n              />\n            )}\n\n            {activeTab === 'files' && (\n              <FileManager server={server} />\n            )}\n\n            {activeTab === 'monitoring' && (\n              <ServerMonitoring server={server} />\n            )}\n\n            {activeTab === 'console' && (\n              <ServerConsole serverId={serverId} isRunning={server.status === 'running'} />\n            )}\n          </div>\n        </main>\n\n        <footer className=\"relative mt-20\">\n          <div className=\"absolute inset-0 bg-gradient-to-r from-blue-900/20 to-purple-900/20 backdrop-blur-sm\"></div>\n          <div className=\"relative z-10 container mx-auto text-center py-8 px-4\">\n            <div className=\"border-t border-white/10 pt-8\">\n              <p className=\"text-sm text-gray-300 font-medium\">\n                &copy; {new Date().getFullYear()} BlocksConnect\n              </p>\n            </div>\n          </div>\n        </footer>\n      </div>\n    </ProtectedRoute>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAdA;;;;;;;;;;;;;;;AAiBe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,OAAO,EAAE;IAC1B,MAAM,EAAE,IAAI,EAAE,WAAW,WAAW,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEzD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACpD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0F;IAEnI,MAAM,qBAAqB;QACzB,oEAAoE;QACpE,IAAI,eAAe,CAAC,MAAM;YACxB;QACF;QAEA,IAAI;YACF,aAAa;YACb,MAAM,QAAQ,MAAM;YACpB,MAAM,OAAO,MAAM,CAAA,GAAA,sHAAA,CAAA,mBAAgB,AAAD,EAAE,UAAU;YAC9C,UAAU;YACV,SAAS;QACX,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,aAAa;QACf;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU;YACZ;QACF;IACF,GAAG;QAAC;QAAU;QAAM;KAAY;IAGhC,MAAM,oBAAoB;QACxB,IAAI,CAAC,QAAQ;QAEb,IAAI;YACF,MAAM,QAAQ,MAAM;YACpB,MAAM,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE,OAAO,EAAE,EAAE;YAC7B,UAAU;gBAAE,GAAG,MAAM;gBAAE,QAAQ;YAAU;QAC3C,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD;IACF;IAEA,MAAM,mBAAmB;QACvB,IAAI,CAAC,QAAQ;QAEb,IAAI;YACF,MAAM,QAAQ,MAAM;YACpB,MAAM,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,OAAO,EAAE,EAAE;YAC5B,UAAU;gBAAE,GAAG,MAAM;gBAAE,QAAQ;YAAU;QAC3C,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD;IACF;IAEA,MAAM,qBAAqB;QACzB,mBAAmB;IACrB;IAEA,MAAM,sBAAsB;QAC1B,IAAI,CAAC,QAAQ;QACb,cAAc;QACd,IAAI;YACF,MAAM,QAAQ,MAAM;YACpB,MAAM,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,OAAO,EAAE,EAAE;YAC9B,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,cAAc;YACd,mBAAmB;YACnB,OAAO,IAAI,CAAC;QACd;IACF;IAEA,MAAM,qBAAqB;QACzB,IAAI,CAAC,QAAQ;QAEb,IAAI;YACF,MAAM,QAAQ,MAAM;YACpB,MAAM,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,OAAO,EAAE,EAAE;YAC9B,yBAAyB;YACzB,MAAM,gBAAgB,MAAM,CAAA,GAAA,sHAAA,CAAA,mBAAgB,AAAD,EAAE,UAAU;YACvD,UAAU;QACZ,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD;IACF;IAEA,MAAM,uBAAuB;QAC3B,IAAI,CAAC,QAAQ;QAEb,IAAI;YACF,MAAM,QAAQ,MAAM;YACpB,MAAM,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,EAAE,EAAE;QAClC,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,IAAI,eAAe,WAAW;QAC5B,qBACE,8OAAC,oIAAA,CAAA,UAAc;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,4HAAA,CAAA,UAAW;;;;;kCACZ,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;;;;;;;8CAEjB,8OAAC;oCAAG,WAAU;8CACX,cAAc,sBAAsB;;;;;;8CAEvC,8OAAC;oCAAE,WAAU;8CACV,cAAc,uDAAuD;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAOpF;IAEA,IAAI,SAAS,CAAC,QAAQ;QACpB,qBACE,8OAAC,oIAAA,CAAA,UAAc;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,4HAAA,CAAA,UAAW;;;;;kCACZ,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;wCAAyB,MAAK;wCAAe,SAAQ;kDAClE,cAAA,8OAAC;4CAAK,UAAS;4CAAU,GAAE;4CAAoH,UAAS;;;;;;;;;;;;;;;;8CAG5J,8OAAC;oCAAG,WAAU;8CAAqC;;;;;;8CACnD,8OAAC;oCAAE,WAAU;8CAAqB,SAAS;;;;;;8CAC3C,8OAAC;oCACC,SAAS,IAAM,OAAO,IAAI,CAAC;oCAC3B,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;4CAAsB,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDAC7E,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;wCACjE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAQpB;IAEA,qBACE,8OAAC,oIAAA,CAAA,UAAc;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,4HAAA,CAAA,UAAW;;;;;8BAEZ,8OAAC;oBAAK,WAAU;;sCACd,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,SAAS,IAAM,OAAO,IAAI,CAAC;gCAC3B,WAAU;;kDAEV,8OAAC;wCAAI,OAAM;wCAA6B,WAAU;wCAAe,SAAQ;wCAAY,MAAK;kDACxF,cAAA,8OAAC;4CAAK,UAAS;4CAAU,GAAE;4CAAwI,UAAS;;;;;;;;;;;oCACxK;;;;;;;;;;;;sCAMV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;8DACC,cAAA,8OAAC;wDAAG,WAAU;;4DACX,OAAO,IAAI;0EACb,8OAAC;gEAAK,WAAW,CAAC,qEAAqE,EAAE,eAAe,OAAO,MAAM,EAAE,kBAAkB,CAAC;0EACxI,OAAO,MAAM,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,OAAO,MAAM,CAAC,KAAK,CAAC;;;;;;;;;;;;;;;;;8DAIjE,8OAAC;oDAAI,WAAU;;wDACZ,OAAO,MAAM,KAAK,0BACjB,8OAAC;4DACC,SAAS;4DACT,WAAU;;8EAEV,8OAAC;oEAAI,WAAU;oEAAsB,MAAK;oEAAe,SAAQ;8EAC/D,cAAA,8OAAC;wEAAK,UAAS;wEAAU,GAAE;wEAA0G,UAAS;;;;;;;;;;;gEAC1I;;;;;;iFAIR,8OAAC;4DACC,SAAS;4DACT,WAAU;;8EAEV,8OAAC;oEAAI,WAAU;oEAAsB,MAAK;oEAAe,SAAQ;8EAC/D,cAAA,8OAAC;wEAAK,UAAS;wEAAU,GAAE;wEAAmG,UAAS;;;;;;;;;;;gEACnI;;;;;;;sEAIV,8OAAC;4DACC,SAAS;4DACT,WAAU;4DACV,UAAU;;8EAEV,8OAAC;oEAAI,WAAU;oEAAsB,MAAK;oEAAO,QAAO;oEAAe,SAAQ;8EAC7E,cAAA,8OAAC;wEAAK,eAAc;wEAAQ,gBAAe;wEAAQ,aAAa;wEAAG,GAAE;;;;;;;;;;;gEAEtE,aAAa,gBAAgB;;;;;;;sEAE5C,8OAAC,kIAAA,CAAA,UAAY;4DACX,MAAM;4DACN,OAAM;4DACN,SAAS,SAAS,CAAC,6CAA6C,EAAE,OAAO,IAAI,CAAC,iCAAiC,CAAC,GAAG;4DACnH,aAAa,aAAa,gBAAgB;4DAC1C,YAAW;4DACX,WAAW;4DACX,UAAU,IAAM,mBAAmB;;;;;;;;;;;;;;;;;;sDAK7B,8OAAC;4CAAI,WAAU;;gDAA0B;8DACrB,8OAAC;oDAAK,WAAU;;wDAA2B;wDAAwB,OAAO,IAAI;;;;;;;8DAC9F,8OAAC;oDACC,SAAS,IAAM,UAAU,SAAS,CAAC,SAAS,CAAC,CAAC,uBAAuB,EAAE,OAAO,IAAI,EAAE;oDACpF,WAAU;8DACX;;;;;;;;;;;;;;;;;;8CAMP,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAmC;;;;;;kEACjD,8OAAC;wDAAE,WAAU;kEAAgC,OAAO,IAAI;;;;;;;;;;;;0DAE1D,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAqC;;;;;;kEACnD,8OAAC;wDAAE,WAAU;kEAAgC,OAAO,OAAO;;;;;;;;;;;;0DAE7D,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAqC;;;;;;kEACnD,8OAAC;wDAAE,WAAU;kEAAgC,OAAO,MAAM;;;;;;;;;;;;0DAE5D,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAsC;;;;;;kEACpD,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAU;0EAAgC,OAAO,MAAM,GAAG,YAAY;;;;;;0EACzE,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEACC,SAAS;wEACT,WAAW,CAAC,yFAAyF,EACnG,OAAO,MAAM,GACT,sKACA,qKACJ;kFACH;;;;;;oEAGA,OAAO,MAAM,kBACZ,8OAAC;wEACC,SAAS;wEACT,WAAU;kFACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAYf,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CACZ;wCACC;4CAAE,IAAI;4CAAY,OAAO;4CAAY,MAAM;wCAAK;wCAChD;4CAAE,IAAI;4CAAc,OAAO;4CAAc,MAAM;wCAAK;wCACpD;4CAAE,IAAI;4CAAW,OAAO;4CAAW,MAAM;wCAAK;wCAC9C;4CAAE,IAAI;4CAAW,OAAO;4CAAW,MAAM;wCAAK;wCAC9C;4CAAE,IAAI;4CAAS,OAAO;4CAAS,MAAM;wCAAK;wCAC1C;4CAAE,IAAI;4CAAc,OAAO;4CAAc,MAAM;wCAAK;wCACpD;4CAAE,IAAI;4CAAW,OAAO;4CAAW,MAAM;wCAAK;qCAC/C,CAAC,GAAG,CAAC,CAAC,oBACL,8OAAC;4CAEC,SAAS,IAAM,aAAa,IAAI,EAAE;4CAClC,WAAW,CAAC,uGAAuG,EACjH,cAAc,IAAI,EAAE,GAChB,iDACA,8EACJ;;8DAEF,8OAAC;oDAAK,WAAU;8DAAQ,IAAI,IAAI;;;;;;gDAC/B,IAAI,KAAK;;2CATL,IAAI,EAAE;;;;;;;;;;;;;;;;;;;;sCAiBrB,8OAAC;4BAAI,WAAU;;gCACZ,cAAc,4BACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,sIAAA,CAAA,UAAgB;4CAAC,QAAQ;;;;;;sDAC1B,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,mIAAA,CAAA,UAAa;gDACZ,QAAQ;gDACR,iBAAiB,IAAM;;;;;;;;;;;;;;;;;gCAM9B,cAAc,8BACb,8OAAC,4IAAA,CAAA,UAAsB;oCACrB,QAAQ;oCACR,qBAAqB,IAAM;;;;;;gCAI9B,cAAc,2BACb,8OAAC,mIAAA,CAAA,UAAa;oCACZ,QAAQ;oCACR,iBAAiB,IAAM;;;;;;gCAI1B,cAAc,2BACb,8OAAC,mIAAA,CAAA,UAAa;oCACZ,QAAQ;oCACR,iBAAiB,IAAM;;;;;;gCAI1B,cAAc,yBACb,8OAAC,iIAAA,CAAA,UAAW;oCAAC,QAAQ;;;;;;gCAGtB,cAAc,8BACb,8OAAC,sIAAA,CAAA,UAAgB;oCAAC,QAAQ;;;;;;gCAG3B,cAAc,2BACb,8OAAC,mIAAA,CAAA,UAAa;oCAAC,UAAU;oCAAU,WAAW,OAAO,MAAM,KAAK;;;;;;;;;;;;;;;;;;8BAKtE,8OAAC;oBAAO,WAAU;;sCAChB,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;;wCAAoC;wCACvC,IAAI,OAAO,WAAW;wCAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQjD", "debugId": null}}]}