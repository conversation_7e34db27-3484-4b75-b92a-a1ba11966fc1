'use client';

import { useState } from 'react';

interface ModInstallationModalProps {
  isOpen: boolean;
  onClose: () => void;
  serverType: 'forge' | 'fabric';
  serverName: string;
  serverId: string;
}

export default function ModInstallationModal({ 
  isOpen, 
  onClose, 
  serverType, 
  serverName, 
  serverId 
}: ModInstallationModalProps) {
  const [activeTab, setActiveTab] = useState<'download' | 'install' | 'configure'>('download');

  if (!isOpen) return null;

  const modName = serverType === 'forge' ? 'BlocksConnect Forge Mod' : 'BlocksConnect Fabric Mod';
  const modFile = serverType === 'forge' 
    ? 'blocksconnect-forge-mod-1.0.0.jar' 
    : 'blocksconnect-fabric-mod-1.0.0.jar';

  const downloadUrl = serverType === 'forge'
    ? '/downloads/blocksconnect-forge-mod-1.0.0.jar'
    : '/downloads/blocksconnect-fabric-mod-1.0.0.jar';

  const capitalizedServerType = serverType ? serverType.charAt(0).toUpperCase() + serverType.slice(1) : '';

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-gray-900 rounded-xl border border-white/10 max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="px-6 py-4 border-b border-white/10 bg-gradient-to-r from-blue-500/10 to-purple-500/10">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-bold text-white flex items-center">
                <svg className="h-6 w-6 mr-3 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                </svg>
                Install {modName}
              </h2>
              <p className="text-gray-300 text-sm mt-1">
                Get enhanced statistics and real-time monitoring for <span className="font-semibold">{serverName}</span>
              </p>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-white transition-colors"
            >
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* Benefits Section */}
        <div className="px-6 py-4 bg-gradient-to-r from-green-500/10 to-blue-500/10 border-b border-white/10">
          <h3 className="text-lg font-semibold text-white mb-3">🚀 Enhanced Features with Mod</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div className="flex items-start space-x-2">
              <svg className="h-4 w-4 text-green-400 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
              <span className="text-gray-300">Real-time player join/leave events</span>
            </div>
            <div className="flex items-start space-x-2">
              <svg className="h-4 w-4 text-green-400 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
              <span className="text-gray-300">Detailed player statistics & positions</span>
            </div>
            <div className="flex items-start space-x-2">
              <svg className="h-4 w-4 text-green-400 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
              <span className="text-gray-300">Live server performance metrics</span>
            </div>
            <div className="flex items-start space-x-2">
              <svg className="h-4 w-4 text-green-400 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
              <span className="text-gray-300">Advanced player management tools</span>
            </div>
          </div>
        </div>

        {/* Tabs */}
        <div className="px-6 py-2 border-b border-white/10">
          <div className="flex space-x-1">
            {[
              { id: 'download', label: '1. Download', icon: '📥' },
              { id: 'install', label: '2. Install', icon: '⚙️' },
              { id: 'configure', label: '3. Configure', icon: '🔧' }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  activeTab === tab.id
                    ? 'bg-blue-500/20 text-blue-400 border border-blue-500/30'
                    : 'text-gray-400 hover:text-white hover:bg-white/5'
                }`}
              >
                {tab.icon} {tab.label}
              </button>
            ))}
          </div>
        </div>

        {/* Content */}
        <div className="px-6 py-6 max-h-96 overflow-y-auto">
          {activeTab === 'download' && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-white">Download the Mod</h3>
              <div className="bg-gray-800/50 rounded-lg p-4 border border-white/10">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-white font-medium">{modName}</p>
                    <p className="text-gray-400 text-sm">Version 1.0.0 • Compatible with Minecraft 1.21.1</p>
                  </div>
                  <a
                    href={downloadUrl}
                    download={modFile}
                    className="btn-primary px-4 py-2 text-sm"
                  >
                    Download
                  </a>
                </div>
              </div>
              <div className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4">
                <p className="text-blue-300 text-sm">
                  <strong>Note:</strong> The mod is specifically designed for {capitalizedServerType} servers. 
                  Make sure your server is running {capitalizedServerType} before installing.
                </p>
              </div>
            </div>
          )}

          {activeTab === 'install' && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-white">Install the Mod</h3>
              <div className="space-y-3">
                <div className="bg-gray-800/50 rounded-lg p-4 border border-white/10">
                  <h4 className="text-white font-medium mb-2">Step 1: Stop Your Server</h4>
                  <p className="text-gray-300 text-sm">
                    Make sure your Minecraft server is stopped before installing the mod.
                  </p>
                </div>
                <div className="bg-gray-800/50 rounded-lg p-4 border border-white/10">
                  <h4 className="text-white font-medium mb-2">Step 2: Upload the Mod</h4>
                  <p className="text-gray-300 text-sm mb-2">
                    Place the downloaded <code className="bg-gray-700 px-1 rounded">{modFile}</code> file in your server's <code className="bg-gray-700 px-1 rounded">mods/</code> folder.
                  </p>
                  <div className="bg-gray-900 rounded p-2 text-xs text-gray-300 font-mono">
                    /path/to/your/server/mods/{modFile}
                  </div>
                </div>
                <div className="bg-gray-800/50 rounded-lg p-4 border border-white/10">
                  <h4 className="text-white font-medium mb-2">Step 3: Start Your Server</h4>
                  <p className="text-gray-300 text-sm">
                    Start your server. The mod will automatically create a configuration file on first run.
                  </p>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'configure' && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-white">Configure the Mod</h3>
              <div className="space-y-3">
                <div className="bg-gray-800/50 rounded-lg p-4 border border-white/10">
                  <h4 className="text-white font-medium mb-2">Configuration File</h4>
                  <p className="text-gray-300 text-sm mb-3">
                    Edit <code className="bg-gray-700 px-1 rounded">config/blocksconnect.json</code> with these settings:
                  </p>
                  <div className="bg-gray-900 rounded p-3 text-xs text-gray-300 font-mono overflow-x-auto">
                    <pre>{`{
  "apiBaseUrl": "${window.location.origin}/api",
  "serverToken": "your-server-token-here",
  "serverId": "${serverId}",
  "enablePlayerSync": true,
  "enablePermissionSync": true,
  "enableEventLogging": true,
  "syncIntervalSeconds": 30,
  "debugMode": false
}`}</pre>
                  </div>
                </div>
                <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-4">
                  <h4 className="text-yellow-300 font-medium mb-2">⚠️ Important</h4>
                  <p className="text-yellow-200 text-sm">
                    You'll need to generate a server token from your server settings page. 
                    The <code className="bg-yellow-900/30 px-1 rounded">serverId</code> above is already filled in for this server.
                  </p>
                </div>
                <div className="bg-green-500/10 border border-green-500/20 rounded-lg p-4">
                  <h4 className="text-green-300 font-medium mb-2">✅ Next Steps</h4>
                  <p className="text-green-200 text-sm">
                    After configuration, restart your server and check the server logs to confirm the mod is connecting successfully.
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="px-6 py-4 border-t border-white/10 bg-gray-800/30">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-400">
              Need help? Check our <a href="#" className="text-blue-400 hover:text-blue-300">documentation</a> or <a href="#" className="text-blue-400 hover:text-blue-300">support</a>.
            </div>
            <div className="flex space-x-3">
              <button
                onClick={onClose}
                className="px-4 py-2 text-gray-400 hover:text-white transition-colors"
              >
                Maybe Later
              </button>
              <button
                onClick={onClose}
                className="btn-primary px-4 py-2"
              >
                Done
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
