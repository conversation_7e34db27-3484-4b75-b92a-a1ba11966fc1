{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_65fe3fd6._.js", "server/edge/chunks/[root-of-the-server]__de110ef0._.js", "server/edge/chunks/edge-wrapper_12a3b8ee.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api/auth|_next/static|_next/image|favicon.ico).*){(\\\\.json)}?", "originalSource": "/((?!api/auth|_next/static|_next/image|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "o0ZYA6Addiyumlp5vP3Ybme8Wp/5AW8QLzPtj8SlCuE=", "__NEXT_PREVIEW_MODE_ID": "db4a14fc35ab79d18b22a4d3f4d57128", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "86e4e7a85f7f5200e922afea724c18b5dec2217755a713095171880fb690b3f1", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "19b4cda0253a522c2671f36afa175efb032d057d7bd34231ce3a24c4c9075d21"}}}, "instrumentation": null, "functions": {}}