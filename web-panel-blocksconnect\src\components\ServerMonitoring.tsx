'use client';

import { useState, useEffect, useRef } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { Server, buildAuthHeaders } from '../services/api';

const API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';

interface ServerMonitoringProps {
  server: Server;
}

interface ServerStats {
  cpu: number;
  memory: {
    used: number;
    total: number;
    percentage: number;
  };
  disk: {
    used: number;
    total: number;
    percentage: number;
  };
  network: {
    bytesIn: number;
    bytesOut: number;
  };
  players: {
    online: number;
    max: number;
    list: string[];
  };
  tps: number;
  uptime: number;
  worldSize: number;
}

interface ChartData {
  timestamp: number;
  cpu: number;
  memory: number;
  players: number;
  tps: number;
}

export default function ServerMonitoring({ server }: ServerMonitoringProps) {
  const [stats, setStats] = useState<ServerStats | null>(null);
  const [chartData, setChartData] = useState<ChartData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const { getToken } = useAuth();
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    fetchStats();
    
    if (autoRefresh) {
      intervalRef.current = setInterval(fetchStats, 5000); // Update every 5 seconds
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [server.id, autoRefresh]);

  const fetchStats = async () => {
    try {
      const token = await getToken();
      const headers = buildAuthHeaders(token);
      const response = await fetch(`${API_URL}/minecraft/servers/${server.id}/stats`, {
        headers
      });
      
      if (response.ok) {
        const data = await response.json();
        setStats(data.stats);
        
        // Add to chart data (keep last 20 points)
        const newDataPoint: ChartData = {
          timestamp: Date.now(),
          cpu: data.stats.cpu,
          memory: data.stats.memory.percentage,
          players: data.stats.players.online,
          tps: data.stats.tps
        };
        
        setChartData(prev => {
          const updated = [...prev, newDataPoint];
          return updated.slice(-20); // Keep only last 20 points
        });
        
        setError(null);
      } else {
        setError('Failed to fetch server statistics');
      }
    } catch (err) {
      setError('Failed to fetch server statistics');
    } finally {
      setIsLoading(false);
    }
  };

  const formatBytes = (bytes: number) => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const formatUptime = (seconds: number) => {
    const days = Math.floor(seconds / 86400);
    const hours = Math.floor((seconds % 86400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    
    if (days > 0) return `${days}d ${hours}h ${minutes}m`;
    if (hours > 0) return `${hours}h ${minutes}m`;
    return `${minutes}m`;
  };

  const getStatusColor = (percentage: number) => {
    if (percentage < 50) return 'text-green-400';
    if (percentage < 80) return 'text-yellow-400';
    return 'text-red-400';
  };

  const getTPSColor = (tps: number) => {
    if (tps >= 19) return 'text-green-400';
    if (tps >= 15) return 'text-yellow-400';
    return 'text-red-400';
  };

  if (isLoading && !stats) {
    return (
      <div className="card overflow-hidden fade-in-fast">
        <div className="px-6 lg:px-8 py-6 border-b border-white/10 bg-gradient-to-r from-cyan-500/10 to-blue-500/10">
          <h2 className="text-xl lg:text-2xl font-bold flex items-center gradient-text">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 lg:h-6 lg:w-6 mr-3 text-cyan-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
            Server Monitoring
          </h2>
          <p className="text-gray-300 mt-2 font-light text-sm lg:text-base">Real-time server performance metrics</p>
        </div>
        <div className="p-8 text-center">
          <div className="animate-spin h-8 w-8 border-4 border-cyan-500/30 border-t-cyan-500 rounded-full mx-auto mb-4"></div>
          <p className="text-gray-400">Loading server statistics...</p>
        </div>
      </div>
    );
  }

  if (error && !stats) {
    return (
      <div className="card overflow-hidden fade-in-fast">
        <div className="px-6 lg:px-8 py-6 border-b border-white/10 bg-gradient-to-r from-cyan-500/10 to-blue-500/10">
          <h2 className="text-xl lg:text-2xl font-bold flex items-center gradient-text">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 lg:h-6 lg:w-6 mr-3 text-cyan-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
            Server Monitoring
          </h2>
        </div>
        <div className="p-8 text-center">
          <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-gradient-to-br from-red-500/20 to-red-600/20 flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h4 className="text-lg font-semibold text-white mb-2">Monitoring Unavailable</h4>
          <p className="text-gray-400 font-light mb-4">{error}</p>
          <button
            onClick={fetchStats}
            className="bg-cyan-500 hover:bg-cyan-600 text-white px-4 py-2 rounded-lg font-semibold transition-all duration-300"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="card overflow-hidden fade-in-fast">
      <div className="px-6 lg:px-8 py-6 border-b border-white/10 bg-gradient-to-r from-cyan-500/10 to-blue-500/10">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-xl lg:text-2xl font-bold flex items-center gradient-text">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 lg:h-6 lg:w-6 mr-3 text-cyan-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
              Server Monitoring
            </h2>
            <p className="text-gray-300 mt-2 font-light text-sm lg:text-base">Real-time server performance metrics</p>
          </div>
          <div className="flex items-center gap-4">
            <label className="flex items-center space-x-2 cursor-pointer">
              <input
                type="checkbox"
                className="form-checkbox h-4 w-4 text-cyan-500"
                checked={autoRefresh}
                onChange={(e) => setAutoRefresh(e.target.checked)}
              />
              <span className="text-gray-300 text-sm">Auto-refresh</span>
            </label>
            <button
              onClick={fetchStats}
              className="bg-cyan-500/20 hover:bg-cyan-500/30 border border-cyan-500/30 hover:border-cyan-500/50 text-cyan-400 hover:text-cyan-300 px-3 py-2 rounded-lg text-sm font-semibold transition-all duration-300 hover:scale-105"
            >
              Refresh
            </button>
          </div>
        </div>
      </div>

      {stats && (
        <div className="p-6 lg:p-8 space-y-8">
          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="bg-gradient-to-br from-green-500/10 to-emerald-500/10 rounded-lg p-4 border border-green-500/20">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-green-300 font-semibold">Players Online</h3>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                </svg>
              </div>
              <p className="text-2xl font-bold text-white">{stats.players.online}/{stats.players.max}</p>
              <p className="text-green-400 text-sm">{Math.round((stats.players.online / stats.players.max) * 100)}% capacity</p>
            </div>

            <div className="bg-gradient-to-br from-blue-500/10 to-cyan-500/10 rounded-lg p-4 border border-blue-500/20">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-blue-300 font-semibold">CPU Usage</h3>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z" />
                </svg>
              </div>
              <p className={`text-2xl font-bold ${getStatusColor(stats.cpu)}`}>{stats.cpu.toFixed(1)}%</p>
              <div className="w-full bg-gray-700 rounded-full h-2 mt-2">
                <div className={`h-2 rounded-full ${stats.cpu < 50 ? 'bg-green-500' : stats.cpu < 80 ? 'bg-yellow-500' : 'bg-red-500'}`} style={{ width: `${Math.min(stats.cpu, 100)}%` }}></div>
              </div>
            </div>

            <div className="bg-gradient-to-br from-purple-500/10 to-pink-500/10 rounded-lg p-4 border border-purple-500/20">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-purple-300 font-semibold">Memory</h3>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-purple-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4" />
                </svg>
              </div>
              <p className={`text-2xl font-bold ${getStatusColor(stats.memory.percentage)}`}>{stats.memory.percentage.toFixed(1)}%</p>
              <p className="text-purple-400 text-sm">{formatBytes(stats.memory.used)} / {formatBytes(stats.memory.total)}</p>
            </div>

            <div className="bg-gradient-to-br from-yellow-500/10 to-orange-500/10 rounded-lg p-4 border border-yellow-500/20">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-yellow-300 font-semibold">TPS</h3>
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <p className={`text-2xl font-bold ${getTPSColor(stats.tps)}`}>{stats.tps.toFixed(1)}</p>
              <p className="text-yellow-400 text-sm">Ticks per second</p>
            </div>
          </div>

          {/* Additional Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-white/5 rounded-lg p-4">
              <h3 className="text-white font-semibold mb-3">Server Info</h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-400">Uptime:</span>
                  <span className="text-white">{formatUptime(stats.uptime)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">World Size:</span>
                  <span className="text-white">{formatBytes(stats.worldSize)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Disk Usage:</span>
                  <span className={getStatusColor(stats.disk.percentage)}>{stats.disk.percentage.toFixed(1)}%</span>
                </div>
              </div>
            </div>

            <div className="bg-white/5 rounded-lg p-4">
              <h3 className="text-white font-semibold mb-3">Network</h3>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-400">Bytes In:</span>
                  <span className="text-white">{formatBytes(stats.network.bytesIn)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Bytes Out:</span>
                  <span className="text-white">{formatBytes(stats.network.bytesOut)}</span>
                </div>
              </div>
            </div>

            <div className="bg-white/5 rounded-lg p-4">
              <h3 className="text-white font-semibold mb-3">Online Players</h3>
              <div className="space-y-1 text-sm max-h-20 overflow-y-auto">
                {stats.players.list.length === 0 ? (
                  <p className="text-gray-400">No players online</p>
                ) : (
                  stats.players.list.map((player, index) => (
                    <div key={index} className="text-white">{player}</div>
                  ))
                )}
              </div>
            </div>
          </div>

          {/* Simple Chart Placeholder */}
          {chartData.length > 1 && (
            <div className="bg-white/5 rounded-lg p-4">
              <h3 className="text-white font-semibold mb-4">Performance History</h3>
              <div className="text-center py-8">
                <p className="text-gray-400">Chart visualization would be implemented here</p>
                <p className="text-gray-500 text-sm mt-2">Showing CPU, Memory, and TPS over time</p>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
