package com.blocksconnect;

import com.blocksconnect.api.BlocksConnectApiClient;
import com.blocksconnect.config.ModConfig;
import com.blocksconnect.events.PlayerEventHandler;
import net.minecraft.server.level.ServerPlayer;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.event.entity.player.PlayerEvent;
import net.minecraftforge.event.server.ServerStartedEvent;
import net.minecraftforge.event.server.ServerStoppingEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.ModLoadingContext;
import net.minecraftforge.fml.common.Mod;
import net.minecraftforge.fml.config.ModConfig.Type;
import net.minecraftforge.fml.event.lifecycle.FMLCommonSetupEvent;
import net.minecraftforge.fml.javafmlmod.FMLJavaModLoadingContext;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

/**
 * Main mod class for BlocksConnect integration
 * Handles initialization and setup of the mod using Forge's event system
 */
@Mod(BlocksConnectMod.MOD_ID)
public class BlocksConnectMod {
    public static final String MOD_ID = "blocksconnect";
    public static final Logger LOGGER = LogManager.getLogger(MOD_ID);
    
    private static BlocksConnectMod instance;
    private ModConfig config;
    private BlocksConnectApiClient apiClient;
    private PlayerEventHandler playerEventHandler;
    
    public BlocksConnectMod() {
        instance = this;

        // Register the setup method for modloading
        @SuppressWarnings("removal")
        var modEventBus = FMLJavaModLoadingContext.get().getModEventBus();
        modEventBus.addListener(this::setup);

        // Register ourselves for server and other game events we are interested in
        MinecraftForge.EVENT_BUS.register(this);

        LOGGER.info("BlocksConnect mod constructor called");
    }
    
    private void setup(final FMLCommonSetupEvent event) {
        LOGGER.info("Initializing BlocksConnect mod...");
        
        // Load configuration
        config = new ModConfig();
        config.load();
        
        // Register configuration
        @SuppressWarnings("removal")
        var modLoadingContext = ModLoadingContext.get();
        modLoadingContext.registerConfig(Type.COMMON, config.getConfigSpec());
        
        // Initialize API client
        apiClient = new BlocksConnectApiClient(config);
        
        // Initialize event handlers
        playerEventHandler = new PlayerEventHandler(apiClient, config);
        
        LOGGER.info("BlocksConnect mod initialized successfully!");
    }
    
    @SubscribeEvent
    public void onPlayerJoin(PlayerEvent.PlayerLoggedInEvent event) {
        if (event.getEntity() instanceof ServerPlayer serverPlayer) {
            playerEventHandler.onPlayerJoin(serverPlayer);
        }
    }
    
    @SubscribeEvent
    public void onPlayerLeave(PlayerEvent.PlayerLoggedOutEvent event) {
        if (event.getEntity() instanceof ServerPlayer serverPlayer) {
            playerEventHandler.onPlayerLeave(serverPlayer);
        }
    }
    
    @SubscribeEvent
    public void onServerStarted(ServerStartedEvent event) {
        LOGGER.info("Server started, initializing BlocksConnect integration...");
        playerEventHandler.onServerStart(event.getServer());
    }
    
    @SubscribeEvent
    public void onServerStopping(ServerStoppingEvent event) {
        LOGGER.info("Server stopping, cleaning up BlocksConnect integration...");
        playerEventHandler.onServerStop(event.getServer());
    }
    
    public static BlocksConnectMod getInstance() {
        return instance;
    }
    
    public ModConfig getConfig() {
        return config;
    }
    
    public BlocksConnectApiClient getApiClient() {
        return apiClient;
    }
    
    public PlayerEventHandler getPlayerEventHandler() {
        return playerEventHandler;
    }
}
