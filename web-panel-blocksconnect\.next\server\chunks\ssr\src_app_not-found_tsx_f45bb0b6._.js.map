{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/BlocksConnect/web-panel-blocksconnect/src/app/not-found.tsx"], "sourcesContent": ["'use client';\n\nimport { useRouter } from 'next/navigation';\nimport { useEffect, useState } from 'react';\n\nexport default function NotFound() {\n  const router = useRouter();\n  const [mounted, setMounted] = useState(false);\n\n  useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  const handleGoHome = () => {\n    router.push('/');\n  };\n\n  const handleGoBack = () => {\n    router.back();\n  };\n\n  if (!mounted) {\n    return null; // Prevent hydration mismatch\n  }\n\n  return (\n    <div className=\"min-h-screen flex items-center justify-center px-4 md:px-8 relative overflow-hidden\">\n      {/* Decorative background elements */}\n      <div className=\"decorative-blob decorative-blob-1\"></div>\n      <div className=\"decorative-blob decorative-blob-2\"></div>\n      <div className=\"decorative-blob decorative-blob-3\"></div>\n\n      <div className=\"w-full max-w-2xl mx-auto text-center space-y-8 fade-in-up\">\n        {/* 404 Icon */}\n        <div className=\"flex justify-center mb-8\">\n          <div className=\"relative\">\n            <div className=\"w-32 h-32 rounded-full flex items-center justify-center relative overflow-hidden card glow\">\n              <div className=\"absolute inset-0 bg-gradient-to-br from-blue-500/20 to-purple-500/20\"></div>\n              <svg \n                xmlns=\"http://www.w3.org/2000/svg\" \n                className=\"h-16 w-16 text-blue-400 relative z-10\" \n                fill=\"none\" \n                viewBox=\"0 0 24 24\" \n                stroke=\"currentColor\"\n              >\n                <path \n                  strokeLinecap=\"round\" \n                  strokeLinejoin=\"round\" \n                  strokeWidth={1.5} \n                  d=\"M9.172 16.172a4 4 0 015.656 0M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" \n                />\n              </svg>\n            </div>\n            {/* Floating particles around the icon */}\n            <div className=\"absolute -top-2 -left-2 w-3 h-3 bg-blue-400 rounded-full opacity-60 animate-pulse\"></div>\n            <div className=\"absolute -bottom-2 -right-2 w-2 h-2 bg-purple-400 rounded-full opacity-60 animate-pulse delay-300\"></div>\n            <div className=\"absolute top-1/2 -right-4 w-2 h-2 bg-blue-300 rounded-full opacity-40 animate-pulse delay-500\"></div>\n          </div>\n        </div>\n\n        {/* Error Code */}\n        <div className=\"space-y-4\">\n          <h1 className=\"text-8xl md:text-9xl font-black gradient-text tracking-tight\">\n            404\n          </h1>\n          <div className=\"h-1 w-24 mx-auto bg-gradient-to-r from-blue-500 to-purple-500 rounded-full\"></div>\n        </div>\n\n        {/* Error Message */}\n        <div className=\"space-y-4 fade-in-up delay-200\">\n          <h2 className=\"text-3xl md:text-4xl font-bold text-white mb-4\">\n            Page Not Found\n          </h2>\n          <p className=\"text-lg md:text-xl text-gray-300 font-light max-w-lg mx-auto leading-relaxed\">\n            Oops! The page you're looking for seems to have wandered off into the digital void. \n            Don't worry, even the best explorers sometimes take a wrong turn.\n          </p>\n          <h3 className=\"text-lg font-semibold text-white mb-4\">Maybe you we're looking for something else?</h3>\n          <ul className=\"space-y-2 text-gray-300\">\n            <li className=\"hover:text-white transition-colors\">\n              <a href=\"/dashboard\">Dashboard</a>\n            </li>\n            <li className=\"hover:text-white transition-colors\">\n              <a href=\"/minecraft/dashboard\">Minecraft Dashboard</a>\n            </li>\n            <li className=\"hover:text-white transition-colors\">\n              <a href=\"https://your-nextcloud-domain.example.com\" target=\"_blank\" rel=\"noopener noreferrer\">File Manager (Nextcloud)</a>\n            </li>\n          </ul>\n        </div>\n\n        {/* Action Buttons */}\n        <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center pt-8 fade-in-up delay-400\">\n          <button\n            onClick={handleGoHome}\n            className=\"btn-primary px-8 py-4 text-lg font-semibold flex items-center gap-3 min-w-[200px] justify-center\"\n          >\n            <svg \n              xmlns=\"http://www.w3.org/2000/svg\" \n              className=\"h-5 w-5\" \n              fill=\"none\" \n              viewBox=\"0 0 24 24\" \n              stroke=\"currentColor\"\n            >\n              <path \n                strokeLinecap=\"round\" \n                strokeLinejoin=\"round\" \n                strokeWidth={2} \n                d=\"M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6\" \n              />\n            </svg>\n            Go Home\n          </button>\n          \n          <button\n            onClick={handleGoBack}\n            className=\"btn-secondary px-8 py-4 text-lg font-semibold flex items-center gap-3 min-w-[200px] justify-center\"\n          >\n            <svg \n              xmlns=\"http://www.w3.org/2000/svg\" \n              className=\"h-5 w-5\" \n              fill=\"none\" \n              viewBox=\"0 0 24 24\" \n              stroke=\"currentColor\"\n            >\n              <path \n                strokeLinecap=\"round\" \n                strokeLinejoin=\"round\" \n                strokeWidth={2} \n                d=\"M10 19l-7-7m0 0l7-7m-7 7h18\" \n              />\n            </svg>\n            Go Back\n          </button>\n        </div>\n\n        {/* Additional Help */}\n        <div className=\"pt-8 fade-in-up delay-500\">\n          <div className=\"card p-6 max-w-md mx-auto\">\n            <h3 className=\"text-lg font-semibold text-white mb-3 flex items-center justify-center gap-2\">\n              <svg \n                xmlns=\"http://www.w3.org/2000/svg\" \n                className=\"h-5 w-5 text-blue-400\" \n                fill=\"none\" \n                viewBox=\"0 0 24 24\" \n                stroke=\"currentColor\"\n              >\n                <path \n                  strokeLinecap=\"round\" \n                  strokeLinejoin=\"round\" \n                  strokeWidth={2} \n                  d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" \n                />\n              </svg>\n              Need Help?\n            </h3>\n            <p className=\"text-gray-300 text-sm leading-relaxed\">\n              If you believe this is an error, please check the URL or contact support. \n              You can also visit our main dashboard to explore available features.\n            </p>\n          </div>\n        </div>\n\n        {/* Branding */}\n        <div className=\"pt-8 fade-in-up delay-600\">\n          <p className=\"text-gray-500 text-sm\">\n            © {new Date().getFullYear()} BlocksConnect Panel\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;IACb,GAAG,EAAE;IAEL,MAAM,eAAe;QACnB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,eAAe;QACnB,OAAO,IAAI;IACb;IAEA,IAAI,CAAC,SAAS;QACZ,OAAO,MAAM,6BAA6B;IAC5C;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;;;;;0BACf,8OAAC;gBAAI,WAAU;;;;;;0BACf,8OAAC;gBAAI,WAAU;;;;;;0BAEf,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CACC,OAAM;4CACN,WAAU;4CACV,MAAK;4CACL,SAAQ;4CACR,QAAO;sDAEP,cAAA,8OAAC;gDACC,eAAc;gDACd,gBAAe;gDACf,aAAa;gDACb,GAAE;;;;;;;;;;;;;;;;;8CAKR,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;kCAKnB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA+D;;;;;;0CAG7E,8OAAC;gCAAI,WAAU;;;;;;;;;;;;kCAIjB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAiD;;;;;;0CAG/D,8OAAC;gCAAE,WAAU;0CAA+E;;;;;;0CAI5F,8OAAC;gCAAG,WAAU;0CAAwC;;;;;;0CACtD,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;wCAAG,WAAU;kDACZ,cAAA,8OAAC;4CAAE,MAAK;sDAAa;;;;;;;;;;;kDAEvB,8OAAC;wCAAG,WAAU;kDACZ,cAAA,8OAAC;4CAAE,MAAK;sDAAuB;;;;;;;;;;;kDAEjC,8OAAC;wCAAG,WAAU;kDACZ,cAAA,8OAAC;4CAAE,MAAK;4CAA4C,QAAO;4CAAS,KAAI;sDAAsB;;;;;;;;;;;;;;;;;;;;;;;kCAMpG,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,8OAAC;wCACC,OAAM;wCACN,WAAU;wCACV,MAAK;wCACL,SAAQ;wCACR,QAAO;kDAEP,cAAA,8OAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,aAAa;4CACb,GAAE;;;;;;;;;;;oCAEA;;;;;;;0CAIR,8OAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,8OAAC;wCACC,OAAM;wCACN,WAAU;wCACV,MAAK;wCACL,SAAQ;wCACR,QAAO;kDAEP,cAAA,8OAAC;4CACC,eAAc;4CACd,gBAAe;4CACf,aAAa;4CACb,GAAE;;;;;;;;;;;oCAEA;;;;;;;;;;;;;kCAMV,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;4CACC,OAAM;4CACN,WAAU;4CACV,MAAK;4CACL,SAAQ;4CACR,QAAO;sDAEP,cAAA,8OAAC;gDACC,eAAc;gDACd,gBAAe;gDACf,aAAa;gDACb,GAAE;;;;;;;;;;;wCAEA;;;;;;;8CAGR,8OAAC;oCAAE,WAAU;8CAAwC;;;;;;;;;;;;;;;;;kCAQzD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;;gCAAwB;gCAChC,IAAI,OAAO,WAAW;gCAAG;;;;;;;;;;;;;;;;;;;;;;;;AAMxC", "debugId": null}}]}