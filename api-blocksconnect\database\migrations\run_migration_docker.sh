#!/bin/bash

echo "🚀 BlocksConnect Database Migration (Docker)"
echo "============================================="
echo

# Check if Docker is running
if ! docker info >/dev/null 2>&1; then
    echo "❌ Docker is not running or not accessible"
    echo "Please start Docker and try again"
    exit 1
fi

# Check if docker-compose is available
if ! command -v docker-compose &> /dev/null; then
    echo "❌ docker-compose not found"
    echo "Please install docker-compose and try again"
    exit 1
fi

# Check if we're in the right directory
if [ ! -f "../../docker-compose.yml" ]; then
    echo "❌ docker-compose.yml not found"
    echo "Please run this script from the api-blocksconnect/database/migrations directory"
    exit 1
fi

echo "🔍 Checking if PostgreSQL container is running..."

# Check if postgres container is running
if ! docker-compose -f ../../docker-compose.yml ps postgres | grep -q "Up"; then
    echo "⚠️  PostgreSQL container is not running"
    echo "Starting PostgreSQL container..."
    
    cd ../../
    docker-compose up -d postgres
    
    echo "⏳ Waiting for PostgreSQL to be ready..."
    sleep 10
    
    # Wait for postgres to be healthy
    for i in {1..30}; do
        if docker-compose exec postgres pg_isready -U blocksconnect >/dev/null 2>&1; then
            echo "✅ PostgreSQL is ready"
            break
        fi
        echo "⏳ Waiting for PostgreSQL... ($i/30)"
        sleep 2
    done
    
    cd database/migrations/
else
    echo "✅ PostgreSQL container is running"
fi

echo
echo "🔄 Running migration using Docker..."

# Run migration using docker-compose exec
cd ../../
docker-compose exec postgres psql -U blocksconnect -d blocksconnect -f /docker-entrypoint-initdb.d/../../../database/migrations/add_mod_integration_columns.sql

if [ $? -eq 0 ]; then
    echo
    echo "✅ Migration completed successfully!"
    echo
    echo "🔍 Verifying migration..."
    
    # Verify the migration
    docker-compose exec postgres psql -U blocksconnect -d blocksconnect -c "
        SELECT column_name, data_type, is_nullable, column_default
        FROM information_schema.columns 
        WHERE table_name = 'minecraft_servers' 
        AND column_name IN ('has_mod_integration', 'mod_last_seen')
        ORDER BY column_name;
    "
    
    echo
    echo "🎉 Migration verification completed!"
    echo "Your database is now ready for mod integration features."
else
    echo
    echo "❌ Migration failed!"
    echo "Please check the error messages above."
    exit 1
fi
