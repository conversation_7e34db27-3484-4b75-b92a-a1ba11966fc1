# net.minecraftforge:forge:1.21.1-52.0.17:userdev - ats/accesstransformer.cfg
public net.minecraft.advancements.CriteriaTriggers m_10595_(Ljava/lang/String;Lnet/minecraft/advancements/CriterionTrigger;)Lnet/minecraft/advancements/CriterionTrigger; # register
default net.minecraft.client.KeyMapping f_90817_ # isDown
public net.minecraft.client.Minecraft f_90987_ # textureManager
public-f net.minecraft.client.Options f_92059_ # keyMappings
public net.minecraft.client.Options$FieldAccess
protected net.minecraft.client.gui.components.AbstractButton f_290895_ # SPRITES
protected net.minecraft.client.gui.components.AbstractSelectionList$Entry f_93521_ # list
protected net.minecraft.client.gui.components.AbstractSliderButton m_293290_()Lnet/minecraft/resources/ResourceLocation; # getHandleSprite
protected net.minecraft.client.gui.components.AbstractSliderButton m_293389_()Lnet/minecraft/resources/ResourceLocation; # getSprite
protected net.minecraft.client.gui.components.DebugScreenOverlay f_94032_ # block
protected net.minecraft.client.gui.components.DebugScreenOverlay f_94033_ # liquid
protected net.minecraft.client.gui.components.DebugScreenOverlay m_286013_(Lnet/minecraft/client/gui/GuiGraphics;Ljava/util/List;Z)V # renderLines
public net.minecraft.client.gui.screens.MenuScreens m_96206_(Lnet/minecraft/world/inventory/MenuType;Lnet/minecraft/client/gui/screens/MenuScreens$ScreenConstructor;)V # register
public net.minecraft.client.gui.screens.MenuScreens$ScreenConstructor
public net.minecraft.client.gui.screens.Screen f_169369_ # renderables
public net.minecraft.client.model.geom.LayerDefinitions f_171106_ # OUTER_ARMOR_DEFORMATION
public net.minecraft.client.model.geom.LayerDefinitions f_171107_ # INNER_ARMOR_DEFORMATION
public net.minecraft.client.multiplayer.ClientPacketListener f_104899_ # commands
public net.minecraft.client.multiplayer.SessionSearchTrees f_336706_ # CREATIVE_TAGS
public net.minecraft.client.multiplayer.SessionSearchTrees f_337599_ # CREATIVE_NAMES
public net.minecraft.client.multiplayer.SessionSearchTrees$Key
public net.minecraft.client.multiplayer.SessionSearchTrees$Key <init>()V
public net.minecraft.client.particle.ParticleEngine m_107378_(Lnet/minecraft/core/particles/ParticleType;Lnet/minecraft/client/particle/ParticleEngine$SpriteParticleRegistration;)V # register
public net.minecraft.client.particle.ParticleEngine m_107381_(Lnet/minecraft/core/particles/ParticleType;Lnet/minecraft/client/particle/ParticleProvider;)V # register
public net.minecraft.client.particle.ParticleEngine m_272137_(Lnet/minecraft/core/particles/ParticleType;Lnet/minecraft/client/particle/ParticleProvider$Sprite;)V # register
public net.minecraft.client.particle.ParticleEngine$SpriteParticleRegistration
public net.minecraft.client.player.LocalPlayer m_8088_()I # getPermissionLevel
public net.minecraft.client.renderer.GameRenderer m_109128_(Lnet/minecraft/resources/ResourceLocation;)V # loadEffect
public net.minecraft.client.renderer.LevelRenderer m_109817_()Z # shouldShowEntityOutlines
protected-f net.minecraft.client.renderer.RenderStateShard f_110131_ # setupState
public net.minecraft.client.renderer.RenderStateShard$BooleanStateShard
public net.minecraft.client.renderer.RenderStateShard$CullStateShard
public net.minecraft.client.renderer.RenderStateShard$DepthTestStateShard
public net.minecraft.client.renderer.RenderStateShard$EmptyTextureStateShard
public net.minecraft.client.renderer.RenderStateShard$LayeringStateShard
public net.minecraft.client.renderer.RenderStateShard$LightmapStateShard
public net.minecraft.client.renderer.RenderStateShard$MultiTextureStateShard
public net.minecraft.client.renderer.RenderStateShard$OffsetTexturingStateShard
public net.minecraft.client.renderer.RenderStateShard$OutputStateShard
public net.minecraft.client.renderer.RenderStateShard$OverlayStateShard
public net.minecraft.client.renderer.RenderStateShard$ShaderStateShard
public net.minecraft.client.renderer.RenderStateShard$TextureStateShard
protected-f net.minecraft.client.renderer.RenderStateShard$TextureStateShard f_110329_ # blur
protected-f net.minecraft.client.renderer.RenderStateShard$TextureStateShard f_110330_ # mipmap
public net.minecraft.client.renderer.RenderStateShard$TexturingStateShard
public net.minecraft.client.renderer.RenderStateShard$TransparencyStateShard
public net.minecraft.client.renderer.RenderStateShard$WriteMaskStateShard
public net.minecraft.client.renderer.RenderType m_173215_(Ljava/lang/String;Lcom/mojang/blaze3d/vertex/VertexFormat;Lcom/mojang/blaze3d/vertex/VertexFormat$Mode;IZZLnet/minecraft/client/renderer/RenderType$CompositeState;)Lnet/minecraft/client/renderer/RenderType$CompositeRenderType; # create
public net.minecraft.client.renderer.RenderType$CompositeState
public net.minecraft.client.renderer.block.model.BlockElement m_111320_(Lnet/minecraft/core/Direction;)[F # uvsByFace
public net.minecraft.client.renderer.block.model.BlockElement$Deserializer
public net.minecraft.client.renderer.block.model.BlockElement$Deserializer <init>()V # constructor
public net.minecraft.client.renderer.block.model.BlockElementFace$Deserializer
public net.minecraft.client.renderer.block.model.BlockElementFace$Deserializer <init>()V # constructor
public net.minecraft.client.renderer.block.model.BlockFaceUV$Deserializer
public net.minecraft.client.renderer.block.model.BlockFaceUV$Deserializer <init>()V # constructor
public net.minecraft.client.renderer.block.model.BlockModel f_111417_ # textureMap
public net.minecraft.client.renderer.block.model.BlockModel f_111418_ # parent
public net.minecraft.client.renderer.block.model.BlockModel f_111424_ # hasAmbientOcclusion
public net.minecraft.client.renderer.block.model.BlockModel m_111437_(Lnet/minecraft/client/renderer/block/model/BlockElement;Lnet/minecraft/client/renderer/block/model/BlockElementFace;Lnet/minecraft/client/renderer/texture/TextureAtlasSprite;Lnet/minecraft/core/Direction;Lnet/minecraft/client/resources/model/ModelState;)Lnet/minecraft/client/renderer/block/model/BakedQuad; # bakeFace
public net.minecraft.client.renderer.block.model.ItemModelGenerator m_111638_(ILjava/lang/String;Lnet/minecraft/client/renderer/texture/SpriteContents;)Ljava/util/List; # processFrames
public net.minecraft.client.renderer.block.model.ItemOverride$Deserializer
public net.minecraft.client.renderer.block.model.ItemOverride$Deserializer <init>()V # constructor
protected net.minecraft.client.renderer.block.model.ItemOverrides <init>()V # constructor
public net.minecraft.client.renderer.block.model.ItemOverrides$BakedOverride
public net.minecraft.client.renderer.block.model.ItemTransform$Deserializer
public net.minecraft.client.renderer.block.model.ItemTransform$Deserializer <init>()V # constructor
public net.minecraft.client.renderer.block.model.ItemTransform$Deserializer f_111769_ # DEFAULT_ROTATION
public net.minecraft.client.renderer.block.model.ItemTransform$Deserializer f_111770_ # DEFAULT_TRANSLATION
public net.minecraft.client.renderer.block.model.ItemTransform$Deserializer f_111771_ # DEFAULT_SCALE
public net.minecraft.client.renderer.block.model.ItemTransforms$Deserializer
public net.minecraft.client.renderer.block.model.ItemTransforms$Deserializer <init>()V # constructor
public net.minecraft.client.renderer.blockentity.BlockEntityRenderDispatcher f_112253_ # fontRenderer - needed for rendering text in TESR items before entering world
public net.minecraft.client.renderer.blockentity.BlockEntityRenderers m_173590_(Lnet/minecraft/world/level/block/entity/BlockEntityType;Lnet/minecraft/client/renderer/blockentity/BlockEntityRendererProvider;)V # register
private-f net.minecraft.client.renderer.blockentity.PistonHeadRenderer f_112441_ # blockRenderer - it's static so we need to un-finalize in case this class loads to early.
public net.minecraft.client.renderer.blockentity.SkullBlockRenderer f_112519_ # SKIN_BY_TYPE
public net.minecraft.client.renderer.entity.EntityRenderDispatcher f_114362_ # renderers
public net.minecraft.client.renderer.entity.EntityRenderers m_174036_(Lnet/minecraft/world/entity/EntityType;Lnet/minecraft/client/renderer/entity/EntityRendererProvider;)V # register
public net.minecraft.client.renderer.entity.ItemRenderer m_115162_(Lcom/mojang/blaze3d/vertex/PoseStack;Lcom/mojang/blaze3d/vertex/VertexConsumer;Ljava/util/List;Lnet/minecraft/world/item/ItemStack;II)V # renderQuadList
public net.minecraft.client.renderer.entity.ItemRenderer m_115189_(Lnet/minecraft/client/resources/model/BakedModel;Lnet/minecraft/world/item/ItemStack;IILcom/mojang/blaze3d/vertex/PoseStack;Lcom/mojang/blaze3d/vertex/VertexConsumer;)V # renderModelLists
public net.minecraft.client.renderer.entity.LivingEntityRenderer m_115326_(Lnet/minecraft/client/renderer/entity/layers/RenderLayer;)Z # addLayer
public net.minecraft.client.renderer.entity.layers.ElytraLayer f_116934_ # WINGS_LOCATION
public net.minecraft.client.renderer.texture.SpriteContents f_243731_ # byMipLevel
default net.minecraft.client.renderer.texture.SpriteContents f_244575_ # animatedTexture
default net.minecraft.client.renderer.texture.SpriteContents m_245088_()I # getFrameCount
public net.minecraft.client.resources.ClientPackSource m_246691_(Ljava/nio/file/Path;)Lnet/minecraft/server/packs/VanillaPackResources; # createVanillaPackSource
protected net.minecraft.client.resources.TextureAtlasHolder f_118884_ # textureAtlas
protected net.minecraft.client.resources.model.ModelBakery m_119364_(Lnet/minecraft/resources/ResourceLocation;)Lnet/minecraft/client/renderer/block/model/BlockModel; # loadBlockModel
public net.minecraft.client.resources.model.SimpleBakedModel$Builder <init>(ZZZLnet/minecraft/client/renderer/block/model/ItemTransforms;Lnet/minecraft/client/renderer/block/model/ItemOverrides;)V # constructor
public net.minecraft.client.sounds.SoundEngine f_120217_ # soundManager
public net.minecraft.commands.CommandSourceStack f_81288_ # source
public net.minecraft.commands.arguments.selector.EntitySelectorParser m_121229_()V # finalizePredicates
public net.minecraft.commands.arguments.selector.EntitySelectorParser m_121317_()V # parseOptions
public net.minecraft.commands.arguments.selector.options.EntitySelectorOptions m_121453_(Ljava/lang/String;Lnet/minecraft/commands/arguments/selector/options/EntitySelectorOptions$Modifier;Ljava/util/function/Predicate;Lnet/minecraft/network/chat/Component;)V # register
public net.minecraft.core.Holder$Reference m_205769_(Ljava/util/Collection;)V # bindTags
public net.minecraft.core.Holder$Reference m_246870_(Lnet/minecraft/resources/ResourceKey;)V # bindKey
public net.minecraft.core.Holder$Reference m_247654_(Ljava/lang/Object;)V # bindValue
public net.minecraft.core.Holder$Reference$Type
public net.minecraft.core.HolderSet$Named <init>(Lnet/minecraft/core/HolderOwner;Lnet/minecraft/tags/TagKey;)V # constructor
public net.minecraft.core.HolderSet$Named m_205835_(Ljava/util/List;)V # bind
protected net.minecraft.core.IdMapper f_122653_ # nextId
protected net.minecraft.core.IdMapper f_122654_ # tToId - internal map
protected net.minecraft.core.IdMapper f_122655_ # idToT - internal index list
protected net.minecraft.core.MappedRegistry f_244282_ # unregisteredIntrusiveHolders
protected net.minecraft.core.MappedRegistry m_245420_(Lnet/minecraft/resources/ResourceKey;)Lnet/minecraft/core/Holder$Reference; # getOrCreateHolderOrThrow
public net.minecraft.core.RegistrySetBuilder$LazyHolder m_247654_(Ljava/lang/Object;)V # bindValue
public net.minecraft.core.particles.SimpleParticleType <init>(Z)V # constructor
protected net.minecraft.data.loot.BlockLootSubProvider m_245335_(Lnet/minecraft/world/level/ItemLike;)Lnet/minecraft/world/level/storage/loot/LootTable$Builder; # createSilkTouchOnlyTable
protected net.minecraft.data.loot.BlockLootSubProvider m_245602_(Lnet/minecraft/world/level/ItemLike;)Lnet/minecraft/world/level/storage/loot/LootTable$Builder; # createPotFlowerItemTable
protected net.minecraft.data.loot.BlockLootSubProvider m_246900_(Lnet/minecraft/world/level/block/Block;Lnet/minecraft/world/level/storage/loot/predicates/LootItemCondition$Builder;Lnet/minecraft/world/level/storage/loot/entries/LootPoolEntryContainer$Builder;)Lnet/minecraft/world/level/storage/loot/LootTable$Builder; # createSelfDropDispatchTable
protected net.minecraft.data.loot.EntityLootSubProvider m_245552_(Lnet/minecraft/world/entity/EntityType;)Z # canHaveLootTable
protected net.minecraft.data.recipes.RecipeProvider f_236355_ # recipePathProvider
protected net.minecraft.data.recipes.RecipeProvider f_236356_ # advancementPathProvider
protected net.minecraft.data.recipes.RecipeProvider m_176523_(Lnet/minecraft/data/BlockFamily;Lnet/minecraft/data/BlockFamily$Variant;)Lnet/minecraft/world/level/block/Block; # getBaseBlock
protected net.minecraft.data.recipes.RecipeProvider m_176658_(Lnet/minecraft/world/level/ItemLike;Lnet/minecraft/world/item/crafting/Ingredient;)Lnet/minecraft/data/recipes/RecipeBuilder; # buttonBuilder
protected net.minecraft.data.recipes.RecipeProvider m_176678_(Lnet/minecraft/world/level/ItemLike;Lnet/minecraft/world/item/crafting/Ingredient;)Lnet/minecraft/data/recipes/RecipeBuilder; # fenceBuilder
protected net.minecraft.data.recipes.RecipeProvider m_176684_(Lnet/minecraft/world/level/ItemLike;Lnet/minecraft/world/item/crafting/Ingredient;)Lnet/minecraft/data/recipes/RecipeBuilder; # fenceGateBuilder
protected net.minecraft.data.recipes.RecipeProvider m_176720_(Lnet/minecraft/world/level/ItemLike;Lnet/minecraft/world/item/crafting/Ingredient;)Lnet/minecraft/data/recipes/RecipeBuilder; # trapdoorBuilder
protected net.minecraft.data.recipes.RecipeProvider m_176726_(Lnet/minecraft/world/level/ItemLike;Lnet/minecraft/world/item/crafting/Ingredient;)Lnet/minecraft/data/recipes/RecipeBuilder; # signBuilder
protected net.minecraft.data.recipes.RecipeProvider m_245792_(Lnet/minecraft/data/recipes/RecipeCategory;Lnet/minecraft/world/level/ItemLike;Lnet/minecraft/world/item/crafting/Ingredient;)Lnet/minecraft/data/recipes/ShapedRecipeBuilder; # cutBuilder
protected net.minecraft.data.recipes.RecipeProvider m_245864_(Lnet/minecraft/data/recipes/RecipeCategory;Lnet/minecraft/world/level/ItemLike;Lnet/minecraft/world/item/crafting/Ingredient;)Lnet/minecraft/data/recipes/RecipeBuilder; # wallBuilder
protected net.minecraft.data.recipes.RecipeProvider m_247174_(Lnet/minecraft/data/recipes/RecipeCategory;Lnet/minecraft/world/level/ItemLike;Lnet/minecraft/world/item/crafting/Ingredient;)Lnet/minecraft/data/recipes/RecipeBuilder; # polishedBuilder
protected net.minecraft.data.recipes.RecipeProvider m_247347_(Lnet/minecraft/data/recipes/RecipeCategory;Lnet/minecraft/world/level/ItemLike;Lnet/minecraft/world/item/crafting/Ingredient;)Lnet/minecraft/data/recipes/RecipeBuilder; # pressurePlateBuilder
public net.minecraft.data.recipes.packs.VanillaRecipeProvider f_243671_ # COAL_SMELTABLES
public net.minecraft.data.recipes.packs.VanillaRecipeProvider f_243779_ # IRON_SMELTABLES
public net.minecraft.data.recipes.packs.VanillaRecipeProvider f_243908_ # COPPER_SMELTABLES
public net.minecraft.data.recipes.packs.VanillaRecipeProvider f_243974_ # DIAMOND_SMELTABLES
public net.minecraft.data.recipes.packs.VanillaRecipeProvider f_244369_ # GOLD_SMELTABLES
public net.minecraft.data.recipes.packs.VanillaRecipeProvider f_244430_ # EMERALD_SMELTABLES
public net.minecraft.data.recipes.packs.VanillaRecipeProvider f_244565_ # REDSTONE_SMELTABLES
public net.minecraft.data.recipes.packs.VanillaRecipeProvider f_244628_ # LAPIS_SMELTABLES
public-f net.minecraft.data.registries.RegistriesDatapackGenerator m_6055_()Ljava/lang/String; # getName
public net.minecraft.data.tags.IntrinsicHolderTagsProvider$IntrinsicTagAppender
protected net.minecraft.data.tags.TagsProvider f_126543_ # builders
public-f net.minecraft.data.tags.TagsProvider m_6055_()Ljava/lang/String; # getName
public net.minecraft.data.tags.TagsProvider$TagAppender
public net.minecraft.gametest.framework.GameTestServer <init>(Ljava/lang/Thread;Lnet/minecraft/world/level/storage/LevelStorageSource$LevelStorageAccess;Lnet/minecraft/server/packs/repository/PackRepository;Lnet/minecraft/server/WorldStem;Ljava/util/Collection;Lnet/minecraft/core/BlockPos;)V # constructor
public net.minecraft.resources.ResourceLocation m_135835_(C)Z # validNamespaceChar
public net.minecraft.resources.ResourceLocation m_135841_(Ljava/lang/String;)Z # isValidPath
public net.minecraft.resources.ResourceLocation m_135843_(Ljava/lang/String;)Z # isValidNamespace
protected net.minecraft.server.MinecraftServer f_302313_ # nextTickTimeNanos
public net.minecraft.server.MinecraftServer f_303727_ # tickTimesNanos
public net.minecraft.server.MinecraftServer$ReloadableResources
public net.minecraft.server.dedicated.DedicatedServer f_139600_ # consoleInput
public net.minecraft.server.level.ServerChunkCache f_8329_ # level
public net.minecraft.server.level.ServerLevel m_142646_()Lnet/minecraft/world/level/entity/LevelEntityGetter; # getEntities
public net.minecraft.server.level.ServerPlayer f_8940_ # containerCounter
public net.minecraft.server.level.ServerPlayer m_143399_(Lnet/minecraft/world/inventory/AbstractContainerMenu;)V # initMenu
public net.minecraft.server.level.ServerPlayer m_9217_()V # nextContainerCounter
public net.minecraft.server.network.ServerConfigurationPacketListenerImpl m_293514_(Lnet/minecraft/server/network/ConfigurationTask$Type;)V # finishCurrentTask
public net.minecraft.server.packs.repository.FolderRepositorySource$FolderPackDetector
public net.minecraft.server.packs.repository.FolderRepositorySource$FolderPackDetector <init>(Lnet/minecraft/world/level/validation/DirectoryValidator;)V # constructor
public net.minecraft.server.packs.repository.ServerPacksSource m_246173_()Lnet/minecraft/server/packs/VanillaPackResources; # createVanillaPackSource
public net.minecraft.server.packs.resources.FallbackResourceManager f_10599_ # fallbacks
public net.minecraft.util.datafix.fixes.StructuresBecomeConfiguredFix$Conversion
public net.minecraft.util.thread.BlockableEventLoop m_18689_(Ljava/lang/Runnable;)Ljava/util/concurrent/CompletableFuture; # submitAsync
#group public net.minecraft.world.damagesource.DamageSource *() #All methods public, most are already
public net.minecraft.world.damagesource.DamageSource <init>(Lnet/minecraft/core/Holder;Lnet/minecraft/world/entity/Entity;Lnet/minecraft/world/entity/Entity;Lnet/minecraft/world/phys/Vec3;)V # constructor
#endgroup
protected net.minecraft.world.entity.Entity f_19843_ # ENTITY_COUNTER
public net.minecraft.world.entity.Entity m_20078_()Ljava/lang/String; # getEncodeId
public net.minecraft.world.entity.ExperienceOrb f_20770_ # value
public net.minecraft.world.entity.Mob f_21345_ # goalSelector
public net.minecraft.world.entity.Mob f_21346_ # targetSelector
public net.minecraft.world.entity.ai.memory.MemoryModuleType <init>(Ljava/util/Optional;)V # constructor
public net.minecraft.world.entity.ai.sensing.SensorType <init>(Ljava/util/function/Supplier;)V # constructor
protected net.minecraft.world.entity.item.PrimedTnt m_32103_()V # explode - make it easier to extend TNTEntity with custom explosion logic
protected net.minecraft.world.entity.monster.AbstractSkeleton m_7878_()Lnet/minecraft/sounds/SoundEvent; # getStepSound - make AbstractSkeletonEntity implementable
protected net.minecraft.world.entity.monster.Skeleton m_7878_()Lnet/minecraft/sounds/SoundEvent; # getStepSound - make AbstractSkeletonEntity implementable
protected net.minecraft.world.entity.monster.Stray m_7878_()Lnet/minecraft/sounds/SoundEvent; # getStepSound - make AbstractSkeletonEntity implementable
protected net.minecraft.world.entity.monster.WitherSkeleton m_7878_()Lnet/minecraft/sounds/SoundEvent; # getStepSound - make AbstractSkeletonEntity implementable
protected net.minecraft.world.entity.monster.Zombie f_34266_ # conversionTime
public net.minecraft.world.entity.npc.VillagerTrades$DyedArmorForEmeralds
public net.minecraft.world.entity.npc.VillagerTrades$EmeraldForItems
public net.minecraft.world.entity.npc.VillagerTrades$EmeraldsForVillagerTypeItem
public net.minecraft.world.entity.npc.VillagerTrades$EnchantBookForEmeralds
public net.minecraft.world.entity.npc.VillagerTrades$EnchantedItemForEmeralds
public net.minecraft.world.entity.npc.VillagerTrades$FailureItemListing
public net.minecraft.world.entity.npc.VillagerTrades$ItemsAndEmeraldsToItems
public net.minecraft.world.entity.npc.VillagerTrades$ItemsForEmeralds
public net.minecraft.world.entity.npc.VillagerTrades$SuspiciousStewForEmerald
public net.minecraft.world.entity.npc.VillagerTrades$TippedArrowForItemsAndEmeralds
public net.minecraft.world.entity.npc.VillagerTrades$TreasureMapForEmeralds
public net.minecraft.world.entity.npc.VillagerTrades$TypeSpecificTrade
public net.minecraft.world.entity.npc.VillagerType <init>(Ljava/lang/String;)V # constructor
public net.minecraft.world.entity.player.Player m_6915_()V # closeContainer
protected net.minecraft.world.entity.projectile.Projectile <init>(Lnet/minecraft/world/entity/EntityType;Lnet/minecraft/world/level/Level;)V # constructor
private-f net.minecraft.world.entity.raid.Raid$RaiderType f_37813_ # VALUES
public net.minecraft.world.entity.schedule.Activity <init>(Ljava/lang/String;)V # constructor
public net.minecraft.world.inventory.AnvilMenu f_39000_ # repairItemCountCost
public net.minecraft.world.inventory.BrewingStandMenu$PotionSlot
public net.minecraft.world.inventory.MenuType <init>(Lnet/minecraft/world/inventory/MenuType$MenuSupplier;Lnet/minecraft/world/flag/FeatureFlagSet;)V # constructor
public net.minecraft.world.inventory.MenuType$MenuSupplier
public net.minecraft.world.item.CreativeModeTab$Output
public net.minecraft.world.item.CreativeModeTab$TabVisibility
public net.minecraft.world.item.CreativeModeTabs f_256725_ # COLORED_BLOCKS
public net.minecraft.world.item.CreativeModeTabs f_256731_ # SPAWN_EGGS
public net.minecraft.world.item.CreativeModeTabs f_256750_ # SEARCH
public net.minecraft.world.item.CreativeModeTabs f_256776_ # NATURAL_BLOCKS
public net.minecraft.world.item.CreativeModeTabs f_256788_ # BUILDING_BLOCKS
public net.minecraft.world.item.CreativeModeTabs f_256791_ # FUNCTIONAL_BLOCKS
public net.minecraft.world.item.CreativeModeTabs f_256797_ # COMBAT
public net.minecraft.world.item.CreativeModeTabs f_256837_ # OP_BLOCKS
public net.minecraft.world.item.CreativeModeTabs f_256839_ # FOOD_AND_DRINKS
public net.minecraft.world.item.CreativeModeTabs f_256869_ # TOOLS_AND_UTILITIES
public net.minecraft.world.item.CreativeModeTabs f_256917_ # HOTBAR
public net.minecraft.world.item.CreativeModeTabs f_256968_ # INGREDIENTS
public net.minecraft.world.item.CreativeModeTabs f_257028_ # REDSTONE_BLOCKS
public net.minecraft.world.item.CreativeModeTabs f_257039_ # INVENTORY
#group public net.minecraft.world.item.Item <init>
public net.minecraft.world.item.DiggerItem <init>(Lnet/minecraft/world/item/Tier;Lnet/minecraft/tags/TagKey;Lnet/minecraft/world/item/Item$Properties;)V # constructor
#endgroup
private-f net.minecraft.world.item.ItemDisplayContext f_268735_ # id
public net.minecraft.world.item.ItemStackLinkedSet f_260558_ # TYPE_AND_TAG
public net.minecraft.world.item.alchemy.PotionBrewing$Mix
public net.minecraft.world.item.alchemy.PotionBrewing$Mix f_43533_ # ingredient
public net.minecraft.world.item.context.BlockPlaceContext <init>(Lnet/minecraft/world/level/Level;Lnet/minecraft/world/entity/player/Player;Lnet/minecraft/world/InteractionHand;Lnet/minecraft/world/item/ItemStack;Lnet/minecraft/world/phys/BlockHitResult;)V # constructor
public net.minecraft.world.item.context.UseOnContext <init>(Lnet/minecraft/world/level/Level;Lnet/minecraft/world/entity/player/Player;Lnet/minecraft/world/InteractionHand;Lnet/minecraft/world/item/ItemStack;Lnet/minecraft/world/phys/BlockHitResult;)V # constructor
public-f net.minecraft.world.item.crafting.Ingredient
protected net.minecraft.world.item.crafting.Ingredient <init>(Ljava/util/stream/Stream;)V # constructor
public net.minecraft.world.item.crafting.Ingredient m_43938_(Ljava/util/stream/Stream;)Lnet/minecraft/world/item/crafting/Ingredient; # fromValues
public net.minecraft.world.item.crafting.Ingredient$ItemValue
public net.minecraft.world.item.crafting.Ingredient$ItemValue <init>(Lnet/minecraft/world/item/ItemStack;)V # constructor
public net.minecraft.world.item.crafting.Ingredient$TagValue
public net.minecraft.world.item.crafting.Ingredient$TagValue <init>(Lnet/minecraft/tags/TagKey;)V # constructor
public net.minecraft.world.item.crafting.Ingredient$Value
public net.minecraft.world.level.GameRules m_46189_(Ljava/lang/String;Lnet/minecraft/world/level/GameRules$Category;Lnet/minecraft/world/level/GameRules$Type;)Lnet/minecraft/world/level/GameRules$Key; # register
public net.minecraft.world.level.GameRules$BooleanValue m_46250_(Z)Lnet/minecraft/world/level/GameRules$Type; # create
public net.minecraft.world.level.GameRules$BooleanValue m_46252_(ZLjava/util/function/BiConsumer;)Lnet/minecraft/world/level/GameRules$Type; # create
public net.minecraft.world.level.GameRules$IntegerValue m_46294_(ILjava/util/function/BiConsumer;)Lnet/minecraft/world/level/GameRules$Type; # create
public net.minecraft.world.level.GameRules$IntegerValue m_46312_(I)Lnet/minecraft/world/level/GameRules$Type; # create
public net.minecraft.world.level.Level f_46437_ # oRainLevel
public net.minecraft.world.level.Level f_46438_ # rainLevel
public net.minecraft.world.level.Level f_46439_ # oThunderLevel
public net.minecraft.world.level.Level f_46440_ # thunderLevel
public net.minecraft.world.level.biome.Biome$ClimateSettings
protected net.minecraft.world.level.biome.BiomeGenerationSettings$PlainBuilder f_254648_ # features
protected net.minecraft.world.level.biome.BiomeGenerationSettings$PlainBuilder f_254678_ # carvers
protected net.minecraft.world.level.biome.BiomeGenerationSettings$PlainBuilder m_255276_(I)V # addFeatureStepsUpTo
#group protected net.minecraft.world.level.biome.BiomeSpecialEffects$Builder *
protected net.minecraft.world.level.biome.BiomeSpecialEffects$Builder f_48005_ # fogColor
protected net.minecraft.world.level.biome.BiomeSpecialEffects$Builder f_48006_ # waterColor
protected net.minecraft.world.level.biome.BiomeSpecialEffects$Builder f_48007_ # waterFogColor
protected net.minecraft.world.level.biome.BiomeSpecialEffects$Builder f_48008_ # skyColor
protected net.minecraft.world.level.biome.BiomeSpecialEffects$Builder f_48009_ # foliageColorOverride
protected net.minecraft.world.level.biome.BiomeSpecialEffects$Builder f_48010_ # grassColorOverride
protected net.minecraft.world.level.biome.BiomeSpecialEffects$Builder f_48011_ # grassColorModifier
protected net.minecraft.world.level.biome.BiomeSpecialEffects$Builder f_48012_ # ambientParticle
protected net.minecraft.world.level.biome.BiomeSpecialEffects$Builder f_48013_ # ambientLoopSoundEvent
protected net.minecraft.world.level.biome.BiomeSpecialEffects$Builder f_48014_ # ambientMoodSettings
protected net.minecraft.world.level.biome.BiomeSpecialEffects$Builder f_48015_ # ambientAdditionsSettings
protected net.minecraft.world.level.biome.BiomeSpecialEffects$Builder f_48016_ # backgroundMusic
#endgroup
protected net.minecraft.world.level.biome.MobSpawnSettings$Builder f_48362_ # spawners
protected net.minecraft.world.level.biome.MobSpawnSettings$Builder f_48363_ # mobSpawnCosts
protected net.minecraft.world.level.biome.MobSpawnSettings$Builder f_48364_ # creatureGenerationProbability
#group public net.minecraft.world.level.block.Block <init>
public net.minecraft.world.level.block.AttachedStemBlock <init>(Lnet/minecraft/resources/ResourceKey;Lnet/minecraft/resources/ResourceKey;Lnet/minecraft/resources/ResourceKey;Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.AzaleaBlock <init>(Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.BarrierBlock <init>(Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.BaseCoralFanBlock <init>(Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.BaseCoralPlantBlock <init>(Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.BaseCoralWallFanBlock <init>(Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.BigDripleafBlock <init>(Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.BigDripleafStemBlock <init>(Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.BlastFurnaceBlock <init>(Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.ButtonBlock <init>(Lnet/minecraft/world/level/block/state/properties/BlockSetType;ILnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.CactusBlock <init>(Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.CakeBlock <init>(Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.CandleCakeBlock <init>(Lnet/minecraft/world/level/block/Block;Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.CartographyTableBlock <init>(Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.CarvedPumpkinBlock <init>(Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.ChestBlock <init>(Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;Ljava/util/function/Supplier;)V # constructor
public net.minecraft.world.level.block.ChorusFlowerBlock <init>(Lnet/minecraft/world/level/block/Block;Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.ChorusPlantBlock <init>(Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.CoralFanBlock <init>(Lnet/minecraft/world/level/block/Block;Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.CoralPlantBlock <init>(Lnet/minecraft/world/level/block/Block;Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.CoralWallFanBlock <init>(Lnet/minecraft/world/level/block/Block;Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.CraftingTableBlock <init>(Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.CropBlock <init>(Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.DeadBushBlock <init>(Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.DecoratedPotBlock <init>(Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.DirtPathBlock <init>(Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.DispenserBlock <init>(Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.DoorBlock <init>(Lnet/minecraft/world/level/block/state/properties/BlockSetType;Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.EnchantingTableBlock <init>(Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.EndGatewayBlock <init>(Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.EndPortalBlock <init>(Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.EndRodBlock <init>(Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.EnderChestBlock <init>(Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.EquipableCarvedPumpkinBlock <init>(Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.FarmBlock <init>(Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.FletchingTableBlock <init>(Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.FungusBlock <init>(Lnet/minecraft/resources/ResourceKey;Lnet/minecraft/world/level/block/Block;Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.FurnaceBlock <init>(Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.GrindstoneBlock <init>(Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.HalfTransparentBlock <init>(Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.HangingRootsBlock <init>(Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.IronBarsBlock <init>(Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.JigsawBlock <init>(Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.JukeboxBlock <init>(Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.KelpBlock <init>(Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.KelpPlantBlock <init>(Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.LadderBlock <init>(Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.LecternBlock <init>(Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.LeverBlock <init>(Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.LiquidBlock <init>(Lnet/minecraft/world/level/material/FlowingFluid;Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.LoomBlock <init>(Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.MangroveRootsBlock <init>(Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.NetherWartBlock <init>(Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.NyliumBlock <init>(Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.PinkPetalsBlock <init>(Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.PlayerHeadBlock <init>(Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.PlayerWallHeadBlock <init>(Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.PoweredRailBlock <init>(Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.PressurePlateBlock <init>(Lnet/minecraft/world/level/block/state/properties/BlockSetType;Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.PumpkinBlock <init>(Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.RailBlock <init>(Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.RedstoneTorchBlock <init>(Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.RedstoneWallTorchBlock <init>(Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.RepeaterBlock <init>(Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.RootsBlock <init>(Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.SaplingBlock <init>(Lnet/minecraft/world/level/block/grower/TreeGrower;Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.ScaffoldingBlock <init>(Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.SeaPickleBlock <init>(Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.SeagrassBlock <init>(Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.SkullBlock <init>(Lnet/minecraft/world/level/block/SkullBlock$Type;Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.SmithingTableBlock <init>(Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.SmokerBlock <init>(Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.SnowLayerBlock <init>(Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.SnowyDirtBlock <init>(Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.SpawnerBlock <init>(Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.SpongeBlock <init>(Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.StairBlock <init>(Lnet/minecraft/world/level/block/state/BlockState;Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.StemBlock <init>(Lnet/minecraft/resources/ResourceKey;Lnet/minecraft/resources/ResourceKey;Lnet/minecraft/resources/ResourceKey;Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.StructureBlock <init>(Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.StructureVoidBlock <init>(Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.SugarCaneBlock <init>(Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.TallGrassBlock <init>(Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.TorchBlock <init>(Lnet/minecraft/core/particles/SimpleParticleType;Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.TransparentBlock <init>(Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.TrapDoorBlock <init>(Lnet/minecraft/world/level/block/state/properties/BlockSetType;Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.WallSkullBlock <init>(Lnet/minecraft/world/level/block/SkullBlock$Type;Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.WallTorchBlock <init>(Lnet/minecraft/core/particles/SimpleParticleType;Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.WaterlilyBlock <init>(Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.WaterloggedTransparentBlock <init>(Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.WeatheringCopperDoorBlock <init>(Lnet/minecraft/world/level/block/state/properties/BlockSetType;Lnet/minecraft/world/level/block/WeatheringCopper$WeatherState;Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.WeatheringCopperGrateBlock <init>(Lnet/minecraft/world/level/block/WeatheringCopper$WeatherState;Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.WeatheringCopperTrapDoorBlock <init>(Lnet/minecraft/world/level/block/state/properties/BlockSetType;Lnet/minecraft/world/level/block/WeatheringCopper$WeatherState;Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.WeightedPressurePlateBlock <init>(ILnet/minecraft/world/level/block/state/properties/BlockSetType;Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.WetSpongeBlock <init>(Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.WitherSkullBlock <init>(Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.WitherWallSkullBlock <init>(Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
public net.minecraft.world.level.block.WoolCarpetBlock <init>(Lnet/minecraft/world/item/DyeColor;Lnet/minecraft/world/level/block/state/BlockBehaviour$Properties;)V # constructor
#endgroup
public net.minecraft.world.level.block.Block m_49805_(Lnet/minecraft/server/level/ServerLevel;Lnet/minecraft/core/BlockPos;I)V # popExperience
public net.minecraft.world.level.block.FireBlock m_221164_(Lnet/minecraft/world/level/block/state/BlockState;)I # getBurnOdds
public net.minecraft.world.level.block.FireBlock m_221166_(Lnet/minecraft/world/level/block/state/BlockState;)I # getIgniteOdds
public net.minecraft.world.level.block.entity.BlockEntityType$BlockEntitySupplier
public net.minecraft.world.level.block.entity.HopperBlockEntity m_59395_(I)V # setCooldown
public net.minecraft.world.level.block.entity.HopperBlockEntity m_59409_()Z # isOnCustomCooldown
public net.minecraft.world.level.block.state.properties.BlockSetType m_272115_(Lnet/minecraft/world/level/block/state/properties/BlockSetType;)Lnet/minecraft/world/level/block/state/properties/BlockSetType; # register
public net.minecraft.world.level.block.state.properties.WoodType m_61844_(Lnet/minecraft/world/level/block/state/properties/WoodType;)Lnet/minecraft/world/level/block/state/properties/WoodType; # register
public net.minecraft.world.level.chunk.status.ChunkStatus <init>(Lnet/minecraft/world/level/chunk/status/ChunkStatus;Ljava/util/EnumSet;Lnet/minecraft/world/level/chunk/status/ChunkType;)V # constructor
protected net.minecraft.world.level.levelgen.Aquifer$NoiseBasedAquifer f_157994_ # barrierNoise
protected net.minecraft.world.level.levelgen.Aquifer$NoiseBasedAquifer f_157996_ # lavaNoise
protected net.minecraft.world.level.levelgen.Aquifer$NoiseBasedAquifer f_157998_ # aquiferCache
protected net.minecraft.world.level.levelgen.Aquifer$NoiseBasedAquifer f_157999_ # aquiferLocationCache
protected net.minecraft.world.level.levelgen.Aquifer$NoiseBasedAquifer f_158000_ # shouldScheduleFluidUpdate
protected net.minecraft.world.level.levelgen.Aquifer$NoiseBasedAquifer f_158002_ # minGridX
protected net.minecraft.world.level.levelgen.Aquifer$NoiseBasedAquifer f_158003_ # minGridY
protected net.minecraft.world.level.levelgen.Aquifer$NoiseBasedAquifer f_158004_ # minGridZ
protected net.minecraft.world.level.levelgen.Aquifer$NoiseBasedAquifer f_158005_ # gridSizeX
protected net.minecraft.world.level.levelgen.Aquifer$NoiseBasedAquifer f_158006_ # gridSizeZ
protected net.minecraft.world.level.levelgen.Aquifer$NoiseBasedAquifer m_158024_(II)D # similarity
protected net.minecraft.world.level.levelgen.Aquifer$NoiseBasedAquifer m_158027_(III)I # getIndex
protected net.minecraft.world.level.levelgen.Aquifer$NoiseBasedAquifer m_158039_(I)I # gridX
protected net.minecraft.world.level.levelgen.Aquifer$NoiseBasedAquifer m_158045_(I)I # gridY
protected net.minecraft.world.level.levelgen.Aquifer$NoiseBasedAquifer m_158047_(I)I # gridZ
protected net.minecraft.world.level.levelgen.Beardifier f_158065_ # pieceIterator
protected net.minecraft.world.level.levelgen.Beardifier f_158066_ # junctionIterator
protected net.minecraft.world.level.levelgen.Beardifier m_223925_(IIII)D # getBeardContribution
private-f net.minecraft.world.level.levelgen.DebugLevelSource f_64114_ # ALL_BLOCKS
private-f net.minecraft.world.level.levelgen.DebugLevelSource f_64115_ # GRID_WIDTH
private-f net.minecraft.world.level.levelgen.DebugLevelSource f_64116_ # GRID_HEIGHT
public-f net.minecraft.world.level.levelgen.NoiseBasedChunkGenerator
protected net.minecraft.world.level.levelgen.NoiseBasedChunkGenerator m_224239_(Lnet/minecraft/world/level/LevelHeightAccessor;Lnet/minecraft/world/level/levelgen/RandomState;IILorg/apache/commons/lang3/mutable/MutableObject;Ljava/util/function/Predicate;)Ljava/util/OptionalInt; # iterateNoiseColumn
#group public net.minecraft.world.level.levelgen.NoiseGeneratorSettings *()
public net.minecraft.world.level.levelgen.NoiseGeneratorSettings m_255038_(Lnet/minecraft/data/worldgen/BootstrapContext;)Lnet/minecraft/world/level/levelgen/NoiseGeneratorSettings; # caves
public net.minecraft.world.level.levelgen.NoiseGeneratorSettings m_255186_(Lnet/minecraft/data/worldgen/BootstrapContext;)Lnet/minecraft/world/level/levelgen/NoiseGeneratorSettings; # end
public net.minecraft.world.level.levelgen.NoiseGeneratorSettings m_255226_(Lnet/minecraft/data/worldgen/BootstrapContext;ZZ)Lnet/minecraft/world/level/levelgen/NoiseGeneratorSettings; # overworld
public net.minecraft.world.level.levelgen.NoiseGeneratorSettings m_255230_(Lnet/minecraft/data/worldgen/BootstrapContext;)Lnet/minecraft/world/level/levelgen/NoiseGeneratorSettings; # floatingIslands
public net.minecraft.world.level.levelgen.NoiseGeneratorSettings m_255410_(Lnet/minecraft/data/worldgen/BootstrapContext;)Lnet/minecraft/world/level/levelgen/NoiseGeneratorSettings; # nether
public net.minecraft.world.level.levelgen.NoiseGeneratorSettings m_64474_(Lcom/mojang/serialization/codecs/RecordCodecBuilder$Instance;)Lcom/mojang/datafixers/kinds/App; # lambda$static$0
#endgroup
public net.minecraft.world.level.levelgen.feature.featuresize.FeatureSizeType <init>(Lcom/mojang/serialization/MapCodec;)V # constructor
public net.minecraft.world.level.levelgen.feature.foliageplacers.FoliagePlacerType <init>(Lcom/mojang/serialization/MapCodec;)V # constructor
public net.minecraft.world.level.levelgen.feature.rootplacers.RootPlacerType <init>(Lcom/mojang/serialization/MapCodec;)V # constructor
public net.minecraft.world.level.levelgen.feature.stateproviders.BlockStateProviderType <init>(Lcom/mojang/serialization/MapCodec;)V # constructor
public net.minecraft.world.level.levelgen.feature.treedecorators.TreeDecoratorType <init>(Lcom/mojang/serialization/MapCodec;)V # constructor
public net.minecraft.world.level.levelgen.feature.trunkplacers.TrunkPlacerType <init>(Lcom/mojang/serialization/MapCodec;)V # constructor
protected net.minecraft.world.level.portal.PortalForcer f_77648_ # level
public net.minecraft.world.level.storage.LevelResource <init>(Ljava/lang/String;)V # constructor
private-f net.minecraft.world.level.storage.loot.LootPool f_79028_ # rolls
private-f net.minecraft.world.level.storage.loot.LootPool f_79029_ # bonusRolls

