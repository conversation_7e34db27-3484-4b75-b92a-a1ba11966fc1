"""
Minecraft Service - Dedicated Minecraft Server Management
This service handles all Minecraft server operations with multi-tenant support.
"""

from flask import Flask, jsonify, request
from flask_cors import CORS
import os
import json
import uuid
import docker
import sys
from datetime import datetime
import shutil
from asgiref.wsgi import WsgiToAsgi
try:
    from dateutil import parser
except ImportError:
    print("Warning: python-dateutil not available. Some date parsing features may not work.", file=sys.stderr)
    parser = None

# Import database utilities
try:
    from database import UserManager, ServerManager, LogManager, db_manager
    print("Successfully imported database utilities", file=sys.stderr)
except ImportError as e:
    print(f"Failed to import database utilities: {e}", file=sys.stderr)
    print("Database connection is required for this application to function", file=sys.stderr)
    # Raise an exception to prevent the application from starting with mocks
    raise RuntimeError(f"Database connection failed: {e}. Application cannot start without database access.")

def get_user_context():
    """Get user context from gateway headers"""
    user_id = request.headers.get('X-User-ID')
    user_email = request.headers.get('X-User-Email')
    user_context_str = request.headers.get('X-User-Context')

    user_context = {}
    if user_context_str:
        try:
            user_context = json.loads(user_context_str)
        except:
            pass

    return {
        'user_id': user_id,
        'user_email': user_email,
        'context': user_context
    }

def require_user():
    """Decorator to ensure user context is available"""
    def decorator(f):
        def wrapper(*args, **kwargs):
            user_context = get_user_context()
            if not user_context.get('user_id'):
                return jsonify({'error': 'User authentication required'}), 401
            return f(*args, **kwargs)
        wrapper.__name__ = f.__name__
        return wrapper
    return decorator

def get_current_user():
    """Get current user from database, create if doesn't exist"""
    user_context = get_user_context()
    if not user_context.get('user_id'):
        return None

    # Get or create user in database
    user = UserManager.get_or_create_user(
        firebase_uid=user_context['user_id'],
        email=user_context.get('user_email', ''),
        display_name=user_context.get('context', {}).get('display_name')
    )
    return user

app = Flask(__name__)

# Configure CORS
CORS(app, resources={
    r"/api/*": {
        "origins": "*",
        "methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
        "allow_headers": ["Content-Type", "Authorization"]
    }
})

# Initialize Docker client
docker_available = True
try:
    docker_client = docker.from_env()
    print("Docker client initialized successfully", file=sys.stderr)
except Exception as e:
    docker_available = False
    print(f"Warning: Docker is not available. Error: {e}", file=sys.stderr)

# Configuration
DATA_DIR = os.getenv('DATA_DIR', '/data')
BACKUPS_DIR = os.getenv('BACKUPS_DIR', '/backups')

# Ensure directories exist
os.makedirs(DATA_DIR, exist_ok=True)
os.makedirs(BACKUPS_DIR, exist_ok=True)

# Legacy functions for backward compatibility (now using database)
def get_servers():
    """Get servers for current user from database"""
    user = get_current_user()
    if not user:
        return []

    try:
        servers = ServerManager.get_user_servers(user['id'])
        # Convert database format to API format
        return [convert_db_server_to_api(server) for server in servers]
    except Exception as e:
        print(f"Error fetching servers: {e}", file=sys.stderr)
        return []

def convert_db_server_to_api(db_server):
    """Convert database server format to API format"""
    return {
        'id': str(db_server['id']),
        'name': db_server['name'],
        'port': db_server['port'],
        'version': db_server['version'],
        'type': db_server['server_type'],
        'memory': db_server['memory'],
        'status': db_server['status'],
        'container_id': db_server.get('container_id'),
        'backup_enabled': db_server.get('backup_enabled', False),
        'has_mod_integration': db_server.get('has_mod_integration', False),
        'mod_last_seen': db_server.get('mod_last_seen').isoformat() if db_server.get('mod_last_seen') else None,
        'created_at': db_server['created_at'].isoformat() if db_server.get('created_at') else None
    }
def create_server_id():
    """Create a unique server ID"""
    return str(uuid.uuid4())

# Health check endpoint
@app.route('/api/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'service': 'minecraft-service',
        'timestamp': datetime.now().isoformat(),
        'version': '1.0.0',
        'docker_available': docker_available
    })

# List all Minecraft servers
@app.route('/api/servers', methods=['GET'])
@require_user()
def list_servers():
    """List all Minecraft servers for the current user"""
    return jsonify(get_servers())

# Get server details
@app.route('/api/servers/<server_id>', methods=['GET'])
@require_user()
def get_server_details(server_id):
    """Get detailed information about a specific Minecraft server owned by current user"""
    user = get_current_user()
    if not user:
        return jsonify({'error': 'User authentication required'}), 401

    try:
        server = ServerManager.get_server_by_id(server_id, user['id'])
        if not server:
            return jsonify({'error': f'Server {server_id} not found'}), 404

        # Convert to API format
        api_server = convert_db_server_to_api(server)

        # Add real-time status if Docker is available
        if docker_available and server.get('container_id'):
            try:
                container = docker_client.containers.get(server['container_id'])
                api_server['runtime_status'] = container.status
                api_server['runtime_info'] = {
                    'created': container.attrs['Created'],
                    'started': container.attrs['State'].get('StartedAt'),
                    'ports': container.attrs['NetworkSettings']['Ports']
                }
            except Exception as e:
                print(f"Error getting container info: {e}", file=sys.stderr)
                api_server['runtime_status'] = 'unknown'

        return jsonify(api_server)
    except Exception as e:
        print(f"Error fetching server details: {e}", file=sys.stderr)
        return jsonify({'error': 'Failed to fetch server details'}), 500

# Create new server
@app.route('/api/servers', methods=['POST'])
@require_user()
def create_server():
    """Create a new Minecraft server for the current user"""
    user = get_current_user()
    if not user:
        return jsonify({'error': 'User authentication required'}), 401

    data = request.json

    # Validate required fields
    required_fields = ['name', 'port', 'version', 'memory']
    for field in required_fields:
        if field not in data:
            return jsonify({'error': f'Missing required field: {field}'}), 400

    # Validate EULA acceptance
    if not data.get('eulaAccepted'):
        return jsonify({'error': 'Minecraft EULA must be accepted to create a server'}), 400

    # Check if port is available for this user
    if not ServerManager.check_port_availability(data['port'], user['id']):
        return jsonify({'error': f'Port {data["port"]} is already in use by another server'}), 400

    try:
        # Prepare server config with EULA acceptance
        server_config = data.get('config', {})
        server_config['eula_accepted'] = data.get('eulaAccepted', False)
        server_config['eula_accepted_at'] = data.get('eulaAcceptedAt')

        # Create server in database
        server = ServerManager.create_server(
            owner_id=user['id'],
            name=data['name'],
            port=data['port'],
            version=data['version'],
            server_type=data.get('type', 'vanilla').lower(),  # Convert to lowercase
            memory=data['memory'],
            server_config=server_config
        )

        # Create server directory
        server_dir = os.path.join(DATA_DIR, str(server['id']))
        os.makedirs(server_dir, exist_ok=True)

        # Create eula.txt file since user has accepted EULA
        eula_file = os.path.join(server_dir, 'eula.txt')
        with open(eula_file, 'w') as f:
            f.write(f"# EULA accepted by user on {data.get('eulaAcceptedAt', datetime.now().isoformat())}\n")
            f.write("# By changing the setting below to TRUE you are indicating your agreement to our EULA (https://aka.ms/MinecraftEULA).\n")
            f.write("eula=true\n")

        # Log the action
        LogManager.log_server_action(
            server_id=str(server['id']),
            user_id=user['id'],
            action='server_created',
            details={'name': data['name'], 'port': data['port'], 'version': data['version']}
        )

        return jsonify(convert_db_server_to_api(server)), 201
    except Exception as e:
        print(f"Error creating server: {e}", file=sys.stderr)
        return jsonify({'error': 'Failed to create server'}), 500

# Start server
@app.route('/api/servers/<server_id>/start', methods=['POST'])
@require_user()
def start_server(server_id):
    """Start a Minecraft server owned by current user"""
    user = get_current_user()
    if not user:
        return jsonify({'error': 'User authentication required'}), 401

    if not docker_available:
        return jsonify({'error': 'Docker is not available'}), 500

    try:
        # Get server from database (user-filtered)
        server = ServerManager.get_server_by_id(server_id, user['id'])
        if not server:
            return jsonify({'error': f'Server {server_id} not found'}), 404

        # Create container name
        container_name = f"minecraft-server-{str(server['id'])[:8]}"
        server_dir = os.path.join(DATA_DIR, str(server['id']))

        # Start the container on a dedicated network for Minecraft servers
        container = docker_client.containers.run(
            'itzg/minecraft-server:latest',
            detach=True,
            name=container_name,
            ports={25565: server['port']},
            environment={
                'EULA': 'TRUE',
                'VERSION': server['version'],
                'MEMORY': server['memory'],
                'TYPE': server.get('server_type', 'vanilla').upper()
            },
            volumes={
                server_dir: {
                    'bind': '/data',
                    'mode': 'rw'
                }
            },
            network='minecraft-servers-network',
            restart_policy={"Name": "unless-stopped"}
        )

        # Update server status in database
        ServerManager.update_server_status(str(server['id']), 'running', container.id)

        # Log the action
        LogManager.log_server_action(
            server_id=str(server['id']),
            user_id=user['id'],
            action='server_started',
            details={'container_id': container.id}
        )

        return jsonify({
            'status': 'running',
            'message': f'Server {server["name"]} started successfully',
            'container_id': container.id
        })

    except Exception as e:
        print(f"Error starting server: {e}", file=sys.stderr)
        return jsonify({'error': str(e)}), 500

# Stop server
@app.route('/api/servers/<server_id>/stop', methods=['POST'])
@require_user()
def stop_server(server_id):
    """Stop a Minecraft server owned by current user"""
    user = get_current_user()
    if not user:
        return jsonify({'error': 'User authentication required'}), 401

    try:
        # Get server from database (user-filtered)
        server = ServerManager.get_server_by_id(server_id, user['id'])
        if not server:
            return jsonify({'error': f'Server {server_id} not found'}), 404

        if not server.get('container_id'):
            return jsonify({'error': 'Server is not running'}), 400

        if docker_available:
            container = docker_client.containers.get(server['container_id'])
            container.stop(timeout=30)
            container.remove()

        # Update server status in database
        ServerManager.update_server_status(str(server['id']), 'stopped')

        # Log the action
        LogManager.log_server_action(
            server_id=str(server['id']),
            user_id=user['id'],
            action='server_stopped',
            details={}
        )

        return jsonify({
            'status': 'stopped',
            'message': f'Server {server["name"]} stopped successfully'
        })

    except Exception as e:
        print(f"Error stopping server: {e}", file=sys.stderr)
        return jsonify({'error': str(e)}), 500

# Delete server
@app.route('/api/servers/<server_id>', methods=['DELETE'])
@require_user()
def delete_server(server_id):
    """Delete a Minecraft server owned by current user"""
    user = get_current_user()
    if not user:
        return jsonify({'error': 'User authentication required'}), 401

    try:
        # Get server from database (user-filtered)
        server = ServerManager.get_server_by_id(server_id, user['id'])
        if not server:
            return jsonify({'error': f'Server {server_id} not found'}), 404

        server_name = server['name']

        # Stop server if running
        if server.get('container_id') and docker_available:
            try:
                container = docker_client.containers.get(server['container_id'])
                container.stop(timeout=30)
                container.remove()
            except Exception as e:
                print(f"Error stopping container: {e}", file=sys.stderr)

        # Remove server directory
        server_dir = os.path.join(DATA_DIR, str(server['id']))
        if os.path.exists(server_dir):
            shutil.rmtree(server_dir)

        # Delete from database
        deleted_count = ServerManager.delete_server(str(server['id']), user['id'])
        if deleted_count == 0:
            return jsonify({'error': 'Failed to delete server from database'}), 500

        # Log the action
        LogManager.log_server_action(
            server_id=str(server['id']),
            user_id=user['id'],
            action='server_deleted',
            details={'server_name': server_name}
        )

        return jsonify({'message': f'Server {server_name} deleted successfully'})

    except Exception as e:
        print(f"Error deleting server: {e}", file=sys.stderr)
        return jsonify({'error': str(e)}), 500

# Get server logs
@app.route('/api/servers/<server_id>/logs', methods=['GET'])
@require_user()
def get_server_logs(server_id):
    """Get server logs for user's server"""
    user = get_current_user()
    if not user:
        return jsonify({'error': 'User authentication required'}), 401

    try:
        # Get server from database (user-filtered)
        server = ServerManager.get_server_by_id(server_id, user['id'])
        if not server:
            return jsonify({'error': f'Server {server_id} not found'}), 404

        if docker_available and server.get('container_id'):
            container = docker_client.containers.get(server['container_id'])
            logs = container.logs(tail=100).decode('utf-8')
            return jsonify({'logs': logs.split('\n')})
        else:
            return jsonify({'logs': ['Server not running or Docker unavailable']})
    except Exception as e:
        return jsonify({'error': f'Failed to get logs: {str(e)}'}), 500

# Send command to server
@app.route('/api/servers/<server_id>/command', methods=['POST'])
@require_user()
def send_server_command(server_id):
    """Send command to user's server"""
    user = get_current_user()
    if not user:
        return jsonify({'error': 'User authentication required'}), 401

    try:
        # Get server from database (user-filtered)
        server = ServerManager.get_server_by_id(server_id, user['id'])
        if not server:
            return jsonify({'error': f'Server {server_id} not found'}), 404

        data = request.json
        command = data.get('command')
        if not command:
            return jsonify({'error': 'Command is required'}), 400

        if docker_available and server.get('container_id'):
            try:
                container = docker_client.containers.get(server['container_id'])
                if container.status == 'running':
                    # Execute command in the container
                    result = container.exec_run(f'rcon-cli {command}')

                    response_text = result.output.decode('utf-8') if result.output else 'Command executed'

                    # Log the command
                    LogManager.log_server_action(
                        server_id=str(server['id']),
                        user_id=user['id'],
                        action='command_sent',
                        details={
                            'command': command,
                            'response': response_text,
                            'exit_code': result.exit_code
                        }
                    )

                    return jsonify({
                        'response': response_text,
                        'exit_code': result.exit_code,
                        'success': result.exit_code == 0
                    })
                else:
                    return jsonify({'error': 'Server is not running'}), 400
            except Exception as e:
                print(f"Error executing command: {e}", file=sys.stderr)
                return jsonify({'error': f'Failed to execute command: {str(e)}'}), 500
        else:
            return jsonify({'error': 'Server not running or Docker unavailable'}), 400
    except Exception as e:
        return jsonify({'error': f'Failed to send command: {str(e)}'}), 500

# Toggle server backup
@app.route('/api/servers/<server_id>/backup', methods=['POST'])
@require_user()
def toggle_server_backup(server_id):
    """Toggle server backup for user's server"""
    user = get_current_user()
    if not user:
        return jsonify({'error': 'User authentication required'}), 401

    try:
        # Get server from database (user-filtered)
        server = ServerManager.get_server_by_id(server_id, user['id'])
        if not server:
            return jsonify({'error': f'Server {server_id} not found'}), 404

        # Toggle backup status in database
        new_backup_status = not server.get('backup_enabled', False)

        # Update server config
        server_config = server.get('server_config', {})
        server_config['backup_enabled'] = new_backup_status

        # Update in database (this would need a new method in ServerManager)
        db_manager.execute_update(
            "UPDATE minecraft_servers SET server_config = %s WHERE id = %s AND owner_id = %s",
            (json.dumps(server_config), str(server['id']), user['id'])
        )

        # Log the action
        LogManager.log_server_action(
            server_id=str(server['id']),
            user_id=user['id'],
            action='backup_toggled',
            details={'backup_enabled': new_backup_status}
        )

        return jsonify({
            'backup_enabled': new_backup_status,
            'message': f'Backup {"enabled" if new_backup_status else "disabled"} for server {server["name"]}'
        })
    except Exception as e:
        return jsonify({'error': f'Failed to toggle backup: {str(e)}'}), 500

# Download server backup
@app.route('/api/servers/<server_id>/backup', methods=['GET'])
@require_user()
def download_server_backup(server_id):
    """Download server backup for user's server"""
    user = get_current_user()
    if not user:
        return jsonify({'error': 'User authentication required'}), 401

    try:
        # Get server from database (user-filtered)
        server = ServerManager.get_server_by_id(server_id, user['id'])
        if not server:
            return jsonify({'error': f'Server {server_id} not found'}), 404

        # Create backup if it doesn't exist
        server_dir = os.path.join(DATA_DIR, str(server['id']))
        backup_file = os.path.join(BACKUPS_DIR, f'{str(server["id"])}_backup.tar.gz')

        if not os.path.exists(backup_file):
            # Create backup
            import tarfile
            with tarfile.open(backup_file, 'w:gz') as tar:
                tar.add(server_dir, arcname=server['name'])

        # Log the action
        LogManager.log_server_action(
            server_id=str(server['id']),
            user_id=user['id'],
            action='backup_downloaded',
            details={'backup_file': backup_file}
        )

        from flask import send_file
        return send_file(backup_file, as_attachment=True,
                        download_name=f'{server["name"]}_backup.tar.gz')
    except Exception as e:
        return jsonify({'error': f'Failed to create/download backup: {str(e)}'}), 500

# Update server properties
@app.route('/api/servers/<server_id>/properties', methods=['PUT'])
@require_user()
def update_server_properties(server_id):
    """Update server properties for user's server"""
    user = get_current_user()
    if not user:
        return jsonify({'error': 'User authentication required'}), 401

    try:
        # Get server from database (user-filtered)
        server = ServerManager.get_server_by_id(server_id, user['id'])
        if not server:
            return jsonify({'error': f'Server {server_id} not found'}), 404

        data = request.json
        if not data:
            return jsonify({'error': 'Request body is required'}), 400

        properties = data.get('properties', {})
        memory = data.get('memory')

        # Update memory allocation if provided
        if memory:
            db_manager.execute_update(
                "UPDATE minecraft_servers SET memory = %s WHERE id = %s AND owner_id = %s",
                (memory, str(server['id']), user['id'])
            )

        # Update server properties in server_config
        server_config = server.get('server_config', {})
        if 'properties' not in server_config:
            server_config['properties'] = {}

        # Update properties
        server_config['properties'].update(properties)

        # Save updated config to database
        db_manager.execute_update(
            "UPDATE minecraft_servers SET server_config = %s WHERE id = %s AND owner_id = %s",
            (json.dumps(server_config), str(server['id']), user['id'])
        )

        # If server has a data directory, also update server.properties file
        server_dir = os.path.join(DATA_DIR, str(server['id']))
        properties_file = os.path.join(server_dir, 'server.properties')

        if os.path.exists(server_dir):
            os.makedirs(server_dir, exist_ok=True)

            # Write server.properties file
            with open(properties_file, 'w') as f:
                f.write("# Minecraft server properties\n")
                f.write(f"# Updated on {datetime.now().isoformat()}\n")
                for key, value in properties.items():
                    # Convert boolean values to lowercase strings
                    if isinstance(value, bool):
                        value = str(value).lower()
                    f.write(f"{key}={value}\n")

        # Log the action
        LogManager.log_server_action(
            server_id=str(server['id']),
            user_id=user['id'],
            action='properties_updated',
            details={'properties': properties, 'memory': memory}
        )

        return jsonify({
            'message': 'Server properties updated successfully',
            'properties': properties,
            'memory': memory
        })

    except Exception as e:
        print(f"Error updating server properties: {e}", file=sys.stderr)
        return jsonify({'error': 'Failed to update server properties'}), 500

# Get server statistics
@app.route('/api/servers/<server_id>/stats', methods=['GET'])
@require_user()
def get_server_stats(server_id):
    """Get server performance statistics"""
    user = get_current_user()
    if not user:
        return jsonify({'error': 'User authentication required'}), 401

    try:
        # Get server from database (user-filtered)
        server = ServerManager.get_server_by_id(server_id, user['id'])
        if not server:
            return jsonify({'error': f'Server {server_id} not found'}), 404

        # Get real statistics from Docker container and server files
        stats = get_real_server_stats(server)
        return jsonify({'stats': stats})

    except Exception as e:
        print(f"Error getting server stats: {e}", file=sys.stderr)
        return jsonify({'error': 'Failed to get server statistics'}), 500

def get_real_server_stats(server):
    """Get real server statistics from Docker and file system, prioritizing mod data when available"""
    stats = {
        'cpu': 0.0,
        'memory': {
            'used': 0,
            'total': 0,
            'percentage': 0.0
        },
        'disk': {
            'used': 0,
            'total': 0,
            'percentage': 0.0
        },
        'network': {
            'bytesIn': 0,
            'bytesOut': 0
        },
        'players': {
            'online': 0,
            'max': int(server.get('server_config', {}).get('properties', {}).get('max-players', 20)),
            'list': []
        },
        'tps': 0.0,
        'uptime': 0,
        'worldSize': 0,
        'data_source': 'docker_files',  # Track data source
        'has_mod_integration': server.get('has_mod_integration', False),
        'mod_last_seen': server.get('mod_last_seen')
    }

    # Get Docker container stats if available
    if docker_available and server.get('container_id'):
        try:
            container = docker_client.containers.get(server['container_id'])
            if container.status == 'running':
                # Get container stats
                container_stats = container.stats(stream=False)

                # Calculate CPU percentage
                cpu_delta = container_stats['cpu_stats']['cpu_usage']['total_usage'] - \
                           container_stats['precpu_stats']['cpu_usage']['total_usage']
                system_delta = container_stats['cpu_stats']['system_cpu_usage'] - \
                              container_stats['precpu_stats']['system_cpu_usage']

                if system_delta > 0:
                    stats['cpu'] = (cpu_delta / system_delta) * len(container_stats['cpu_stats']['cpu_usage']['percpu_usage']) * 100.0

                # Memory stats
                memory_usage = container_stats['memory_stats']['usage']
                memory_limit = container_stats['memory_stats']['limit']
                stats['memory'] = {
                    'used': memory_usage,
                    'total': memory_limit,
                    'percentage': (memory_usage / memory_limit) * 100.0 if memory_limit > 0 else 0.0
                }

                # Network stats
                networks = container_stats.get('networks', {})
                total_rx = sum(net.get('rx_bytes', 0) for net in networks.values())
                total_tx = sum(net.get('tx_bytes', 0) for net in networks.values())
                stats['network'] = {
                    'bytesIn': total_rx,
                    'bytesOut': total_tx
                }

                # Container uptime
                started_at = container.attrs['State'].get('StartedAt')
                if started_at and parser:
                    try:
                        start_time = parser.parse(started_at)
                        stats['uptime'] = int((datetime.now(start_time.tzinfo) - start_time).total_seconds())
                    except Exception as e:
                        print(f"Error parsing container start time: {e}", file=sys.stderr)

        except Exception as e:
            print(f"Error getting container stats: {e}", file=sys.stderr)

    # Get disk usage from server directory
    server_dir = os.path.join(DATA_DIR, str(server['id']))
    if os.path.exists(server_dir):
        try:
            # Calculate directory size
            total_size = 0
            for dirpath, dirnames, filenames in os.walk(server_dir):
                for filename in filenames:
                    filepath = os.path.join(dirpath, filename)
                    if os.path.exists(filepath):
                        total_size += os.path.getsize(filepath)

            stats['worldSize'] = total_size

            # Get disk usage of the data directory
            import shutil
            disk_usage = shutil.disk_usage(DATA_DIR)
            stats['disk'] = {
                'used': total_size,
                'total': disk_usage.total,
                'percentage': (total_size / disk_usage.total) * 100.0 if disk_usage.total > 0 else 0.0
            }
        except Exception as e:
            print(f"Error calculating disk usage: {e}", file=sys.stderr)

    # Get real player data
    try:
        player_data = get_real_player_data(server)
        stats['players'] = player_data
    except Exception as e:
        print(f"Error getting player data: {e}", file=sys.stderr)

    # Update data source based on mod integration
    if server.get('has_mod_integration'):
        stats['data_source'] = 'mod_integration'
        # Note: When mod is active, real-time data comes via API endpoints
        # /api/servers/<id>/events and /api/servers/<id>/players/<uuid>
        # This provides more accurate and real-time statistics

    return stats

# Get server players
@app.route('/api/servers/<server_id>/players', methods=['GET'])
@require_user()
def get_server_players(server_id):
    """Get server players list"""
    user = get_current_user()
    if not user:
        return jsonify({'error': 'User authentication required'}), 401

    try:
        # Get server from database (user-filtered)
        server = ServerManager.get_server_by_id(server_id, user['id'])
        if not server:
            return jsonify({'error': f'Server {server_id} not found'}), 404

        # Get real player data from server files
        player_data = get_real_player_data(server)
        players = player_data.get('all_players', [])
        online_players = [p for p in players if p.get('isOnline', False)]

        return jsonify({
            'players': players,
            'onlinePlayers': online_players
        })

    except Exception as e:
        print(f"Error getting server players: {e}", file=sys.stderr)
        return jsonify({'error': 'Failed to get server players'}), 500

def get_real_player_data(server):
    """Get real player data from server files and RCON"""
    server_dir = os.path.join(DATA_DIR, str(server['id']))

    # Initialize player data structure
    player_data = {
        'online': 0,
        'max': int(server.get('server_config', {}).get('properties', {}).get('max-players', 20)),
        'list': [],
        'all_players': []
    }

    # Dictionary to store all known players
    all_players = {}

    try:
        # Read usercache.json for player UUIDs and usernames
        usercache_file = os.path.join(server_dir, 'usercache.json')
        if os.path.exists(usercache_file):
            with open(usercache_file, 'r') as f:
                usercache = json.load(f)
                for entry in usercache:
                    uuid = entry.get('uuid')
                    username = entry.get('name')
                    if uuid and username:
                        all_players[uuid] = {
                            'uuid': uuid,
                            'username': username,
                            'lastSeen': entry.get('expiresOn', ''),
                            'isOnline': False,
                            'isOp': False,
                            'isBanned': False,
                            'isWhitelisted': False
                        }
    except Exception as e:
        print(f"Error reading usercache.json: {e}", file=sys.stderr)

    try:
        # Read ops.json for operator status
        ops_file = os.path.join(server_dir, 'ops.json')
        if os.path.exists(ops_file):
            with open(ops_file, 'r') as f:
                ops = json.load(f)
                for op in ops:
                    uuid = op.get('uuid')
                    if uuid in all_players:
                        all_players[uuid]['isOp'] = True
    except Exception as e:
        print(f"Error reading ops.json: {e}", file=sys.stderr)

    try:
        # Read banned-players.json for ban status
        banned_file = os.path.join(server_dir, 'banned-players.json')
        if os.path.exists(banned_file):
            with open(banned_file, 'r') as f:
                banned = json.load(f)
                for ban in banned:
                    uuid = ban.get('uuid')
                    if uuid in all_players:
                        all_players[uuid]['isBanned'] = True
    except Exception as e:
        print(f"Error reading banned-players.json: {e}", file=sys.stderr)

    try:
        # Read whitelist.json for whitelist status
        whitelist_file = os.path.join(server_dir, 'whitelist.json')
        if os.path.exists(whitelist_file):
            with open(whitelist_file, 'r') as f:
                whitelist = json.load(f)
                for entry in whitelist:
                    uuid = entry.get('uuid')
                    if uuid in all_players:
                        all_players[uuid]['isWhitelisted'] = True
    except Exception as e:
        print(f"Error reading whitelist.json: {e}", file=sys.stderr)

    # Get online players via RCON if server is running
    if docker_available and server.get('container_id'):
        try:
            container = docker_client.containers.get(server['container_id'])
            if container.status == 'running':
                # Try to get online players via RCON
                result = container.exec_run('rcon-cli list')
                if result.exit_code == 0:
                    output = result.output.decode('utf-8').strip()
                    # Parse the output to get online players
                    # Format is usually: "There are X of a max of Y players online: player1, player2"
                    if 'players online:' in output:
                        parts = output.split('players online:')
                        if len(parts) > 1:
                            online_names = [name.strip() for name in parts[1].split(',') if name.strip()]
                            player_data['list'] = online_names
                            player_data['online'] = len(online_names)

                            # Mark players as online
                            for player in all_players.values():
                                if player['username'] in online_names:
                                    player['isOnline'] = True
        except Exception as e:
            print(f"Error getting online players via RCON: {e}", file=sys.stderr)

    # Convert to list format
    player_data['all_players'] = list(all_players.values())

    return player_data

# Execute player action
@app.route('/api/servers/<server_id>/players/<username>/action', methods=['POST'])
@require_user()
def execute_player_action(server_id, username):
    """Execute action on player (kick, ban, op, etc.)"""
    user = get_current_user()
    if not user:
        return jsonify({'error': 'User authentication required'}), 401

    try:
        # Get server from database (user-filtered)
        server = ServerManager.get_server_by_id(server_id, user['id'])
        if not server:
            return jsonify({'error': f'Server {server_id} not found'}), 404

        data = request.json
        action_type = data.get('type')
        reason = data.get('reason', 'No reason provided')

        if not action_type:
            return jsonify({'error': 'Action type is required'}), 400

        # Map action types to actual Minecraft commands
        command_map = {
            'kick': f'kick {username} {reason}',
            'ban': f'ban {username} {reason}',
            'unban': f'pardon {username}',
            'op': f'op {username}',
            'deop': f'deop {username}',
            'whitelist': f'whitelist add {username}',
            'unwhitelist': f'whitelist remove {username}'
        }

        command = command_map.get(action_type)
        if not command:
            return jsonify({'error': f'Invalid action type: {action_type}'}), 400

        # Execute the command via RCON if server is running
        command_result = None
        if docker_available and server.get('container_id'):
            try:
                container = docker_client.containers.get(server['container_id'])
                if container.status == 'running':
                    result = container.exec_run(f'rcon-cli {command}')
                    command_result = {
                        'output': result.output.decode('utf-8') if result.output else 'Command executed',
                        'exit_code': result.exit_code,
                        'success': result.exit_code == 0
                    }
                else:
                    return jsonify({'error': 'Server is not running'}), 400
            except Exception as e:
                print(f"Error executing RCON command: {e}", file=sys.stderr)
                return jsonify({'error': f'Failed to execute command: {str(e)}'}), 500
        else:
            return jsonify({'error': 'Server not available or Docker unavailable'}), 400

        # Log the action
        LogManager.log_server_action(
            server_id=str(server['id']),
            user_id=user['id'],
            action=f'player_{action_type}',
            details={
                'username': username,
                'reason': reason,
                'command': command,
                'result': command_result
            }
        )

        return jsonify({
            'message': f'Action {action_type} executed successfully for {username}',
            'command': command,
            'result': command_result
        })

    except Exception as e:
        print(f"Error executing player action: {e}", file=sys.stderr)
        return jsonify({'error': 'Failed to execute player action'}), 500

# Get server backups
@app.route('/api/servers/<server_id>/backups', methods=['GET'])
@require_user()
def get_server_backups(server_id):
    """Get list of server backups"""
    user = get_current_user()
    if not user:
        return jsonify({'error': 'User authentication required'}), 401

    try:
        # Get server from database (user-filtered)
        server = ServerManager.get_server_by_id(server_id, user['id'])
        if not server:
            return jsonify({'error': f'Server {server_id} not found'}), 404

        # Scan real backup directory for backup files
        backups = get_real_server_backups(server)
        return jsonify({'backups': backups})

    except Exception as e:
        print(f"Error getting server backups: {e}", file=sys.stderr)
        return jsonify({'error': 'Failed to get server backups'}), 500

def get_real_server_backups(server):
    """Scan backup directory for real backup files"""
    backups = []
    server_id = str(server['id'])

    try:
        # Ensure backup directory exists
        os.makedirs(BACKUPS_DIR, exist_ok=True)

        # Look for backup files matching this server
        for filename in os.listdir(BACKUPS_DIR):
            if filename.startswith(server_id) and filename.endswith('.tar.gz'):
                backup_path = os.path.join(BACKUPS_DIR, filename)

                if os.path.isfile(backup_path):
                    # Get file stats
                    stat = os.stat(backup_path)

                    # Extract backup ID from filename
                    # Expected format: {server_id}_{backup_id}.tar.gz
                    backup_id = filename.replace(f'{server_id}_', '').replace('.tar.gz', '')

                    # Create backup entry
                    backup = {
                        'id': backup_id,
                        'name': filename,
                        'size': stat.st_size,
                        'created': datetime.fromtimestamp(stat.st_ctime).isoformat() + 'Z',
                        'modified': datetime.fromtimestamp(stat.st_mtime).isoformat() + 'Z',
                        'path': backup_path
                    }
                    backups.append(backup)

        # Sort backups by creation time (newest first)
        backups.sort(key=lambda x: x['created'], reverse=True)

    except Exception as e:
        print(f"Error scanning backup directory: {e}", file=sys.stderr)

    return backups

# Create manual backup
@app.route('/api/servers/<server_id>/backup', methods=['POST'])
@require_user()
def create_manual_backup(server_id):
    """Create a manual backup of the server"""
    user = get_current_user()
    if not user:
        return jsonify({'error': 'User authentication required'}), 401

    try:
        # Get server from database (user-filtered)
        server = ServerManager.get_server_by_id(server_id, user['id'])
        if not server:
            return jsonify({'error': f'Server {server_id} not found'}), 404

        # Create actual backup directory and file
        server_dir = os.path.join(DATA_DIR, str(server['id']))
        backup_id = f"manual_{int(datetime.now().timestamp())}"
        backup_file = os.path.join(BACKUPS_DIR, f'{str(server["id"])}_{backup_id}.tar.gz')

        # Ensure backup directory exists
        os.makedirs(BACKUPS_DIR, exist_ok=True)

        # Create backup if server directory exists
        if os.path.exists(server_dir):
            import tarfile
            with tarfile.open(backup_file, 'w:gz') as tar:
                tar.add(server_dir, arcname=server['name'])

        # Log the action
        LogManager.log_server_action(
            server_id=str(server['id']),
            user_id=user['id'],
            action='manual_backup_created',
            details={'backup_id': backup_id, 'backup_file': backup_file}
        )

        return jsonify({
            'message': 'Manual backup created successfully',
            'backup_id': backup_id
        })

    except Exception as e:
        print(f"Error creating manual backup: {e}", file=sys.stderr)
        return jsonify({'error': 'Failed to create manual backup'}), 500

# Get backup schedule
@app.route('/api/servers/<server_id>/backup-schedule', methods=['GET'])
@require_user()
def get_backup_schedule(server_id):
    """Get backup schedule for a server"""
    user = get_current_user()
    if not user:
        return jsonify({'error': 'User authentication required'}), 401

    try:
        # Get server from database (user-filtered)
        server = ServerManager.get_server_by_id(server_id, user['id'])
        if not server:
            return jsonify({'error': f'Server {server_id} not found'}), 404

        # Get real backup schedule from server config
        server_config = server.get('server_config', {})
        backup_schedule = server_config.get('backup_schedule', {})

        # Default schedule if none exists
        schedule = {
            'enabled': backup_schedule.get('enabled', False),
            'frequency': backup_schedule.get('frequency', 'daily'),
            'time': backup_schedule.get('time', '02:00'),
            'dayOfWeek': backup_schedule.get('dayOfWeek', 0),
            'maxBackups': backup_schedule.get('maxBackups', 7)
        }

        return jsonify({'schedule': schedule})

    except Exception as e:
        print(f"Error getting backup schedule: {e}", file=sys.stderr)
        return jsonify({'error': 'Failed to get backup schedule'}), 500

# Update backup schedule
@app.route('/api/servers/<server_id>/backup-schedule', methods=['PUT'])
@require_user()
def update_backup_schedule(server_id):
    """Update backup schedule for a server"""
    user = get_current_user()
    if not user:
        return jsonify({'error': 'User authentication required'}), 401

    try:
        # Get server from database (user-filtered)
        server = ServerManager.get_server_by_id(server_id, user['id'])
        if not server:
            return jsonify({'error': f'Server {server_id} not found'}), 404

        data = request.get_json()
        schedule = data.get('schedule', {})

        # Validate schedule data
        valid_frequencies = ['daily', 'weekly', 'monthly']
        if schedule.get('frequency') not in valid_frequencies:
            return jsonify({'error': 'Invalid frequency. Must be daily, weekly, or monthly'}), 400

        # Update server config with new backup schedule
        server_config = server.get('server_config', {})
        server_config['backup_schedule'] = schedule

        # Save updated config to database
        db_manager.execute_update(
            "UPDATE minecraft_servers SET server_config = %s WHERE id = %s AND owner_id = %s",
            (json.dumps(server_config), str(server['id']), user['id'])
        )

        # Log the action
        LogManager.log_server_action(
            server_id=str(server['id']),
            user_id=user['id'],
            action='backup_schedule_updated',
            details={'schedule': schedule}
        )

        return jsonify({
            'message': 'Backup schedule updated successfully',
            'schedule': schedule
        })

    except Exception as e:
        print(f"Error updating backup schedule: {e}", file=sys.stderr)
        return jsonify({'error': 'Failed to update backup schedule'}), 500

# Delete backup
@app.route('/api/servers/<server_id>/backups/<backup_id>', methods=['DELETE'])
@require_user()
def delete_backup(server_id, backup_id):
    """Delete a specific backup"""
    user = get_current_user()
    if not user:
        return jsonify({'error': 'User authentication required'}), 401

    try:
        # Get server from database (user-filtered)
        server = ServerManager.get_server_by_id(server_id, user['id'])
        if not server:
            return jsonify({'error': f'Server {server_id} not found'}), 404

        # In a real implementation, this would delete the actual backup file
        backup_file = os.path.join(BACKUPS_DIR, f'{str(server["id"])}_{backup_id}.tar.gz')
        if os.path.exists(backup_file):
            os.remove(backup_file)

        # Log the action
        LogManager.log_server_action(
            server_id=str(server['id']),
            user_id=user['id'],
            action='backup_deleted',
            details={'backup_id': backup_id}
        )

        return jsonify({'message': 'Backup deleted successfully'})

    except Exception as e:
        print(f"Error deleting backup: {e}", file=sys.stderr)
        return jsonify({'error': 'Failed to delete backup'}), 500

# Download specific backup
@app.route('/api/servers/<server_id>/backups/<backup_id>/download', methods=['GET'])
@require_user()
def download_specific_backup(server_id, backup_id):
    """Download a specific backup"""
    user = get_current_user()
    if not user:
        return jsonify({'error': 'User authentication required'}), 401

    try:
        # Get server from database (user-filtered)
        server = ServerManager.get_server_by_id(server_id, user['id'])
        if not server:
            return jsonify({'error': f'Server {server_id} not found'}), 404

        # Find backup file
        backup_file = os.path.join(BACKUPS_DIR, f'{str(server["id"])}_{backup_id}.tar.gz')
        if not os.path.exists(backup_file):
            return jsonify({'error': 'Backup file not found'}), 404

        # Log the action
        LogManager.log_server_action(
            server_id=str(server['id']),
            user_id=user['id'],
            action='backup_downloaded',
            details={'backup_id': backup_id, 'backup_file': backup_file}
        )

        from flask import send_file
        return send_file(backup_file, as_attachment=True,
                        download_name=f'{server["name"]}_backup_{backup_id}.tar.gz')

    except Exception as e:
        print(f"Error downloading backup: {e}", file=sys.stderr)
        return jsonify({'error': 'Failed to download backup'}), 500

# Get server files
@app.route('/api/servers/<server_id>/files', methods=['GET'])
@require_user()
def get_server_files(server_id):
    """Get server files and directories"""
    user = get_current_user()
    if not user:
        return jsonify({'error': 'User authentication required'}), 401

    try:
        # Get server from database (user-filtered)
        server = ServerManager.get_server_by_id(server_id, user['id'])
        if not server:
            return jsonify({'error': f'Server {server_id} not found'}), 404

        path = request.args.get('path', '/')
        server_dir = os.path.join(DATA_DIR, str(server['id']))

        # Ensure the path is within the server directory (security)
        full_path = os.path.normpath(os.path.join(server_dir, path.lstrip('/')))
        if not full_path.startswith(server_dir):
            return jsonify({'error': 'Invalid path'}), 400

        files = []
        if os.path.exists(full_path) and os.path.isdir(full_path):
            for item in os.listdir(full_path):
                item_path = os.path.join(full_path, item)
                relative_path = os.path.relpath(item_path, server_dir)

                stat = os.stat(item_path)
                files.append({
                    'name': item,
                    'type': 'directory' if os.path.isdir(item_path) else 'file',
                    'size': stat.st_size if os.path.isfile(item_path) else 0,
                    'modified': datetime.fromtimestamp(stat.st_mtime).isoformat(),
                    'path': '/' + relative_path.replace('\\', '/'),
                    'permissions': oct(stat.st_mode)[-3:]
                })

        # Sort: directories first, then files, both alphabetically
        files.sort(key=lambda x: (x['type'] == 'file', x['name'].lower()))

        return jsonify({'files': files})

    except Exception as e:
        print(f"Error getting server files: {e}", file=sys.stderr)
        return jsonify({'error': 'Failed to get server files'}), 500

# Upload file to server
@app.route('/api/servers/<server_id>/files/upload', methods=['POST'])
@require_user()
def upload_server_file(server_id):
    """Upload a file to the server"""
    user = get_current_user()
    if not user:
        return jsonify({'error': 'User authentication required'}), 401

    try:
        # Get server from database (user-filtered)
        server = ServerManager.get_server_by_id(server_id, user['id'])
        if not server:
            return jsonify({'error': f'Server {server_id} not found'}), 404

        if 'file' not in request.files:
            return jsonify({'error': 'No file provided'}), 400

        file = request.files['file']
        path = request.form.get('path', '/')

        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400

        server_dir = os.path.join(DATA_DIR, str(server['id']))
        full_path = os.path.normpath(os.path.join(server_dir, path.lstrip('/'), file.filename))

        # Ensure the path is within the server directory (security)
        if not full_path.startswith(server_dir):
            return jsonify({'error': 'Invalid path'}), 400

        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(full_path), exist_ok=True)

        # Save the file
        file.save(full_path)

        # Log the action
        LogManager.log_server_action(
            server_id=str(server['id']),
            user_id=user['id'],
            action='file_uploaded',
            details={'filename': file.filename, 'path': path}
        )

        return jsonify({'message': f'File {file.filename} uploaded successfully'})

    except Exception as e:
        print(f"Error uploading file: {e}", file=sys.stderr)
        return jsonify({'error': 'Failed to upload file'}), 500

# Create folder in server
@app.route('/api/servers/<server_id>/files/folder', methods=['POST'])
@require_user()
def create_server_folder(server_id):
    """Create a folder in the server directory"""
    user = get_current_user()
    if not user:
        return jsonify({'error': 'User authentication required'}), 401

    try:
        # Get server from database (user-filtered)
        server = ServerManager.get_server_by_id(server_id, user['id'])
        if not server:
            return jsonify({'error': f'Server {server_id} not found'}), 404

        data = request.get_json()
        path = data.get('path', '/')
        name = data.get('name', '').strip()

        if not name:
            return jsonify({'error': 'Folder name is required'}), 400

        server_dir = os.path.join(DATA_DIR, str(server['id']))
        full_path = os.path.normpath(os.path.join(server_dir, path.lstrip('/'), name))

        # Ensure the path is within the server directory (security)
        if not full_path.startswith(server_dir):
            return jsonify({'error': 'Invalid path'}), 400

        # Create the folder
        os.makedirs(full_path, exist_ok=True)

        # Log the action
        LogManager.log_server_action(
            server_id=str(server['id']),
            user_id=user['id'],
            action='folder_created',
            details={'folder_name': name, 'path': path}
        )

        return jsonify({'message': f'Folder {name} created successfully'})

    except Exception as e:
        print(f"Error creating folder: {e}", file=sys.stderr)
        return jsonify({'error': 'Failed to create folder'}), 500

# Handle server events from Fabric/Forge mod
@app.route('/api/servers/<server_id>/events', methods=['POST'])
@require_user()
def handle_server_event(server_id):
    """Handle events from the Fabric/Forge mod"""
    user = get_current_user()
    if not user:
        return jsonify({'error': 'User authentication required'}), 401

    try:
        # Get server from database (user-filtered)
        server = ServerManager.get_server_by_id(server_id, user['id'])
        if not server:
            return jsonify({'error': f'Server {server_id} not found'}), 404

        data = request.get_json()
        event_type = data.get('eventType')
        player_uuid = data.get('playerUuid')
        event_data = data.get('data', {})

        # Mark server as having mod integration
        ServerManager.update_server_mod_status(str(server['id']), True)

        # Log the event
        LogManager.log_server_action(
            server_id=str(server['id']),
            user_id=user['id'],
            action=f'mod_event_{event_type}',
            details={
                'event_type': event_type,
                'player_uuid': player_uuid,
                'event_data': event_data,
                'timestamp': data.get('timestamp'),
                'mod_type': 'fabric_or_forge'
            }
        )

        return jsonify({'message': 'Event received successfully'})

    except Exception as e:
        print(f"Error handling server event: {e}", file=sys.stderr)
        return jsonify({'error': 'Failed to handle server event'}), 500

# Update player data from Fabric/Forge mod
@app.route('/api/servers/<server_id>/players/<player_uuid>', methods=['PUT'])
@require_user()
def update_player_data(server_id, player_uuid):
    """Update player data from the Fabric/Forge mod"""
    user = get_current_user()
    if not user:
        return jsonify({'error': 'User authentication required'}), 401

    try:
        # Get server from database (user-filtered)
        server = ServerManager.get_server_by_id(server_id, user['id'])
        if not server:
            return jsonify({'error': f'Server {server_id} not found'}), 404

        data = request.get_json()

        # Mark server as having mod integration
        ServerManager.update_server_mod_status(str(server['id']), True)

        # Store player data in database (you might want to create a player data table)
        # For now, just log it
        LogManager.log_server_action(
            server_id=str(server['id']),
            user_id=user['id'],
            action='mod_player_update',
            details={
                'player_uuid': player_uuid,
                'player_data': data,
                'mod_type': 'fabric_or_forge'
            }
        )

        return jsonify({'message': 'Player data updated successfully'})

    except Exception as e:
        print(f"Error updating player data: {e}", file=sys.stderr)
        return jsonify({'error': 'Failed to update player data'}), 500

# Create ASGI application for uvicorn
asgi_app = WsgiToAsgi(app)

# Make the app directly importable for ASGI servers
#app = asgi_app  # This ensures compatibility with both WSGI and ASGI servers

if __name__ == '__main__':
    port = int(os.getenv('PORT', 5001))
    host = os.getenv('HOST', '0.0.0.0')
    debug = os.getenv('DEBUG', 'False').lower() == 'true'

    print(f"Starting Minecraft Service on {host}:{port}", file=sys.stderr)
    app.run(host=host, port=port, debug=debug)
