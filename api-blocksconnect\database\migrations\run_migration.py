#!/usr/bin/env python3
"""
Database Migration Runner for BlocksConnect
Runs the mod integration columns migration safely
"""

import os
import sys
import psycopg2
from psycopg2.extras import RealDictCursor
from pathlib import Path

def get_database_url():
    """Get database URL from environment or prompt user"""
    db_url = os.getenv('DATABASE_URL')
    
    if not db_url:
        print("DATABASE_URL not found in environment variables.")
        print("Please provide database connection details:")
        
        host = input("Database host (default: localhost): ").strip() or "localhost"
        port = input("Database port (default: 5432): ").strip() or "5432"
        database = input("Database name (default: blocksconnect): ").strip() or "blocksconnect"
        username = input("Database username (default: blocksconnect): ").strip() or "blocksconnect"
        password = input("Database password: ").strip()
        
        if not password:
            print("Error: Password is required")
            sys.exit(1)
        
        db_url = f"postgresql://{username}:{password}@{host}:{port}/{database}"
    
    return db_url

def test_connection(db_url):
    """Test database connection"""
    try:
        conn = psycopg2.connect(db_url)
        cursor = conn.cursor()
        cursor.execute("SELECT version();")
        version = cursor.fetchone()[0]
        print(f"✅ Database connection successful!")
        print(f"PostgreSQL version: {version}")
        cursor.close()
        conn.close()
        return True
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

def check_table_exists(db_url):
    """Check if minecraft_servers table exists"""
    try:
        conn = psycopg2.connect(db_url)
        cursor = conn.cursor()
        cursor.execute("""
            SELECT EXISTS (
                SELECT 1 FROM information_schema.tables 
                WHERE table_name = 'minecraft_servers'
            );
        """)
        exists = cursor.fetchone()[0]
        cursor.close()
        conn.close()
        return exists
    except Exception as e:
        print(f"Error checking table existence: {e}")
        return False

def check_columns_exist(db_url):
    """Check if mod integration columns already exist"""
    try:
        conn = psycopg2.connect(db_url)
        cursor = conn.cursor(cursor_factory=RealDictCursor)
        cursor.execute("""
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'minecraft_servers' 
            AND column_name IN ('has_mod_integration', 'mod_last_seen');
        """)
        existing_columns = [row['column_name'] for row in cursor.fetchall()]
        cursor.close()
        conn.close()
        return existing_columns
    except Exception as e:
        print(f"Error checking columns: {e}")
        return []

def run_migration(db_url):
    """Run the migration script"""
    migration_file = Path(__file__).parent / "add_mod_integration_columns.sql"
    
    if not migration_file.exists():
        print(f"❌ Migration file not found: {migration_file}")
        return False
    
    try:
        # Read migration SQL
        with open(migration_file, 'r') as f:
            migration_sql = f.read()
        
        # Execute migration
        conn = psycopg2.connect(db_url)
        cursor = conn.cursor()
        
        print("🔄 Running migration...")
        cursor.execute(migration_sql)
        conn.commit()
        
        print("✅ Migration completed successfully!")
        
        cursor.close()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        return False

def verify_migration(db_url):
    """Verify that the migration was successful"""
    try:
        conn = psycopg2.connect(db_url)
        cursor = conn.cursor(cursor_factory=RealDictCursor)
        
        # Check columns exist
        cursor.execute("""
            SELECT column_name, data_type, is_nullable, column_default
            FROM information_schema.columns 
            WHERE table_name = 'minecraft_servers' 
            AND column_name IN ('has_mod_integration', 'mod_last_seen')
            ORDER BY column_name;
        """)
        columns = cursor.fetchall()
        
        # Check indexes exist
        cursor.execute("""
            SELECT indexname 
            FROM pg_indexes 
            WHERE tablename = 'minecraft_servers' 
            AND indexname LIKE '%mod%';
        """)
        indexes = cursor.fetchall()
        
        cursor.close()
        conn.close()
        
        print("\n📊 Migration Verification:")
        print("=" * 50)
        
        if columns:
            print("✅ Columns added:")
            for col in columns:
                print(f"  - {col['column_name']} ({col['data_type']}, nullable: {col['is_nullable']}, default: {col['column_default']})")
        else:
            print("❌ No mod integration columns found")
            return False
        
        if indexes:
            print("✅ Indexes created:")
            for idx in indexes:
                print(f"  - {idx['indexname']}")
        else:
            print("⚠️  No mod-related indexes found")
        
        return True
        
    except Exception as e:
        print(f"❌ Verification failed: {e}")
        return False

def main():
    """Main migration runner"""
    print("🚀 BlocksConnect Database Migration Runner")
    print("=" * 50)
    print("This script will add mod integration tracking columns to the minecraft_servers table.")
    print()
    
    # Get database URL
    db_url = get_database_url()
    
    # Test connection
    print("\n🔍 Testing database connection...")
    if not test_connection(db_url):
        print("Please check your database connection details and try again.")
        sys.exit(1)
    
    # Check if table exists
    print("\n🔍 Checking if minecraft_servers table exists...")
    if not check_table_exists(db_url):
        print("❌ minecraft_servers table not found!")
        print("Please ensure the database is properly initialized.")
        sys.exit(1)
    
    print("✅ minecraft_servers table found")
    
    # Check existing columns
    print("\n🔍 Checking existing columns...")
    existing_columns = check_columns_exist(db_url)
    
    if existing_columns:
        print(f"⚠️  Some mod integration columns already exist: {existing_columns}")
        response = input("Do you want to continue anyway? (y/N): ").strip().lower()
        if response != 'y':
            print("Migration cancelled.")
            sys.exit(0)
    else:
        print("✅ No mod integration columns found - migration needed")
    
    # Confirm migration
    print("\n🚨 Ready to run migration!")
    print("This will add the following columns to minecraft_servers:")
    print("  - has_mod_integration (BOOLEAN, default FALSE)")
    print("  - mod_last_seen (TIMESTAMP WITH TIME ZONE, nullable)")
    print("And create indexes for better performance.")
    print()
    
    response = input("Do you want to proceed? (y/N): ").strip().lower()
    if response != 'y':
        print("Migration cancelled.")
        sys.exit(0)
    
    # Run migration
    if run_migration(db_url):
        # Verify migration
        print("\n🔍 Verifying migration...")
        if verify_migration(db_url):
            print("\n🎉 Migration completed successfully!")
            print("Your database is now ready for mod integration features.")
        else:
            print("\n⚠️  Migration may have issues. Please check manually.")
    else:
        print("\n❌ Migration failed. Please check the error messages above.")
        sys.exit(1)

if __name__ == "__main__":
    main()
