# Build Troubleshooting Guide

This guide helps resolve common build issues with the BlocksConnect Forge mod.

## Quick Fix Summary

The build has been updated to use more stable versions:
- **Minecraft**: 1.20.1 (instead of 1.21.1)
- **Forge**: 47.2.0 (instead of 52.0.17)
- **ForgeGradle**: 5.1.+ (instead of 6.0.24)
- **Removed**: Parchment plugin (was causing dependency issues)

## Build Steps

### 1. Initialize Gradle Wrapper
```bash
# Windows
init-gradle-wrapper.bat

# Linux/Mac
chmod +x init-gradle-wrapper.sh
./init-gradle-wrapper.sh
```

### 2. Build the Mod
```bash
# Windows
gradlew.bat build

# Linux/Mac
./gradlew build
```

## Common Issues & Solutions

### Issue 1: "Could not find org.parchmentmc:librarian-forgegradle"
**Solution**: ✅ Fixed - Removed Parchment plugin from build.gradle

### Issue 2: "GradleWrapperMain not found"
**Solution**: Run the initialization script first:
```bash
# This downloads the missing gradle-wrapper.jar
./init-gradle-wrapper.sh
```

### Issue 3: "Unsupported class file major version"
**Problem**: Wrong Java version
**Solution**: Install Java 17 or 21
```bash
# Check Java version
java -version

# Should show Java 17 or 21
```

### Issue 4: "Could not resolve net.minecraftforge:forge"
**Problem**: Network/repository issues
**Solution**: 
1. Check internet connection
2. Clear Gradle cache:
```bash
# Windows
rmdir /s .gradle
gradlew.bat build

# Linux/Mac
rm -rf .gradle
./gradlew build
```

### Issue 5: "Task 'build' not found"
**Problem**: Gradle wrapper not properly initialized
**Solution**:
1. Delete existing wrapper: `rm -rf gradle/wrapper`
2. Re-run initialization: `./init-gradle-wrapper.sh`
3. Try build again: `./gradlew build`

### Issue 6: "Access denied" or "Permission denied"
**Problem**: File permissions (Linux/Mac)
**Solution**:
```bash
chmod +x gradlew
chmod +x init-gradle-wrapper.sh
./gradlew build
```

### Issue 7: Build succeeds but no JAR file
**Problem**: Build completed but output not found
**Solution**: Check these locations:
```bash
# Primary location
build/libs/blocksconnect-forge-mod-1.0.0.jar

# Alternative locations
build/libs/
build/outputs/
```

## Alternative Build Methods

### Method 1: Use System Gradle (if installed)
```bash
# Skip wrapper, use system Gradle
gradle build
```

### Method 2: Manual Dependency Download
If automatic downloads fail:
1. Download gradle-wrapper.jar manually from:
   https://github.com/gradle/gradle/raw/v8.8.0/gradle/wrapper/gradle-wrapper.jar
2. Place in: `gradle/wrapper/gradle-wrapper.jar`
3. Run: `./gradlew build`

### Method 3: IDE Build
1. Import project into IntelliJ IDEA or Eclipse
2. Let IDE handle Gradle setup
3. Use IDE's build function

## Verification

After successful build:
```bash
# Check if JAR exists
ls -la build/libs/

# Should show:
# blocksconnect-forge-mod-1.0.0.jar

# Check JAR contents
jar tf build/libs/blocksconnect-forge-mod-1.0.0.jar | head -10
```

## Development Setup

For development work:
```bash
# Generate IDE run configurations
./gradlew genEclipseRuns    # For Eclipse
./gradlew genIntellijRuns   # For IntelliJ

# Run in development
./gradlew runServer         # Start test server
./gradlew runClient         # Start test client
```

## Clean Build

If you encounter persistent issues:
```bash
# Full clean and rebuild
./gradlew clean
rm -rf .gradle
./gradlew build --refresh-dependencies
```

## Environment Requirements

- **Java**: 17 or 21 (required for Minecraft 1.20.1)
- **Memory**: At least 4GB RAM for build process
- **Disk**: ~2GB free space for dependencies
- **Network**: Internet connection for dependency downloads

## Getting Help

If issues persist:

1. **Check Java version**: `java -version`
2. **Check Gradle version**: `./gradlew --version`
3. **Run with debug**: `./gradlew build --debug`
4. **Check logs**: Look in `build/` directory for error logs

## Success Indicators

Build is successful when you see:
```
BUILD SUCCESSFUL in Xs
```

And the JAR file exists:
```
build/libs/blocksconnect-forge-mod-1.0.0.jar
```

File size should be approximately 50-100KB for this mod.
