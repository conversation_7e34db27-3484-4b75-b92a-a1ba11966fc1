package com.blocksconnect.events;

import com.blocksconnect.api.BlocksConnectApiClient;
import com.blocksconnect.api.models.PlayerData;
import com.blocksconnect.config.ModConfig;
import com.blocksconnect.utils.PlayerDataConverter;
import net.minecraft.server.MinecraftServer;
import net.minecraft.server.level.ServerPlayer;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * Handles player events and synchronization with BlocksConnect API
 * Adapted for Forge's event system
 */
public class PlayerEventHandler {
    private static final Logger LOGGER = LogManager.getLogger("blocksconnect-events");
    
    private final BlocksConnectApiClient apiClient;
    private final ModConfig config;
    private final ConcurrentHashMap<String, PlayerData> onlinePlayers;
    private final ScheduledExecutorService scheduler;
    
    public PlayerEventHandler(BlocksConnectApiClient apiClient, ModConfig config) {
        this.apiClient = apiClient;
        this.config = config;
        this.onlinePlayers = new ConcurrentHashMap<>();
        this.scheduler = Executors.newScheduledThreadPool(2);
    }
    
    /**
     * Handle player join event
     */
    public void onPlayerJoin(ServerPlayer player) {
        try {
            PlayerData playerData = PlayerDataConverter.fromServerPlayer(player);
            onlinePlayers.put(playerData.getUuid(), playerData);
            
            LOGGER.info("Player {} ({}) joined the server", playerData.getUsername(), playerData.getUuid());
            
            if (config.isEnableEventLogging()) {
                apiClient.sendPlayerJoinEvent(playerData).thenAccept(success -> {
                    if (success) {
                        LOGGER.debug("Player join event sent successfully for {}", playerData.getUsername());
                    } else {
                        LOGGER.warn("Failed to send player join event for {}", playerData.getUsername());
                    }
                });
            }
            
            if (config.isEnablePlayerSync()) {
                apiClient.syncPlayerData(playerData).thenAccept(success -> {
                    if (success) {
                        LOGGER.debug("Player data synced successfully for {}", playerData.getUsername());
                    } else {
                        LOGGER.warn("Failed to sync player data for {}", playerData.getUsername());
                    }
                });
            }
        } catch (Exception e) {
            LOGGER.error("Error handling player join event for {}", player.getName().getString(), e);
        }
    }
    
    /**
     * Handle player leave event
     */
    public void onPlayerLeave(ServerPlayer player) {
        try {
            String playerUuid = player.getStringUUID();
            PlayerData playerData = onlinePlayers.remove(playerUuid);

            if (playerData == null) {
                playerData = PlayerDataConverter.fromServerPlayer(player);
            }

            playerData.setOnline(false);
            playerData.setLastSeen(java.time.Instant.now());

            // Make playerData effectively final for lambda usage
            final PlayerData finalPlayerData = playerData;

            LOGGER.info("Player {} ({}) left the server", finalPlayerData.getUsername(), finalPlayerData.getUuid());

            if (config.isEnableEventLogging()) {
                apiClient.sendPlayerLeaveEvent(finalPlayerData).thenAccept(success -> {
                    if (success) {
                        LOGGER.debug("Player leave event sent successfully for {}", finalPlayerData.getUsername());
                    } else {
                        LOGGER.warn("Failed to send player leave event for {}", finalPlayerData.getUsername());
                    }
                });
            }

            if (config.isEnablePlayerSync()) {
                apiClient.syncPlayerData(finalPlayerData).thenAccept(success -> {
                    if (success) {
                        LOGGER.debug("Player data synced successfully for {}", finalPlayerData.getUsername());
                    } else {
                        LOGGER.warn("Failed to sync player data for {}", finalPlayerData.getUsername());
                    }
                });
            }
        } catch (Exception e) {
            LOGGER.error("Error handling player leave event for {}", player.getName().getString(), e);
        }
    }
    
    /**
     * Handle server start event
     */
    public void onServerStart(MinecraftServer server) {
        LOGGER.info("Server started, initializing BlocksConnect integration...");
        
        // Test API connection
        apiClient.testConnection().thenAccept(success -> {
            if (success) {
                LOGGER.info("BlocksConnect API connection established successfully");
                startPeriodicSync();
            } else {
                LOGGER.error("Failed to establish BlocksConnect API connection. Please check your configuration.");
            }
        });
    }
    
    /**
     * Handle server stop event
     */
    public void onServerStop(MinecraftServer server) {
        LOGGER.info("Server stopping, cleaning up BlocksConnect integration...");
        
        // Send leave events for all online players
        onlinePlayers.values().forEach(playerData -> {
            playerData.setOnline(false);
            playerData.setLastSeen(java.time.Instant.now());
            
            if (config.isEnableEventLogging()) {
                apiClient.sendPlayerLeaveEvent(playerData);
            }
        });
        
        onlinePlayers.clear();
        scheduler.shutdown();
        apiClient.shutdown();
    }
    
    /**
     * Start periodic synchronization of player data
     */
    private void startPeriodicSync() {
        if (!config.isEnablePlayerSync()) {
            return;
        }
        
        int intervalSeconds = config.getSyncIntervalSeconds();
        scheduler.scheduleAtFixedRate(this::syncAllPlayerData, intervalSeconds, intervalSeconds, TimeUnit.SECONDS);
        
        LOGGER.info("Started periodic player data sync (interval: {} seconds)", intervalSeconds);
    }
    
    /**
     * Sync all online player data
     */
    private void syncAllPlayerData() {
        if (onlinePlayers.isEmpty()) {
            return;
        }
        
        LOGGER.debug("Syncing data for {} online players", onlinePlayers.size());
        
        onlinePlayers.values().forEach(playerData -> {
            apiClient.syncPlayerData(playerData).thenAccept(success -> {
                if (!success && config.isDebugMode()) {
                    LOGGER.debug("Failed to sync data for player {}", playerData.getUsername());
                }
            });
        });
    }
    
    /**
     * Update player data for an online player
     */
    public void updatePlayerData(ServerPlayer player) {
        String playerUuid = player.getStringUUID();
        PlayerData playerData = onlinePlayers.get(playerUuid);
        
        if (playerData != null) {
            PlayerDataConverter.updateFromServerPlayer(playerData, player);
        }
    }
    
    /**
     * Get online player count
     */
    public int getOnlinePlayerCount() {
        return onlinePlayers.size();
    }
    
    /**
     * Check if a player is tracked as online
     */
    public boolean isPlayerOnline(String playerUuid) {
        return onlinePlayers.containsKey(playerUuid);
    }
}
