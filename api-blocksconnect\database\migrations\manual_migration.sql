-- Manual Migration: Mod Integration Columns
-- Simple version for manual execution
-- Run this if you prefer a minimal approach

-- Add columns (safe to run multiple times)
ALTER TABLE minecraft_servers 
ADD COLUMN IF NOT EXISTS has_mod_integration BOOLEAN DEFAULT FALSE;

ALTER TABLE minecraft_servers 
ADD COLUMN IF NOT EXISTS mod_last_seen TIMESTAMP WITH TIME ZONE;

-- <PERSON><PERSON> indexes
CREATE INDEX IF NOT EXISTS idx_minecraft_servers_mod_integration 
ON minecraft_servers(has_mod_integration);

CREATE INDEX IF NOT EXISTS idx_minecraft_servers_mod_last_seen 
ON minecraft_servers(mod_last_seen);

-- Set default values for existing records
UPDATE minecraft_servers 
SET has_mod_integration = FALSE 
WHERE has_mod_integration IS NULL;

-- Verify the migration
SELECT 'Migration completed successfully!' as status;

-- Show the new columns
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'minecraft_servers' 
AND column_name IN ('has_mod_integration', 'mod_last_seen');
