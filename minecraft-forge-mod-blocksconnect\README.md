# BlocksConnect Forge Mod

A Forge mod for Minecraft 1.21.1 that integrates Minecraft servers with the BlocksConnect player management system. This mod provides real-time player tracking, event logging, and seamless integration with the BlocksConnect web panel.

## Features

### 🎮 Player Management Integration
- **Real-time Player Tracking**: Automatically tracks player join/leave events
- **Player Data Synchronization**: Syncs player stats, position, and game state
- **Event Logging**: Logs all player activities to BlocksConnect dashboard
- **Permission Sync**: Integrates with BlocksConnect permission system

### 📊 Server Monitoring
- **Real-time Metrics**: Server performance and player statistics
- **Event History**: Complete log of server events and player activities
- **Multi-tenant Support**: Proper user isolation for server management

### 🔧 Easy Configuration
- **JSON Configuration**: Simple configuration file for easy setup
- **Forge Config Integration**: Native Forge configuration system support
- **Hot Reload**: Configuration changes without server restart

## Requirements

- **Minecraft**: 1.21.1
- **Forge**: 52.0.17 or higher
- **Java**: 21 or higher
- **BlocksConnect Server**: Running BlocksConnect API backend

## Installation

### For Server Administrators

1. **Download the mod**:
   - Download the latest `.jar` file from the releases page
   - Or build from source (see Building section below)

2. **Install on your server**:
   ```bash
   # Place the mod in your server's mods folder
   cp blocksconnect-forge-mod-1.0.0.jar /path/to/your/server/mods/
   ```

3. **Configure the mod**:
   - Start your server once to generate the default configuration
   - Edit `config/blocksconnect.json` with your server details
   - Restart the server

### Configuration

The mod creates a configuration file at `config/blocksconnect.json`:

```json
{
  "apiBaseUrl": "http://localhost:5000/api",
  "serverToken": "your-server-token-here",
  "serverId": "your-server-id-here",
  "enablePlayerSync": true,
  "enablePermissionSync": true,
  "enableEventLogging": true,
  "syncIntervalSeconds": 30,
  "debugMode": false
}
```

#### Configuration Options

- **apiBaseUrl**: Base URL for your BlocksConnect API server
- **serverToken**: Authentication token for API access
- **serverId**: Unique identifier for this server in BlocksConnect
- **enablePlayerSync**: Enable/disable player data synchronization
- **enablePermissionSync**: Enable/disable permission synchronization
- **enableEventLogging**: Enable/disable event logging to BlocksConnect
- **syncIntervalSeconds**: How often to sync player data (10-300 seconds)
- **debugMode**: Enable debug logging for troubleshooting

### Getting Server Credentials

1. **Log into BlocksConnect Dashboard**:
   - Go to your BlocksConnect web panel
   - Navigate to your server settings

2. **Generate API Token**:
   - In server settings, find the "API Integration" section
   - Click "Generate Token" to create a new server token
   - Copy the token and server ID

3. **Update Configuration**:
   - Paste the token and server ID into your `blocksconnect.json`
   - Set the correct API base URL for your BlocksConnect instance

## Building from Source

### Prerequisites
- Java 21 JDK
- Git

### Build Steps

1. **Clone the repository**:
   ```bash
   git clone https://github.com/blocksconnect/forge-mod.git
   cd forge-mod
   ```

2. **Build the mod**:
   ```bash
   ./gradlew build
   ```

3. **Find the built mod**:
   ```bash
   # The built mod will be in:
   build/libs/blocksconnect-forge-mod-1.0.0.jar
   ```

### Development Setup

1. **Import into IDE**:
   ```bash
   ./gradlew genEclipseRuns
   ./gradlew genIntellijRuns
   ```

2. **Run in development**:
   ```bash
   ./gradlew runServer
   ```

## API Integration

The mod communicates with the BlocksConnect API to:

- Send player join/leave events
- Sync player data (position, stats, game mode)
- Update server status and metrics
- Handle permission synchronization

### Supported Events

- **player_join**: When a player joins the server
- **player_leave**: When a player leaves the server
- **player_update**: Periodic player data updates

### Data Synchronization

The mod syncs the following player data:
- UUID and username
- Display name and IP address
- Game mode and health status
- Experience level and progress
- Position and dimension
- Food level and other stats

## Troubleshooting

### Common Issues

1. **API Connection Failed**:
   - Check your `apiBaseUrl` configuration
   - Verify the BlocksConnect server is running
   - Ensure firewall allows connections

2. **Authentication Errors**:
   - Verify your `serverToken` is correct
   - Check that the token hasn't expired
   - Ensure the `serverId` matches your server

3. **Player Data Not Syncing**:
   - Check `enablePlayerSync` is set to `true`
   - Verify API connectivity
   - Check server logs for error messages

### Debug Mode

Enable debug mode in your configuration for detailed logging:

```json
{
  "debugMode": true
}
```

This will provide additional information about API calls and data synchronization.

## Compatibility

### Minecraft Versions
- **Supported**: 1.21.1
- **Forge Version**: 52.0.17+

### Server Software
- **Minecraft Forge Server**: Full support
- **Other Servers**: Not supported (use Fabric mod for Fabric servers)

## Migration from Fabric

If you're migrating from the Fabric version of this mod:

1. **Stop your server**
2. **Remove the Fabric mod** from the `mods` folder
3. **Install this Forge mod** in the `mods` folder
4. **Copy your configuration** from the Fabric config to the Forge config
5. **Start your server**

The configuration format is identical, so you can copy your existing `blocksconnect.json` file.

## Support

For support and bug reports:
- Check the [GitHub Issues](https://github.com/blocksconnect/forge-mod/issues)
- Join our [Discord Server](https://discord.gg/blocksconnect)
- Email: <EMAIL>

## License

This mod is licensed under the MIT License. See [LICENSE](LICENSE) for details.

## Contributing

Contributions are welcome! Please read our [Contributing Guidelines](CONTRIBUTING.md) before submitting pull requests.

## Changelog

### Version 1.0.0
- Initial release
- Player join/leave event tracking
- Real-time player data synchronization
- API integration with BlocksConnect
- Forge configuration system integration
- Multi-tenant server support
