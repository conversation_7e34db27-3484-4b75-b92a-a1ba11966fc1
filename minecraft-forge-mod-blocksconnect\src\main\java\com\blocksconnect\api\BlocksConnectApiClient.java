package com.blocksconnect.api;

import com.blocksconnect.config.ModConfig;
import com.blocksconnect.api.models.PlayerData;
import com.blocksconnect.api.models.ServerEvent;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import okhttp3.*;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.IOException;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;

/**
 * API client for communicating with BlocksConnect backend
 * Handles all HTTP requests and responses
 */
public class BlocksConnectApiClient {
    private static final Logger LOGGER = LogManager.getLogger("blocksconnect-api");
    private static final MediaType JSON = MediaType.get("application/json; charset=utf-8");
    
    private final ModConfig config;
    private final OkHttpClient httpClient;
    private final Gson gson;
    
    public BlocksConnectApiClient(ModConfig config) {
        this.config = config;
        this.gson = new GsonBuilder()
                .setDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'")
                .create();
        
        this.httpClient = new OkHttpClient.Builder()
                .connectTimeout(10, TimeUnit.SECONDS)
                .readTimeout(30, TimeUnit.SECONDS)
                .writeTimeout(30, TimeUnit.SECONDS)
                .addInterceptor(this::addAuthHeaders)
                .build();
    }
    
    /**
     * Add authentication headers to requests
     */
    private Response addAuthHeaders(Interceptor.Chain chain) throws IOException {
        Request original = chain.request();
        Request.Builder builder = original.newBuilder()
                .header("Authorization", "Bearer " + config.getServerToken())
                .header("Content-Type", "application/json")
                .header("User-Agent", "BlocksConnect-Forge-Mod/1.0.0");
        
        return chain.proceed(builder.build());
    }
    
    /**
     * Send player join event to BlocksConnect
     */
    public CompletableFuture<Boolean> sendPlayerJoinEvent(PlayerData playerData) {
        if (!config.isConfigurationValid()) {
            LOGGER.warn("Configuration invalid, skipping player join event");
            return CompletableFuture.completedFuture(false);
        }
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                ServerEvent event = new ServerEvent(
                    "player_join",
                    config.getServerId(),
                    playerData.getUuid(),
                    playerData
                );
                
                String json = gson.toJson(event);
                RequestBody body = RequestBody.create(json, JSON);
                
                Request request = new Request.Builder()
                        .url(config.getApiBaseUrl() + "/minecraft/servers/" + config.getServerId() + "/events")
                        .post(body)
                        .build();
                
                try (Response response = httpClient.newCall(request).execute()) {
                    if (response.isSuccessful()) {
                        LOGGER.debug("Player join event sent successfully for {}", playerData.getUsername());
                        return true;
                    } else {
                        LOGGER.error("Failed to send player join event: {} {}", response.code(), response.message());
                        return false;
                    }
                }
            } catch (Exception e) {
                LOGGER.error("Error sending player join event", e);
                return false;
            }
        });
    }
    
    /**
     * Send player leave event to BlocksConnect
     */
    public CompletableFuture<Boolean> sendPlayerLeaveEvent(PlayerData playerData) {
        if (!config.isConfigurationValid()) {
            LOGGER.warn("Configuration invalid, skipping player leave event");
            return CompletableFuture.completedFuture(false);
        }
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                ServerEvent event = new ServerEvent(
                    "player_leave",
                    config.getServerId(),
                    playerData.getUuid(),
                    playerData
                );
                
                String json = gson.toJson(event);
                RequestBody body = RequestBody.create(json, JSON);
                
                Request request = new Request.Builder()
                        .url(config.getApiBaseUrl() + "/minecraft/servers/" + config.getServerId() + "/events")
                        .post(body)
                        .build();
                
                try (Response response = httpClient.newCall(request).execute()) {
                    if (response.isSuccessful()) {
                        LOGGER.debug("Player leave event sent successfully for {}", playerData.getUsername());
                        return true;
                    } else {
                        LOGGER.error("Failed to send player leave event: {} {}", response.code(), response.message());
                        return false;
                    }
                }
            } catch (Exception e) {
                LOGGER.error("Error sending player leave event", e);
                return false;
            }
        });
    }
    
    /**
     * Sync player data with BlocksConnect
     */
    public CompletableFuture<Boolean> syncPlayerData(PlayerData playerData) {
        if (!config.isConfigurationValid()) {
            LOGGER.warn("Configuration invalid, skipping player data sync");
            return CompletableFuture.completedFuture(false);
        }
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                String json = gson.toJson(playerData);
                RequestBody body = RequestBody.create(json, JSON);
                
                Request request = new Request.Builder()
                        .url(config.getApiBaseUrl() + "/minecraft/servers/" + config.getServerId() + "/players/" + playerData.getUuid())
                        .put(body)
                        .build();
                
                try (Response response = httpClient.newCall(request).execute()) {
                    if (response.isSuccessful()) {
                        LOGGER.debug("Player data synced successfully for {}", playerData.getUsername());
                        return true;
                    } else {
                        LOGGER.error("Failed to sync player data: {} {}", response.code(), response.message());
                        return false;
                    }
                }
            } catch (Exception e) {
                LOGGER.error("Error syncing player data", e);
                return false;
            }
        });
    }
    
    /**
     * Test API connection
     */
    public CompletableFuture<Boolean> testConnection() {
        if (!config.isConfigurationValid()) {
            LOGGER.warn("Configuration invalid, cannot test connection");
            return CompletableFuture.completedFuture(false);
        }
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                Request request = new Request.Builder()
                        .url(config.getApiBaseUrl() + "/minecraft/servers/" + config.getServerId())
                        .get()
                        .build();
                
                try (Response response = httpClient.newCall(request).execute()) {
                    if (response.isSuccessful()) {
                        LOGGER.info("API connection test successful");
                        return true;
                    } else {
                        LOGGER.error("API connection test failed: {} {}", response.code(), response.message());
                        return false;
                    }
                }
            } catch (Exception e) {
                LOGGER.error("Error testing API connection", e);
                return false;
            }
        });
    }
    
    /**
     * Shutdown the HTTP client
     */
    public void shutdown() {
        httpClient.dispatcher().executorService().shutdown();
        httpClient.connectionPool().evictAll();
    }
}
