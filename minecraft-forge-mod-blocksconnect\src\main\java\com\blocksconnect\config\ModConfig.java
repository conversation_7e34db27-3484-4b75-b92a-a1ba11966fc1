package com.blocksconnect.config;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import net.minecraftforge.common.ForgeConfigSpec;
import net.minecraftforge.fml.loading.FMLPaths;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;

/**
 * Configuration management for BlocksConnect mod
 * Handles loading and saving of mod configuration using Forge's config system
 */
public class ModConfig {
    private static final Logger LOGGER = LogManager.getLogger("blocksconnect-config");
    private static final String CONFIG_FILE_NAME = "blocksconnect.json";
    
    private final Gson gson = new GsonBuilder().setPrettyPrinting().create();
    private final Path configPath;
    
    // Forge config spec
    private static final ForgeConfigSpec.Builder BUILDER = new ForgeConfigSpec.Builder();
    
    // Configuration fields with Forge config spec
    public static final ForgeConfigSpec.ConfigValue<String> API_BASE_URL = BUILDER
            .comment("Base URL for the BlocksConnect API")
            .define("apiBaseUrl", "http://localhost:5000/api");
    
    public static final ForgeConfigSpec.ConfigValue<String> SERVER_TOKEN = BUILDER
            .comment("Server authentication token")
            .define("serverToken", "");
    
    public static final ForgeConfigSpec.ConfigValue<String> SERVER_ID = BUILDER
            .comment("Server ID for BlocksConnect integration")
            .define("serverId", "");
    
    public static final ForgeConfigSpec.BooleanValue ENABLE_PLAYER_SYNC = BUILDER
            .comment("Enable player data synchronization")
            .define("enablePlayerSync", true);
    
    public static final ForgeConfigSpec.BooleanValue ENABLE_PERMISSION_SYNC = BUILDER
            .comment("Enable permission synchronization")
            .define("enablePermissionSync", true);
    
    public static final ForgeConfigSpec.BooleanValue ENABLE_EVENT_LOGGING = BUILDER
            .comment("Enable event logging to BlocksConnect")
            .define("enableEventLogging", true);
    
    public static final ForgeConfigSpec.IntValue SYNC_INTERVAL_SECONDS = BUILDER
            .comment("Interval in seconds for periodic data synchronization")
            .defineInRange("syncIntervalSeconds", 30, 10, 300);
    
    public static final ForgeConfigSpec.BooleanValue DEBUG_MODE = BUILDER
            .comment("Enable debug mode for additional logging")
            .define("debugMode", false);
    
    public static final ForgeConfigSpec SPEC = BUILDER.build();
    
    // Local configuration values (for JSON file compatibility)
    private String apiBaseUrl = "http://localhost:5000/api";
    private String serverToken = "";
    private String serverId = "";
    private boolean enablePlayerSync = true;
    private boolean enablePermissionSync = true;
    private boolean enableEventLogging = true;
    private int syncIntervalSeconds = 30;
    private boolean debugMode = false;
    
    public ModConfig() {
        this.configPath = FMLPaths.CONFIGDIR.get().resolve(CONFIG_FILE_NAME);
    }
    
    /**
     * Load configuration from JSON file (for backward compatibility)
     * Also updates Forge config values
     */
    public void load() {
        if (Files.exists(configPath)) {
            try {
                String json = Files.readString(configPath);
                ModConfig loaded = gson.fromJson(json, ModConfig.class);
                
                // Copy loaded values
                this.apiBaseUrl = loaded.apiBaseUrl;
                this.serverToken = loaded.serverToken;
                this.serverId = loaded.serverId;
                this.enablePlayerSync = loaded.enablePlayerSync;
                this.enablePermissionSync = loaded.enablePermissionSync;
                this.enableEventLogging = loaded.enableEventLogging;
                this.syncIntervalSeconds = loaded.syncIntervalSeconds;
                this.debugMode = loaded.debugMode;
                
                LOGGER.info("Configuration loaded from {}", configPath);
            } catch (IOException e) {
                LOGGER.error("Failed to load configuration from {}", configPath, e);
                save(); // Save default configuration
            }
        } else {
            LOGGER.info("Configuration file not found, creating default configuration at {}", configPath);
            save();
        }
    }
    
    /**
     * Save configuration to JSON file
     */
    public void save() {
        try {
            Files.createDirectories(configPath.getParent());
            String json = gson.toJson(this);
            Files.writeString(configPath, json);
            LOGGER.info("Configuration saved to {}", configPath);
        } catch (IOException e) {
            LOGGER.error("Failed to save configuration to {}", configPath, e);
        }
    }
    
    /**
     * Get the Forge config spec
     */
    public ForgeConfigSpec getConfigSpec() {
        return SPEC;
    }
    
    /**
     * Check if configuration is valid for API communication
     */
    public boolean isConfigurationValid() {
        return !getServerToken().isEmpty() && !getServerId().isEmpty() && !getApiBaseUrl().isEmpty();
    }
    
    // Getters that prioritize Forge config values when available
    public String getApiBaseUrl() {
        return API_BASE_URL.get() != null ? API_BASE_URL.get() : apiBaseUrl;
    }
    
    public String getServerToken() {
        return SERVER_TOKEN.get() != null ? SERVER_TOKEN.get() : serverToken;
    }
    
    public String getServerId() {
        return SERVER_ID.get() != null ? SERVER_ID.get() : serverId;
    }
    
    public boolean isEnablePlayerSync() {
        return ENABLE_PLAYER_SYNC.get() != null ? ENABLE_PLAYER_SYNC.get() : enablePlayerSync;
    }
    
    public boolean isEnablePermissionSync() {
        return ENABLE_PERMISSION_SYNC.get() != null ? ENABLE_PERMISSION_SYNC.get() : enablePermissionSync;
    }
    
    public boolean isEnableEventLogging() {
        return ENABLE_EVENT_LOGGING.get() != null ? ENABLE_EVENT_LOGGING.get() : enableEventLogging;
    }
    
    public int getSyncIntervalSeconds() {
        return SYNC_INTERVAL_SECONDS.get() != null ? SYNC_INTERVAL_SECONDS.get() : syncIntervalSeconds;
    }
    
    public boolean isDebugMode() {
        return DEBUG_MODE.get() != null ? DEBUG_MODE.get() : debugMode;
    }
    
    // Setters for JSON compatibility
    public void setApiBaseUrl(String apiBaseUrl) {
        this.apiBaseUrl = apiBaseUrl;
    }
    
    public void setServerToken(String serverToken) {
        this.serverToken = serverToken;
    }
    
    public void setServerId(String serverId) {
        this.serverId = serverId;
    }
    
    public void setEnablePlayerSync(boolean enablePlayerSync) {
        this.enablePlayerSync = enablePlayerSync;
    }
    
    public void setEnablePermissionSync(boolean enablePermissionSync) {
        this.enablePermissionSync = enablePermissionSync;
    }
    
    public void setEnableEventLogging(boolean enableEventLogging) {
        this.enableEventLogging = enableEventLogging;
    }
    
    public void setSyncIntervalSeconds(int syncIntervalSeconds) {
        this.syncIntervalSeconds = syncIntervalSeconds;
    }
    
    public void setDebugMode(boolean debugMode) {
        this.debugMode = debugMode;
    }
}
