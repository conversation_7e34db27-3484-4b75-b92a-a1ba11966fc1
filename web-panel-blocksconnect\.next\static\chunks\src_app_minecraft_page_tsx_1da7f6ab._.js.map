{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/BlocksConnect/web-panel-blocksconnect/src/app/minecraft/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useAuth } from '../../contexts/AuthContext';\n\nexport default function MinecraftPage() {\n  const router = useRouter();\n  const { user, isLoading } = useAuth();\n\n  useEffect(() => {\n    if (!isLoading) {\n      if (user) {\n        // Redirect to the minecraft dashboard\n        router.push('/minecraft/dashboard');\n      } else {\n        router.push('/login');\n      }\n    }\n  }, [user, isLoading, router]);\n\n  // Show loading state while checking authentication\n  return (\n    <div className=\"min-h-screen flex items-center justify-center\">\n      <div className=\"text-center fade-in-up\">\n        <div className=\"relative mb-6\">\n          <div className=\"animate-spin h-16 w-16 border-4 border-blue-500/30 border-t-blue-500 rounded-full mx-auto\"></div>\n          <div className=\"absolute inset-0 animate-ping h-16 w-16 border-4 border-blue-500/20 rounded-full mx-auto\"></div>\n        </div>\n        <h3 className=\"text-xl font-semibold text-white mb-2\">Loading Minecraft Service</h3>\n        <p className=\"text-gray-300 font-light\">Redirecting to Minecraft dashboard...</p>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAElC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,CAAC,WAAW;gBACd,IAAI,MAAM;oBACR,sCAAsC;oBACtC,OAAO,IAAI,CAAC;gBACd,OAAO;oBACL,OAAO,IAAI,CAAC;gBACd;YACF;QACF;kCAAG;QAAC;QAAM;QAAW;KAAO;IAE5B,mDAAmD;IACnD,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;;;;;;;8BAEjB,6LAAC;oBAAG,WAAU;8BAAwC;;;;;;8BACtD,6LAAC;oBAAE,WAAU;8BAA2B;;;;;;;;;;;;;;;;;AAIhD;GA5BwB;;QACP,qIAAA,CAAA,YAAS;QACI,kIAAA,CAAA,UAAO;;;KAFb", "debugId": null}}]}