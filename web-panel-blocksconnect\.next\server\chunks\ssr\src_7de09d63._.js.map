{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/BlocksConnect/web-panel-blocksconnect/src/components/ProtectedRoute.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useAuth } from '../contexts/AuthContext';\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode;\n}\n\nexport default function ProtectedRoute({ children }: ProtectedRouteProps) {\n  const { user, isLoading } = useAuth();\n  const router = useRouter();\n\n  useEffect(() => {\n    if (!isLoading && !user) {\n      router.push('/login');\n    }\n  }, [user, isLoading, router]);\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-center fade-in-fast\">\n          <div className=\"relative mb-6\">\n            <div className=\"animate-spin h-16 w-16 border-4 border-blue-500/30 border-t-blue-500 rounded-full mx-auto\"></div>\n            <div className=\"absolute inset-0 animate-ping h-16 w-16 border-4 border-blue-500/20 rounded-full mx-auto\"></div>\n          </div>\n          <h3 className=\"text-xl font-semibold text-white mb-2\">Loading</h3>\n          <p className=\"text-gray-300 font-light\">Please wait...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (!user) {\n    return null; // Will redirect to login\n  }\n\n  return <>{children}</>;\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAUe,SAAS,eAAe,EAAE,QAAQ,EAAuB;IACtE,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAClC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,aAAa,CAAC,MAAM;YACvB,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAM;QAAW;KAAO;IAE5B,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;;;;;;;kCAEjB,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,8OAAC;wBAAE,WAAU;kCAA2B;;;;;;;;;;;;;;;;;IAIhD;IAEA,IAAI,CAAC,MAAM;QACT,OAAO,MAAM,yBAAyB;IACxC;IAEA,qBAAO;kBAAG;;AACZ", "debugId": null}}, {"offset": {"line": 101, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/BlocksConnect/web-panel-blocksconnect/src/components/Header.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useRef, useEffect } from 'react';\nimport Link from 'next/link';\nimport { useAuth } from '../contexts/AuthContext';\nimport { useRouter, usePathname } from 'next/navigation';\nimport { QuickThemeToggle } from './ThemeSettings';\n\ninterface NavigationItem {\n  name: string;\n  href: string;\n  icon: React.ReactNode;\n  description: string;\n}\n\nexport default function AdminHeader() {\n  const { user, logout } = useAuth();\n  const router = useRouter();\n  const pathname = usePathname();\n  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const wrapperRef = useRef<HTMLDivElement>(null);\n\n  const handleLogout = () => {\n    logout();\n    router.push('/login');\n    setIsUserMenuOpen(false);\n  };\n\n  // Close the menu when you click anywhere *outside* the wrapper\n  useEffect(() => {\n    function onDocClick(e: MouseEvent) {\n      if (wrapperRef.current && !wrapperRef.current.contains(e.target as Node)) {\n        setIsUserMenuOpen(false);\n      }\n    }\n    document.addEventListener('mousedown', onDocClick);\n    return () => document.removeEventListener('mousedown', onDocClick);\n  }, []);\n\n  const navigationItems: NavigationItem[] = [\n    {\n      name: 'Dashboard',\n      href: '/',\n      description: 'Overview of all services',\n      icon: (\n        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z\" />\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 5a2 2 0 012-2h4a2 2 0 012 2v6a2 2 0 01-2 2H10a2 2 0 01-2-2V5z\" />\n        </svg>\n      ),\n    },\n    {\n      name: 'Minecraft',\n      href: '/minecraft',\n      description: 'Server management',\n      icon: (\n        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01\" />\n        </svg>\n      ),\n    },\n\n  ];\n\n  const isActiveRoute = (href: string) => {\n    if (href === '/') {\n      return pathname === '/';\n    }\n    return pathname.startsWith(href);\n  };\n\n  const getUserInitials = () => {\n    if (user?.displayName) {\n      return user.displayName\n        .split(' ')\n        .map(name => name[0])\n        .join('')\n        .toUpperCase()\n        .slice(0, 2);\n    }\n    if (user?.email) {\n      return user.email[0].toUpperCase();\n    }\n    return 'U';\n  };\n\n  return (\n    <header className=\"relative border-b border-white/10\">\n      {/* Background Gradients */}\n      <div className=\"absolute inset-0 bg-gradient-to-r from-blue-600/95 to-purple-600/95 backdrop-blur-md z-0\"></div>\n      <div className=\"absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 z-0\"></div>\n\n      {/* Animated Background Elements */}\n      <div className=\"absolute top-0 left-1/4 w-32 h-32 bg-blue-400/10 rounded-full blur-3xl animate-pulse z-0\"></div>\n      <div className=\"absolute top-0 right-1/4 w-24 h-24 bg-purple-400/10 rounded-full blur-2xl animate-pulse delay-1000 z-0\"></div>\n\n      <div className=\"relative z-10 max-w-7xl mx-auto\">\n        <div className=\"flex justify-between items-center px-4 sm:px-6 lg:px-8 py-4\">\n\n          {/* Logo and Brand */}\n          <div className=\"flex items-center fade-in-up\">\n            <Link href=\"/\" className=\"flex items-center group\">\n              <div className=\"w-12 h-12 rounded-xl flex items-center justify-center mr-4 bg-gradient-to-br from-blue-400/30 to-purple-400/30 backdrop-blur-sm border border-white/20 group-hover:scale-110 transition-all duration-300 group-hover:shadow-lg group-hover:shadow-blue-500/25\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-7 w-7 text-blue-200 group-hover:text-white transition-colors duration-300\" viewBox=\"0 0 24 24\" fill=\"currentColor\">\n                  <path d=\"M3 3h18v18H3V3zm2 2v14h14V5H5zm2 2h10v10H7V7zm2 2v6h6V9H9z\"/>\n                  <path d=\"M10 10h1v1h-1v-1zm2 0h1v1h-1v-1zm-2 2h1v1h-1v-1zm2 0h1v1h-1v-1z\"/>\n                </svg>\n              </div>\n              <div>\n                <h1 className=\"text-2xl font-bold text-white group-hover:text-blue-200 transition-colors duration-300\">\n                  BlocksConnect\n                </h1>\n                <p className=\"text-sm text-blue-200/80 font-medium\">Admin Panel</p>\n              </div>\n            </Link>\n          </div>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden md:flex items-center space-x-1 fade-in-up delay-100\">\n            {navigationItems.map((item) => (\n              <Link\n                key={item.name}\n                href={item.href}\n                className={`relative px-4 py-2 rounded-lg font-medium transition-all duration-300 group ${\n                  isActiveRoute(item.href)\n                    ? 'text-white bg-white/20 shadow-lg'\n                    : 'text-blue-100 hover:text-white hover:bg-white/10'\n                }`}\n              >\n                <div className=\"flex items-center space-x-2\">\n                  {item.icon}\n                  <span>{item.name}</span>\n                </div>\n                {isActiveRoute(item.href) && (\n                  <div className=\"absolute bottom-0 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-blue-300 rounded-full\"></div>\n                )}\n              </Link>\n            ))}\n          </nav>\n\n          {/* User Menu and Mobile Menu Button */}\n          <div className=\"flex items-center space-x-4 fade-in-up delay-200\">\n            {/* Theme Toggle */}\n            <QuickThemeToggle />\n\n            {/* Mobile Menu Button */}\n            <button\n              onClick={(e) => {\n                e.stopPropagation();\n                setIsMobileMenuOpen(!isMobileMenuOpen);\n              }}\n              className=\"md:hidden p-2 rounded-lg text-white hover:bg-white/10 transition-colors duration-200\"\n            >\n              <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n              </svg>\n            </button>\n\n            {/* User Menu */}\n            <div className=\"relative\" ref={wrapperRef}>\n              <button\n                onClick={(e) => {\n                  e.stopPropagation();\n                  setIsUserMenuOpen(!isUserMenuOpen);\n                }}\n                className=\"flex items-center space-x-3 p-2 rounded-lg hover:bg-white/10 transition-all duration-200 group\"\n              >\n                <div className=\"w-10 h-10 rounded-full bg-gradient-to-br from-blue-400 to-purple-500 flex items-center justify-center text-white font-semibold text-sm shadow-lg group-hover:scale-105 transition-transform duration-200\">\n                  {getUserInitials()}\n                </div>\n                <div className=\"hidden sm:block text-left\">\n                  <p className=\"text-white font-medium text-sm\">\n                    {user?.displayName || 'User'}\n                  </p>\n                  <p className=\"text-blue-200/80 text-xs\">\n                    {user?.email}\n                  </p>\n                </div>\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className={`h-4 w-4 text-blue-200 transition-transform duration-200 ${isUserMenuOpen ? 'rotate-180' : ''}`} fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n                </svg>\n              </button>\n\n              {/* User Dropdown Menu */}\n              {isUserMenuOpen && (\n                <div\n                  onClick={(e) => e.stopPropagation()}\n                  className=\"absolute right-0 mt-2 w-64 bg-gray-900/95 backdrop-blur-md rounded-xl shadow-2xl border border-white/10 py-2 z-[100] fade-in-up\"\n                >\n                  <div className=\"px-4 py-3 border-b border-white/10\">\n                    <p className=\"text-white font-semibold\">{user?.displayName || 'User'}</p>\n                    <p className=\"text-gray-300 text-sm\">{user?.email}</p>\n                    <p className=\"text-gray-400 text-xs mt-1\">Administrator</p>\n                  </div>\n\n                  <div className=\"py-2\">\n                    <Link\n                      href=\"/account\"\n                      onClick={() => setIsUserMenuOpen(false)}\n                      className=\"flex items-center px-4 py-2 text-gray-300 hover:text-white hover:bg-white/10 transition-colors duration-200\"\n                    >\n                      <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n                      </svg>\n                      Account Settings\n                    </Link>\n\n                    <button\n                      onClick={handleLogout}\n                      className=\"flex items-center w-full px-4 py-2 text-gray-300 hover:text-red-400 hover:bg-red-500/10 transition-colors duration-200\"\n                    >\n                      <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 mr-3\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\n                      </svg>\n                      Sign Out\n                    </button>\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n\n        {/* Mobile Navigation Menu */}\n        {isMobileMenuOpen && (\n          <div className=\"md:hidden border-t border-white/10 bg-black/20 backdrop-blur-sm\">\n            <div className=\"px-4 py-4 space-y-2\">\n              {navigationItems.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  onClick={() => setIsMobileMenuOpen(false)}\n                  className={`flex items-center space-x-3 px-4 py-3 rounded-lg transition-all duration-200 ${\n                    isActiveRoute(item.href)\n                      ? 'text-white bg-white/20'\n                      : 'text-blue-100 hover:text-white hover:bg-white/10'\n                  }`}\n                >\n                  {item.icon}\n                  <div>\n                    <p className=\"font-medium\">{item.name}</p>\n                    <p className=\"text-xs text-blue-200/70\">{item.description}</p>\n                  </div>\n                </Link>\n              ))}\n            </div>\n          </div>\n        )}\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAee,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAE1C,MAAM,eAAe;QACnB;QACA,OAAO,IAAI,CAAC;QACZ,kBAAkB;IACpB;IAEA,+DAA+D;IAC/D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS,WAAW,CAAa;YAC/B,IAAI,WAAW,OAAO,IAAI,CAAC,WAAW,OAAO,CAAC,QAAQ,CAAC,EAAE,MAAM,GAAW;gBACxE,kBAAkB;YACpB;QACF;QACA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;IACzD,GAAG,EAAE;IAEL,MAAM,kBAAoC;QACxC;YACE,MAAM;YACN,MAAM;YACN,aAAa;YACb,oBACE,8OAAC;gBAAI,OAAM;gBAA6B,WAAU;gBAAU,MAAK;gBAAO,SAAQ;gBAAY,QAAO;;kCACjG,8OAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;kCACrE,8OAAC;wBAAK,eAAc;wBAAQ,gBAAe;wBAAQ,aAAa;wBAAG,GAAE;;;;;;;;;;;;QAG3E;QACA;YACE,MAAM;YACN,MAAM;YACN,aAAa;YACb,oBACE,8OAAC;gBAAI,OAAM;gBAA6B,WAAU;gBAAU,MAAK;gBAAO,SAAQ;gBAAY,QAAO;0BACjG,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;;QAG3E;KAED;IAED,MAAM,gBAAgB,CAAC;QACrB,IAAI,SAAS,KAAK;YAChB,OAAO,aAAa;QACtB;QACA,OAAO,SAAS,UAAU,CAAC;IAC7B;IAEA,MAAM,kBAAkB;QACtB,IAAI,MAAM,aAAa;YACrB,OAAO,KAAK,WAAW,CACpB,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,IAAI,CAAC,EAAE,EACnB,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;QACd;QACA,IAAI,MAAM,OAAO;YACf,OAAO,KAAK,KAAK,CAAC,EAAE,CAAC,WAAW;QAClC;QACA,OAAO;IACT;IAEA,qBACE,8OAAC;QAAO,WAAU;;0BAEhB,8OAAC;gBAAI,WAAU;;;;;;0BACf,8OAAC;gBAAI,WAAU;;;;;;0BAGf,8OAAC;gBAAI,WAAU;;;;;;0BACf,8OAAC;gBAAI,WAAU;;;;;;0BAEf,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CAGb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;;sDACvB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,OAAM;gDAA6B,WAAU;gDAA8E,SAAQ;gDAAY,MAAK;;kEACvJ,8OAAC;wDAAK,GAAE;;;;;;kEACR,8OAAC;wDAAK,GAAE;;;;;;;;;;;;;;;;;sDAGZ,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAyF;;;;;;8DAGvG,8OAAC;oDAAE,WAAU;8DAAuC;;;;;;;;;;;;;;;;;;;;;;;0CAM1D,8OAAC;gCAAI,WAAU;0CACZ,gBAAgB,GAAG,CAAC,CAAC,qBACpB,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAW,CAAC,4EAA4E,EACtF,cAAc,KAAK,IAAI,IACnB,qCACA,oDACJ;;0DAEF,8OAAC;gDAAI,WAAU;;oDACZ,KAAK,IAAI;kEACV,8OAAC;kEAAM,KAAK,IAAI;;;;;;;;;;;;4CAEjB,cAAc,KAAK,IAAI,mBACtB,8OAAC;gDAAI,WAAU;;;;;;;uCAbZ,KAAK,IAAI;;;;;;;;;;0CAoBpB,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC,mIAAA,CAAA,mBAAgB;;;;;kDAGjB,8OAAC;wCACC,SAAS,CAAC;4CACR,EAAE,eAAe;4CACjB,oBAAoB,CAAC;wCACvB;wCACA,WAAU;kDAEV,cAAA,8OAAC;4CAAI,OAAM;4CAA6B,WAAU;4CAAU,MAAK;4CAAO,SAAQ;4CAAY,QAAO;sDACjG,cAAA,8OAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;kDAKzE,8OAAC;wCAAI,WAAU;wCAAW,KAAK;;0DAC7B,8OAAC;gDACC,SAAS,CAAC;oDACR,EAAE,eAAe;oDACjB,kBAAkB,CAAC;gDACrB;gDACA,WAAU;;kEAEV,8OAAC;wDAAI,WAAU;kEACZ;;;;;;kEAEH,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAU;0EACV,MAAM,eAAe;;;;;;0EAExB,8OAAC;gEAAE,WAAU;0EACV,MAAM;;;;;;;;;;;;kEAGX,8OAAC;wDAAI,OAAM;wDAA6B,WAAW,CAAC,wDAAwD,EAAE,iBAAiB,eAAe,IAAI;wDAAE,MAAK;wDAAO,SAAQ;wDAAY,QAAO;kEACzL,cAAA,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;;;;;;4CAKxE,gCACC,8OAAC;gDACC,SAAS,CAAC,IAAM,EAAE,eAAe;gDACjC,WAAU;;kEAEV,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAU;0EAA4B,MAAM,eAAe;;;;;;0EAC9D,8OAAC;gEAAE,WAAU;0EAAyB,MAAM;;;;;;0EAC5C,8OAAC;gEAAE,WAAU;0EAA6B;;;;;;;;;;;;kEAG5C,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,4JAAA,CAAA,UAAI;gEACH,MAAK;gEACL,SAAS,IAAM,kBAAkB;gEACjC,WAAU;;kFAEV,8OAAC;wEAAI,OAAM;wEAA6B,WAAU;wEAAe,MAAK;wEAAO,SAAQ;wEAAY,QAAO;kFACtG,cAAA,8OAAC;4EAAK,eAAc;4EAAQ,gBAAe;4EAAQ,aAAa;4EAAG,GAAE;;;;;;;;;;;oEACjE;;;;;;;0EAIR,8OAAC;gEACC,SAAS;gEACT,WAAU;;kFAEV,8OAAC;wEAAI,OAAM;wEAA6B,WAAU;wEAAe,MAAK;wEAAO,SAAQ;wEAAY,QAAO;kFACtG,cAAA,8OAAC;4EAAK,eAAc;4EAAQ,gBAAe;4EAAQ,aAAa;4EAAG,GAAE;;;;;;;;;;;oEACjE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAWnB,kCACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCACZ,gBAAgB,GAAG,CAAC,CAAC,qBACpB,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,SAAS,IAAM,oBAAoB;oCACnC,WAAW,CAAC,6EAA6E,EACvF,cAAc,KAAK,IAAI,IACnB,2BACA,oDACJ;;wCAED,KAAK,IAAI;sDACV,8OAAC;;8DACC,8OAAC;oDAAE,WAAU;8DAAe,KAAK,IAAI;;;;;;8DACrC,8OAAC;oDAAE,WAAU;8DAA4B,KAAK,WAAW;;;;;;;;;;;;;mCAZtD,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsBhC", "debugId": null}}, {"offset": {"line": 687, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/BlocksConnect/web-panel-blocksconnect/src/services/api.ts"], "sourcesContent": ["'use client';\n\nconst API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:5000/api';\n\n// Configuration\nconst MAX_RETRY_ATTEMPTS = 3;\nconst INITIAL_RETRY_DELAY = 1000; // 1 second\nconst AUTH_ERROR_EVENT = 'auth-error'; // Custom event name for auth errors\n\nexport interface ServerProperties {\n  [key: string]: string;\n}\n\nexport interface PlayerInfo {\n  online: number;\n  max: number;\n  list: string[];\n}\n\nexport interface Server {\n  id: string;\n  name: string;\n  port: number;\n  version: string;\n  memory: string;\n  status: string;\n  backup?: boolean;\n  container_id?: string;\n  properties?: ServerProperties;\n  players?: PlayerInfo;\n}\n\nexport interface ServerFormData {\n  name: string;\n  port: number;\n  version: string;\n  type: string;\n  memory: string;\n  eulaAccepted?: boolean;\n  eulaAcceptedAt?: string;\n}\n\n// Error class for authentication errors to distinguish them from other errors\nexport class AuthenticationError extends Error {\n  status: number;\n\n  constructor(message: string, status: number = 401) {\n    super(message);\n    this.name = 'AuthenticationError';\n    this.status = status;\n  }\n}\n\n// Helper function to emit auth error events for app-wide notification\nexport function emitAuthErrorEvent(error: AuthenticationError): void {\n  if (typeof window !== 'undefined') {\n    const event = new CustomEvent(AUTH_ERROR_EVENT, {\n      detail: { message: error.message, status: error.status }\n    });\n    window.dispatchEvent(event);\n  }\n}\n\n// Setup listener for auth error events\nexport function setupAuthErrorListener(callback: (error: { message: string, status: number }) => void): () => void {\n  if (typeof window === 'undefined') return () => {};\n\n  const listener = (event: Event) => {\n    const customEvent = event as CustomEvent;\n    callback(customEvent.detail);\n  };\n\n  window.addEventListener(AUTH_ERROR_EVENT, listener);\n  return () => window.removeEventListener(AUTH_ERROR_EVENT, listener);\n}\n\n// Helper function to get authentication headers from a token\nexport function buildAuthHeaders(token: string | null): HeadersInit {\n  const headers: HeadersInit = {\n    'Content-Type': 'application/json',\n    'Accept': 'application/json',\n  };\n  if (token) {\n    headers['Authorization'] = `Bearer ${token}`;\n  }\n  return headers;\n}\n\n// Simple fetch wrapper (no hooks)\nexport async function authenticatedFetch<T>(\n  url: string,\n  options: RequestInit\n): Promise<T> {\n  const response = await fetch(url, options);\n  if (response.ok) {\n    if (response.headers.get('Content-Type')?.includes('application/json')) {\n      return await response.json() as T;\n    }\n    return response as unknown as T;\n  }\n  // Handle authentication errors (401 Unauthorized)\n  if (response.status === 401) {\n    const errorData = await response.json().catch(() => ({ error: 'Authentication failed' }));\n    const authError = new AuthenticationError(\n      errorData.error || `Authentication failed: ${response.status} ${response.statusText}`,\n      response.status\n    );\n    emitAuthErrorEvent(authError);\n    throw authError;\n  }\n  // Handle other error responses\n  const errorData = await response.json().catch(() => ({ error: `Request failed: ${response.status} ${response.statusText}` }));\n  throw new Error(errorData.error || `Request failed: ${response.status} ${response.statusText}`);\n}\n\nexport async function getServers(token: string | null): Promise<Server[]> {\n  try {\n    const headers = buildAuthHeaders(token);\n    return await authenticatedFetch<Server[]>(`${API_URL}/minecraft/servers`, {\n      method: 'GET',\n      headers,\n      cache: 'no-store',\n    });\n  } catch (error) {\n    console.error('Error fetching servers:', error);\n    if (error instanceof AuthenticationError) {\n      console.error('Authentication error while fetching servers');\n    }\n    throw error;\n  }\n}\n\nexport async function createServer(data: ServerFormData, token: string | null): Promise<Server> {\n  try {\n    const headers = buildAuthHeaders(token);\n    return await authenticatedFetch<Server>(`${API_URL}/minecraft/servers`, {\n      method: 'POST',\n      headers,\n      body: JSON.stringify(data),\n    });\n  } catch (error) {\n    console.error('Error creating server:', error);\n    if (error instanceof AuthenticationError) {\n      console.error('Authentication error while creating server');\n    }\n    throw error;\n  }\n}\n\nexport async function startServer(serverId: string, token: string | null): Promise<{ status: string; message: string }> {\n  try {\n    const headers = buildAuthHeaders(token);\n    return await authenticatedFetch<{ status: string; message: string }>(`${API_URL}/minecraft/servers/${serverId}/start`, {\n      method: 'POST',\n      headers: {\n        ...headers,\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({}),\n    });\n  } catch (error) {\n    console.error(`Error starting server ${serverId}:`, error);\n    if (error instanceof AuthenticationError) {\n      console.error(`Authentication error while starting server ${serverId}`);\n    }\n    throw error;\n  }\n}\n\nexport async function stopServer(serverId: string, token: string | null): Promise<{ status: string; message: string }> {\n  try {\n    const headers = buildAuthHeaders(token);\n    return await authenticatedFetch<{ status: string; message: string }>(`${API_URL}/minecraft/servers/${serverId}/stop`, {\n      method: 'POST',\n      headers: {\n        ...headers,\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({}),\n    });\n  } catch (error) {\n    console.error(`Error stopping server ${serverId}:`, error);\n    if (error instanceof AuthenticationError) {\n      console.error(`Authentication error while stopping server ${serverId}`);\n    }\n    throw error;\n  }\n}\n\nexport async function deleteServer(serverId: string, token: string | null): Promise<{ message: string }> {\n  try {\n    const headers = buildAuthHeaders(token);\n    return await authenticatedFetch<{ message: string }>(`${API_URL}/minecraft/servers/${serverId}`, {\n      method: 'DELETE',\n      headers: {\n        ...headers,\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({}),\n    });\n  } catch (error) {\n    console.error(`Error deleting server ${serverId}:`, error);\n    if (error instanceof AuthenticationError) {\n      console.error(`Authentication error while deleting server ${serverId}`);\n    }\n    throw error;\n  }\n}\n\nexport async function toggleBackup(serverId: string, token: string | null): Promise<{ message: string }> {\n  try {\n    const headers = buildAuthHeaders(token);\n    return await authenticatedFetch<{ message: string }>(`${API_URL}/minecraft/servers/${serverId}/backup`, {\n      method: 'POST',\n      headers: {\n        ...headers,\n        'Content-Type': 'application/json',\n      },\n      body: JSON.stringify({}),\n    });\n  } catch (error) {\n    console.error(`Error toggling backup for server ${serverId}:`, error);\n    if (error instanceof AuthenticationError) {\n      console.error(`Authentication error while toggling backup for server ${serverId}`);\n    }\n    throw error;\n  }\n}\n\nexport async function downloadBackup(serverId: string, token: string | null): Promise<void> {\n  try {\n    const headers = buildAuthHeaders(token);\n    const response = await authenticatedFetch<Response>(`${API_URL}/minecraft/servers/${serverId}/backup`, {\n      method: 'GET',\n      headers,\n    });\n    if (response instanceof Response) {\n      const blob = await response.blob();\n      const url = window.URL.createObjectURL(blob);\n      const a = document.createElement('a');\n      a.href = url;\n      a.download = `server_${serverId}_backup.zip`;\n      document.body.appendChild(a);\n      a.click();\n      document.body.removeChild(a);\n      window.URL.revokeObjectURL(url);\n    } else {\n      throw new Error('Unexpected response type');\n    }\n  } catch (error) {\n    console.error(`Error downloading backup for server ${serverId}:`, error);\n    if (error instanceof AuthenticationError) {\n      console.error(`Authentication error while downloading backup for server ${serverId}`);\n    }\n    throw error;\n  }\n}\n\nexport async function getServerDetails(serverId: string, token: string | null): Promise<Server> {\n  try {\n    const headers = buildAuthHeaders(token);\n    return await authenticatedFetch<Server>(`${API_URL}/minecraft/servers/${serverId}`, {\n      method: 'GET',\n      headers,\n      cache: 'no-store',\n    });\n  } catch (error) {\n    console.error(`Error fetching server details for ${serverId}:`, error);\n    if (error instanceof AuthenticationError) {\n      console.error(`Authentication error while fetching server details for ${serverId}`);\n    }\n    throw error;\n  }\n}\n\nexport async function getServerLogs(serverId: string, token: string | null): Promise<{ logs: string }> {\n  try {\n    const headers = buildAuthHeaders(token);\n    return await authenticatedFetch<{ logs: string }>(`${API_URL}/minecraft/servers/${serverId}/logs`, {\n      method: 'GET',\n      headers,\n      cache: 'no-store',\n    });\n  } catch (error) {\n    console.error(`Error fetching logs for server ${serverId}:`, error);\n    if (error instanceof AuthenticationError) {\n      console.error(`Authentication error while fetching logs for server ${serverId}`);\n    }\n    throw error;\n  }\n}\n\nexport async function sendServerCommand(serverId: string, command: string, token: string | null): Promise<{ response: string }> {\n  try {\n    const headers = buildAuthHeaders(token);\n    return await authenticatedFetch<{ response: string }>(`${API_URL}/minecraft/servers/${serverId}/command`, {\n      method: 'POST',\n      headers,\n      body: JSON.stringify({ command }),\n    });\n  } catch (error) {\n    console.error(`Error sending command to server ${serverId}:`, error);\n    if (error instanceof AuthenticationError) {\n      console.error(`Authentication error while sending command to server ${serverId}`);\n    }\n    throw error;\n  }\n}\n\nexport interface ServerPropertiesUpdate {\n  properties: { [key: string]: string | boolean | number };\n  memory?: string;\n}\n\nexport async function updateServerProperties(serverId: string, properties: ServerPropertiesUpdate, token: string | null): Promise<{ message: string }> {\n  try {\n    const headers = buildAuthHeaders(token);\n    return await authenticatedFetch<{ message: string }>(`${API_URL}/minecraft/servers/${serverId}/properties`, {\n      method: 'PUT',\n      headers,\n      body: JSON.stringify(properties),\n    });\n  } catch (error) {\n    console.error(`Error updating properties for server ${serverId}:`, error);\n    if (error instanceof AuthenticationError) {\n      console.error(`Authentication error while updating properties for server ${serverId}`);\n    }\n    throw error;\n  }\n}\n\n// Enhanced helper function to handle auth errors at the component level\nexport function handleApiAuthError(error: unknown, onAuthError?: () => void): boolean {\n  if (error instanceof AuthenticationError) {\n    console.error('Authentication error occurred:', error.message);\n\n    // Emit auth error event\n    emitAuthErrorEvent(error);\n\n    // If a callback is provided, execute it\n    if (onAuthError) {\n      onAuthError();\n    }\n\n    return true;\n  }\n  return false;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AAAA;AAEA,MAAM,UAAU,iEAAmC;AAEnD,gBAAgB;AAChB,MAAM,qBAAqB;AAC3B,MAAM,sBAAsB,MAAM,WAAW;AAC7C,MAAM,mBAAmB,cAAc,oCAAoC;AAoCpE,MAAM,4BAA4B;IACvC,OAAe;IAEf,YAAY,OAAe,EAAE,SAAiB,GAAG,CAAE;QACjD,KAAK,CAAC;QACN,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,MAAM,GAAG;IAChB;AACF;AAGO,SAAS,mBAAmB,KAA0B;IAC3D,uCAAmC;;IAKnC;AACF;AAGO,SAAS,uBAAuB,QAA8D;IACnG,wCAAmC,OAAO,KAAO;;IAEjD,MAAM;AAOR;AAGO,SAAS,iBAAiB,KAAoB;IACnD,MAAM,UAAuB;QAC3B,gBAAgB;QAChB,UAAU;IACZ;IACA,IAAI,OAAO;QACT,OAAO,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,OAAO;IAC9C;IACA,OAAO;AACT;AAGO,eAAe,mBACpB,GAAW,EACX,OAAoB;IAEpB,MAAM,WAAW,MAAM,MAAM,KAAK;IAClC,IAAI,SAAS,EAAE,EAAE;QACf,IAAI,SAAS,OAAO,CAAC,GAAG,CAAC,iBAAiB,SAAS,qBAAqB;YACtE,OAAO,MAAM,SAAS,IAAI;QAC5B;QACA,OAAO;IACT;IACA,kDAAkD;IAClD,IAAI,SAAS,MAAM,KAAK,KAAK;QAC3B,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC;gBAAE,OAAO;YAAwB,CAAC;QACvF,MAAM,YAAY,IAAI,oBACpB,UAAU,KAAK,IAAI,CAAC,uBAAuB,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE,EACrF,SAAS,MAAM;QAEjB,mBAAmB;QACnB,MAAM;IACR;IACA,+BAA+B;IAC/B,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC;YAAE,OAAO,CAAC,gBAAgB,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;QAAC,CAAC;IAC3H,MAAM,IAAI,MAAM,UAAU,KAAK,IAAI,CAAC,gBAAgB,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;AAChG;AAEO,eAAe,WAAW,KAAoB;IACnD,IAAI;QACF,MAAM,UAAU,iBAAiB;QACjC,OAAO,MAAM,mBAA6B,GAAG,QAAQ,kBAAkB,CAAC,EAAE;YACxE,QAAQ;YACR;YACA,OAAO;QACT;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,IAAI,iBAAiB,qBAAqB;YACxC,QAAQ,KAAK,CAAC;QAChB;QACA,MAAM;IACR;AACF;AAEO,eAAe,aAAa,IAAoB,EAAE,KAAoB;IAC3E,IAAI;QACF,MAAM,UAAU,iBAAiB;QACjC,OAAO,MAAM,mBAA2B,GAAG,QAAQ,kBAAkB,CAAC,EAAE;YACtE,QAAQ;YACR;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,IAAI,iBAAiB,qBAAqB;YACxC,QAAQ,KAAK,CAAC;QAChB;QACA,MAAM;IACR;AACF;AAEO,eAAe,YAAY,QAAgB,EAAE,KAAoB;IACtE,IAAI;QACF,MAAM,UAAU,iBAAiB;QACjC,OAAO,MAAM,mBAAwD,GAAG,QAAQ,mBAAmB,EAAE,SAAS,MAAM,CAAC,EAAE;YACrH,QAAQ;YACR,SAAS;gBACP,GAAG,OAAO;gBACV,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC,CAAC;QACxB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,sBAAsB,EAAE,SAAS,CAAC,CAAC,EAAE;QACpD,IAAI,iBAAiB,qBAAqB;YACxC,QAAQ,KAAK,CAAC,CAAC,2CAA2C,EAAE,UAAU;QACxE;QACA,MAAM;IACR;AACF;AAEO,eAAe,WAAW,QAAgB,EAAE,KAAoB;IACrE,IAAI;QACF,MAAM,UAAU,iBAAiB;QACjC,OAAO,MAAM,mBAAwD,GAAG,QAAQ,mBAAmB,EAAE,SAAS,KAAK,CAAC,EAAE;YACpH,QAAQ;YACR,SAAS;gBACP,GAAG,OAAO;gBACV,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC,CAAC;QACxB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,sBAAsB,EAAE,SAAS,CAAC,CAAC,EAAE;QACpD,IAAI,iBAAiB,qBAAqB;YACxC,QAAQ,KAAK,CAAC,CAAC,2CAA2C,EAAE,UAAU;QACxE;QACA,MAAM;IACR;AACF;AAEO,eAAe,aAAa,QAAgB,EAAE,KAAoB;IACvE,IAAI;QACF,MAAM,UAAU,iBAAiB;QACjC,OAAO,MAAM,mBAAwC,GAAG,QAAQ,mBAAmB,EAAE,UAAU,EAAE;YAC/F,QAAQ;YACR,SAAS;gBACP,GAAG,OAAO;gBACV,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC,CAAC;QACxB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,sBAAsB,EAAE,SAAS,CAAC,CAAC,EAAE;QACpD,IAAI,iBAAiB,qBAAqB;YACxC,QAAQ,KAAK,CAAC,CAAC,2CAA2C,EAAE,UAAU;QACxE;QACA,MAAM;IACR;AACF;AAEO,eAAe,aAAa,QAAgB,EAAE,KAAoB;IACvE,IAAI;QACF,MAAM,UAAU,iBAAiB;QACjC,OAAO,MAAM,mBAAwC,GAAG,QAAQ,mBAAmB,EAAE,SAAS,OAAO,CAAC,EAAE;YACtG,QAAQ;YACR,SAAS;gBACP,GAAG,OAAO;gBACV,gBAAgB;YAClB;YACA,MAAM,KAAK,SAAS,CAAC,CAAC;QACxB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,iCAAiC,EAAE,SAAS,CAAC,CAAC,EAAE;QAC/D,IAAI,iBAAiB,qBAAqB;YACxC,QAAQ,KAAK,CAAC,CAAC,sDAAsD,EAAE,UAAU;QACnF;QACA,MAAM;IACR;AACF;AAEO,eAAe,eAAe,QAAgB,EAAE,KAAoB;IACzE,IAAI;QACF,MAAM,UAAU,iBAAiB;QACjC,MAAM,WAAW,MAAM,mBAA6B,GAAG,QAAQ,mBAAmB,EAAE,SAAS,OAAO,CAAC,EAAE;YACrG,QAAQ;YACR;QACF;QACA,IAAI,oBAAoB,UAAU;YAChC,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,MAAM,MAAM,OAAO,GAAG,CAAC,eAAe,CAAC;YACvC,MAAM,IAAI,SAAS,aAAa,CAAC;YACjC,EAAE,IAAI,GAAG;YACT,EAAE,QAAQ,GAAG,CAAC,OAAO,EAAE,SAAS,WAAW,CAAC;YAC5C,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,EAAE,KAAK;YACP,SAAS,IAAI,CAAC,WAAW,CAAC;YAC1B,OAAO,GAAG,CAAC,eAAe,CAAC;QAC7B,OAAO;YACL,MAAM,IAAI,MAAM;QAClB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,oCAAoC,EAAE,SAAS,CAAC,CAAC,EAAE;QAClE,IAAI,iBAAiB,qBAAqB;YACxC,QAAQ,KAAK,CAAC,CAAC,yDAAyD,EAAE,UAAU;QACtF;QACA,MAAM;IACR;AACF;AAEO,eAAe,iBAAiB,QAAgB,EAAE,KAAoB;IAC3E,IAAI;QACF,MAAM,UAAU,iBAAiB;QACjC,OAAO,MAAM,mBAA2B,GAAG,QAAQ,mBAAmB,EAAE,UAAU,EAAE;YAClF,QAAQ;YACR;YACA,OAAO;QACT;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,kCAAkC,EAAE,SAAS,CAAC,CAAC,EAAE;QAChE,IAAI,iBAAiB,qBAAqB;YACxC,QAAQ,KAAK,CAAC,CAAC,uDAAuD,EAAE,UAAU;QACpF;QACA,MAAM;IACR;AACF;AAEO,eAAe,cAAc,QAAgB,EAAE,KAAoB;IACxE,IAAI;QACF,MAAM,UAAU,iBAAiB;QACjC,OAAO,MAAM,mBAAqC,GAAG,QAAQ,mBAAmB,EAAE,SAAS,KAAK,CAAC,EAAE;YACjG,QAAQ;YACR;YACA,OAAO;QACT;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,+BAA+B,EAAE,SAAS,CAAC,CAAC,EAAE;QAC7D,IAAI,iBAAiB,qBAAqB;YACxC,QAAQ,KAAK,CAAC,CAAC,oDAAoD,EAAE,UAAU;QACjF;QACA,MAAM;IACR;AACF;AAEO,eAAe,kBAAkB,QAAgB,EAAE,OAAe,EAAE,KAAoB;IAC7F,IAAI;QACF,MAAM,UAAU,iBAAiB;QACjC,OAAO,MAAM,mBAAyC,GAAG,QAAQ,mBAAmB,EAAE,SAAS,QAAQ,CAAC,EAAE;YACxG,QAAQ;YACR;YACA,MAAM,KAAK,SAAS,CAAC;gBAAE;YAAQ;QACjC;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,gCAAgC,EAAE,SAAS,CAAC,CAAC,EAAE;QAC9D,IAAI,iBAAiB,qBAAqB;YACxC,QAAQ,KAAK,CAAC,CAAC,qDAAqD,EAAE,UAAU;QAClF;QACA,MAAM;IACR;AACF;AAOO,eAAe,uBAAuB,QAAgB,EAAE,UAAkC,EAAE,KAAoB;IACrH,IAAI;QACF,MAAM,UAAU,iBAAiB;QACjC,OAAO,MAAM,mBAAwC,GAAG,QAAQ,mBAAmB,EAAE,SAAS,WAAW,CAAC,EAAE;YAC1G,QAAQ;YACR;YACA,MAAM,KAAK,SAAS,CAAC;QACvB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,qCAAqC,EAAE,SAAS,CAAC,CAAC,EAAE;QACnE,IAAI,iBAAiB,qBAAqB;YACxC,QAAQ,KAAK,CAAC,CAAC,0DAA0D,EAAE,UAAU;QACvF;QACA,MAAM;IACR;AACF;AAGO,SAAS,mBAAmB,KAAc,EAAE,WAAwB;IACzE,IAAI,iBAAiB,qBAAqB;QACxC,QAAQ,KAAK,CAAC,kCAAkC,MAAM,OAAO;QAE7D,wBAAwB;QACxB,mBAAmB;QAEnB,wCAAwC;QACxC,IAAI,aAAa;YACf;QACF;QAEA,OAAO;IACT;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 984, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/BlocksConnect/web-panel-blocksconnect/src/components/EulaAcceptanceModal.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\n\ninterface EulaAcceptanceModalProps {\n  isOpen: boolean;\n  onAccept: () => void;\n  onDecline: () => void;\n  serverName: string;\n}\n\nexport default function EulaAcceptanceModal({ isOpen, onAccept, onDecline, serverName }: EulaAcceptanceModalProps) {\n  const [hasReadEula, setHasReadEula] = useState(false);\n  const [eulaAccepted, setEulaAccepted] = useState(false);\n\n  if (!isOpen) return null;\n\n  const handleAccept = () => {\n    if (eulaAccepted) {\n      onAccept();\n    }\n  };\n\n  const handleDecline = () => {\n    setHasReadEula(false);\n    setEulaAccepted(false);\n    onDecline();\n  };\n\n  return (\n    <div className=\"fixed inset-0 z-50 flex items-center justify-center\">\n      {/* Backdrop */}\n      <div className=\"absolute inset-0 bg-black/70 backdrop-blur-sm\" onClick={handleDecline}></div>\n      \n      {/* Modal */}\n      <div className=\"relative bg-gray-900 border border-gray-700 rounded-2xl shadow-2xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden\">\n        {/* Header */}\n        <div className=\"px-6 py-4 border-b border-gray-700 bg-gradient-to-r from-blue-600/20 to-purple-600/20\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h2 className=\"text-2xl font-bold text-white\">Minecraft End User License Agreement</h2>\n              <p className=\"text-gray-300 mt-1\">Required for server: <span className=\"font-semibold text-blue-400\">{serverName}</span></p>\n            </div>\n            <button\n              onClick={handleDecline}\n              className=\"text-gray-400 hover:text-white transition-colors p-2 rounded-lg hover:bg-gray-800\"\n            >\n              <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n              </svg>\n            </button>\n          </div>\n        </div>\n\n        {/* Content */}\n        <div className=\"p-6 overflow-y-auto max-h-[60vh]\">\n          <div className=\"space-y-6\">\n            {/* Important Notice */}\n            <div className=\"bg-yellow-500/10 border border-yellow-500/30 rounded-lg p-4\">\n              <div className=\"flex items-start\">\n                <svg className=\"w-6 h-6 text-yellow-400 mt-0.5 mr-3 flex-shrink-0\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path fillRule=\"evenodd\" d=\"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n                </svg>\n                <div>\n                  <h3 className=\"text-yellow-400 font-semibold\">Important Legal Requirement</h3>\n                  <p className=\"text-gray-300 mt-1\">\n                    By creating a Minecraft server, you must accept Mojang Studios' End User License Agreement. \n                    This is a legal requirement for all Minecraft server operators.\n                  </p>\n                </div>\n              </div>\n            </div>\n\n            {/* EULA Summary */}\n            <div className=\"space-y-4\">\n              <h3 className=\"text-xl font-semibold text-white\">Key Points of the Minecraft EULA:</h3>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div className=\"space-y-3\">\n                  <div className=\"flex items-start\">\n                    <span className=\"text-green-400 mr-2 mt-1\">✓</span>\n                    <span className=\"text-gray-300\">You may run a Minecraft server</span>\n                  </div>\n                  <div className=\"flex items-start\">\n                    <span className=\"text-green-400 mr-2 mt-1\">✓</span>\n                    <span className=\"text-gray-300\">You may charge for access to your server</span>\n                  </div>\n                  <div className=\"flex items-start\">\n                    <span className=\"text-green-400 mr-2 mt-1\">✓</span>\n                    <span className=\"text-gray-300\">You may accept donations</span>\n                  </div>\n                  <div className=\"flex items-start\">\n                    <span className=\"text-green-400 mr-2 mt-1\">✓</span>\n                    <span className=\"text-gray-300\">You may sell cosmetic items</span>\n                  </div>\n                </div>\n                <div className=\"space-y-3\">\n                  <div className=\"flex items-start\">\n                    <span className=\"text-red-400 mr-2 mt-1\">✗</span>\n                    <span className=\"text-gray-300\">No selling gameplay advantages</span>\n                  </div>\n                  <div className=\"flex items-start\">\n                    <span className=\"text-red-400 mr-2 mt-1\">✗</span>\n                    <span className=\"text-gray-300\">No selling in-game currency for real money</span>\n                  </div>\n                  <div className=\"flex items-start\">\n                    <span className=\"text-red-400 mr-2 mt-1\">✗</span>\n                    <span className=\"text-gray-300\">No selling items that affect gameplay</span>\n                  </div>\n                  <div className=\"flex items-start\">\n                    <span className=\"text-red-400 mr-2 mt-1\">✗</span>\n                    <span className=\"text-gray-300\">No redistributing Minecraft</span>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Full EULA Link */}\n            <div className=\"bg-gray-800/50 rounded-lg p-4\">\n              <h4 className=\"text-white font-semibold mb-2\">Read the Complete EULA</h4>\n              <p className=\"text-gray-300 mb-3\">\n                The complete Minecraft End User License Agreement is available on Mojang Studios' official website.\n              </p>\n              <a\n                href=\"https://www.minecraft.net/en-us/eula\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                onClick={() => setHasReadEula(true)}\n                className=\"inline-flex items-center text-blue-400 hover:text-blue-300 transition-colors\"\n              >\n                <svg className=\"w-4 h-4 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14\" />\n                </svg>\n                Read Full EULA on minecraft.net\n              </a>\n            </div>\n\n            {/* Commercial Use Notice */}\n            <div className=\"bg-blue-500/10 border border-blue-500/30 rounded-lg p-4\">\n              <h4 className=\"text-blue-400 font-semibold mb-2\">Commercial Use Guidelines</h4>\n              <p className=\"text-gray-300 text-sm\">\n                If you plan to monetize your server, please carefully review the commercial use guidelines in the full EULA. \n                Mojang Studios has specific rules about what you can and cannot charge for.\n              </p>\n            </div>\n          </div>\n        </div>\n\n        {/* Footer */}\n        <div className=\"px-6 py-4 border-t border-gray-700 bg-gray-800/50\">\n          <div className=\"space-y-4\">\n            {/* Checkbox */}\n            <label className=\"flex items-start space-x-3 cursor-pointer\">\n              <input\n                type=\"checkbox\"\n                checked={eulaAccepted}\n                onChange={(e) => setEulaAccepted(e.target.checked)}\n                className=\"mt-1 h-5 w-5 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500 focus:ring-2\"\n              />\n              <div className=\"text-sm\">\n                <span className=\"text-white font-medium\">\n                  I have read and agree to the Minecraft End User License Agreement\n                </span>\n                <p className=\"text-gray-400 mt-1\">\n                  By checking this box, you confirm that you understand and will comply with all terms of the Minecraft EULA.\n                </p>\n              </div>\n            </label>\n\n            {/* Action Buttons */}\n            <div className=\"flex flex-col sm:flex-row gap-3 sm:justify-end\">\n              <button\n                onClick={handleDecline}\n                className=\"px-6 py-3 border border-gray-600 text-gray-300 rounded-lg hover:bg-gray-800 transition-colors font-semibold\"\n              >\n                Cancel Server Creation\n              </button>\n              <button\n                onClick={handleAccept}\n                disabled={!eulaAccepted}\n                className={`px-6 py-3 rounded-lg font-semibold transition-all ${\n                  eulaAccepted\n                    ? 'bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white hover:scale-105'\n                    : 'bg-gray-700 text-gray-500 cursor-not-allowed'\n                }`}\n              >\n                Accept EULA & Create Server\n              </button>\n            </div>\n\n            {/* Legal Disclaimer */}\n            <div className=\"text-xs text-gray-500 pt-2 border-t border-gray-700\">\n              <p>\n                This EULA acceptance is required by Mojang Studios for all Minecraft server operators. \n                BlocksConnect is not responsible for EULA compliance - server operators must ensure they follow all terms.\n              </p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAWe,SAAS,oBAAoB,EAAE,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,EAA4B;IAC/G,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,eAAe;QACnB,IAAI,cAAc;YAChB;QACF;IACF;IAEA,MAAM,gBAAgB;QACpB,eAAe;QACf,gBAAgB;QAChB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;gBAAgD,SAAS;;;;;;0BAGxE,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAgC;;;;;;sDAC9C,8OAAC;4CAAE,WAAU;;gDAAqB;8DAAqB,8OAAC;oDAAK,WAAU;8DAA+B;;;;;;;;;;;;;;;;;;8CAExG,8OAAC;oCACC,SAAS;oCACT,WAAU;8CAEV,cAAA,8OAAC;wCAAI,WAAU;wCAAU,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDACjE,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO7E,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;gDAAoD,MAAK;gDAAe,SAAQ;0DAC7F,cAAA,8OAAC;oDAAK,UAAS;oDAAU,GAAE;oDAAoN,UAAS;;;;;;;;;;;0DAE1P,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAAgC;;;;;;kEAC9C,8OAAC;wDAAE,WAAU;kEAAqB;;;;;;;;;;;;;;;;;;;;;;;8CASxC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAA2B;;;;;;8EAC3C,8OAAC;oEAAK,WAAU;8EAAgB;;;;;;;;;;;;sEAElC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAA2B;;;;;;8EAC3C,8OAAC;oEAAK,WAAU;8EAAgB;;;;;;;;;;;;sEAElC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAA2B;;;;;;8EAC3C,8OAAC;oEAAK,WAAU;8EAAgB;;;;;;;;;;;;sEAElC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAA2B;;;;;;8EAC3C,8OAAC;oEAAK,WAAU;8EAAgB;;;;;;;;;;;;;;;;;;8DAGpC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAyB;;;;;;8EACzC,8OAAC;oEAAK,WAAU;8EAAgB;;;;;;;;;;;;sEAElC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAyB;;;;;;8EACzC,8OAAC;oEAAK,WAAU;8EAAgB;;;;;;;;;;;;sEAElC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAyB;;;;;;8EACzC,8OAAC;oEAAK,WAAU;8EAAgB;;;;;;;;;;;;sEAElC,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAK,WAAU;8EAAyB;;;;;;8EACzC,8OAAC;oEAAK,WAAU;8EAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAOxC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAgC;;;;;;sDAC9C,8OAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAGlC,8OAAC;4CACC,MAAK;4CACL,QAAO;4CACP,KAAI;4CACJ,SAAS,IAAM,eAAe;4CAC9B,WAAU;;8DAEV,8OAAC;oDAAI,WAAU;oDAAe,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DACtE,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;gDACjE;;;;;;;;;;;;;8CAMV,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAmC;;;;;;sDACjD,8OAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;kCAS3C,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAM,WAAU;;sDACf,8OAAC;4CACC,MAAK;4CACL,SAAS;4CACT,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,OAAO;4CACjD,WAAU;;;;;;sDAEZ,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAK,WAAU;8DAAyB;;;;;;8DAGzC,8OAAC;oDAAE,WAAU;8DAAqB;;;;;;;;;;;;;;;;;;8CAOtC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS;4CACT,WAAU;sDACX;;;;;;sDAGD,8OAAC;4CACC,SAAS;4CACT,UAAU,CAAC;4CACX,WAAW,CAAC,kDAAkD,EAC5D,eACI,wHACA,gDACJ;sDACH;;;;;;;;;;;;8CAMH,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;kDAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB", "debugId": null}}, {"offset": {"line": 1621, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/BlocksConnect/web-panel-blocksconnect/src/components/ServerForm.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { useForm } from 'react-hook-form';\nimport { createServer, ServerFormData } from '../services/api';\nimport { useAuth } from '@/contexts/AuthContext';\nimport EulaAcceptanceModal from './EulaAcceptanceModal';\nimport ModInstallationModal from './ModInstallationModal';\n\n// Define common Minecraft versions\nconst MINECRAFT_VERSIONS = [\n  'latest',\n  '1.21.5',\n  '1.21.4',\n  '1.21.1',\n  '1.20.6',\n  '1.20.4',\n  '1.19.4',\n  '1.19.2',\n  '1.18.2',\n  '1.17.1',\n  '1.16.5',\n  '1.15.2',\n  '1.14.4',\n  '1.12.2'\n];\n\n// Define memory options\nconst MEMORY_OPTIONS = [\n  '1G',\n  '2G',\n  '4G',\n];\n\nconst TYPE_OPTIONS = [\n  { value: 'vanilla', label: 'Vanilla' },\n  { value: 'paper', label: 'Paper' },\n  { value: 'fabric', label: 'Fabric' },\n  { value: 'forge', label: 'Forge' }\n];\n\ninterface ServerFormProps {\n  onServerCreated: () => void;\n}\n\nexport default function ServerForm({ onServerCreated }: ServerFormProps) {\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [showEulaModal, setShowEulaModal] = useState(false);\n  const [showModInstallModal, setShowModInstallModal] = useState(false);\n  const [pendingServerData, setPendingServerData] = useState<ServerFormData | null>(null);\n  const [createdServer, setCreatedServer] = useState<any>(null);\n  const { getToken } = useAuth()\n\n  const { register, handleSubmit, reset, formState: { errors } } = useForm<ServerFormData>({\n    defaultValues: {\n      version: MINECRAFT_VERSIONS[0],\n      memory: '2G',\n      type: TYPE_OPTIONS[0].value // Default to the first server type\n    }\n  });\n\n  const onSubmit = async (data: ServerFormData) => {\n    setError(null);\n    // Store the form data and show EULA modal\n    setPendingServerData(data);\n    setShowEulaModal(true);\n  };\n\n  const handleEulaAccept = async () => {\n    if (!pendingServerData) return;\n\n    setIsLoading(true);\n    setShowEulaModal(false);\n    setError(null);\n\n    try {\n      const token = await getToken();\n      // Add EULA acceptance to the server data\n      const serverDataWithEula = {\n        ...pendingServerData,\n        eulaAccepted: true,\n        eulaAcceptedAt: new Date().toISOString()\n      };\n      const createdServer = await createServer(serverDataWithEula, token);\n      reset();\n      setPendingServerData(null);\n\n      // Show mod installation popup for Forge/Fabric servers\n      if (pendingServerData.type === 'forge' || pendingServerData.type === 'fabric') {\n        setShowModInstallModal(true);\n        setCreatedServer(createdServer);\n      }\n\n      onServerCreated();\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'An unknown error occurred');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleEulaDecline = () => {\n    setShowEulaModal(false);\n    setPendingServerData(null);\n    setError('Server creation cancelled. EULA acceptance is required to create a Minecraft server.');\n  };\n\n  return (\n    <>\n      <EulaAcceptanceModal\n        isOpen={showEulaModal}\n        onAccept={handleEulaAccept}\n        onDecline={handleEulaDecline}\n        serverName={pendingServerData?.name || 'New Server'}\n      />\n\n      <div className=\"card overflow-hidden\">\n      <div className=\"px-8 py-6 border-b border-white/10 bg-gradient-to-r from-blue-500/10 to-purple-500/10\">\n        <h2 className=\"text-2xl font-bold flex items-center gradient-text\">\n          <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6 mr-3 text-blue-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 6v6m0 0v6m0-6h6m-6 0H6\" />\n          </svg>\n          Create New Server\n        </h2>\n        <p className=\"text-gray-300 mt-2 font-light\">Configure and deploy a new Minecraft server</p>\n      </div>\n\n      <div className=\"p-8\">\n        {error && (\n          <div className=\"bg-red-500/10 border border-red-500/30 text-red-400 px-4 py-3 rounded-lg mb-6 flex items-start backdrop-blur-sm\">\n            <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 mr-2 mt-0.5 flex-shrink-0\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n              <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n            </svg>\n            <span>{error}</span>\n          </div>\n        )}\n\n        <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-6\">\n          <div>\n            <label className=\"block text-gray-200 text-sm font-semibold mb-3\" htmlFor=\"name\">\n              Server Name\n            </label>\n            <input\n              className=\"input-field w-full\"\n              id=\"name\"\n              type=\"text\"\n              placeholder=\"My Minecraft Server\"\n              {...register('name', { required: 'Server name is required' })}\n            />\n            {errors.name && (\n              <p className=\"text-red-400 text-sm mt-2 flex items-center\">\n                <svg className=\"h-4 w-4 mr-1\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n                </svg>\n                {errors.name.message}\n              </p>\n            )}\n          </div>\n\n          <div>\n            <label className=\"block text-gray-200 text-sm font-semibold mb-3\" htmlFor=\"port\">\n              Port\n            </label>\n            <input\n              className=\"input-field w-full\"\n              id=\"port\"\n              type=\"number\"\n              placeholder=\"25565\"\n              {...register('port', {\n                required: 'Port is required',\n                min: { value: 25565, message: 'Port must be at least 25565' },\n                max: { value: 26000, message: 'Port must be at most 26000' },\n                valueAsNumber: true,\n                validate: (value) => !isNaN(value) || 'Must be a valid number'\n              })}\n            />\n            {errors.port && (\n              <p className=\"text-red-400 text-sm mt-2 flex items-center\">\n                <svg className=\"h-4 w-4 mr-1\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n                </svg>\n                {errors.port.message}\n              </p>\n            )}\n          </div>\n\n          <div>\n            <label className=\"block text-gray-200 text-sm font-semibold mb-3\" htmlFor=\"version\">\n              Minecraft Version\n            </label>\n            <select\n              className=\"input-field w-full\"\n              id=\"version\"\n              {...register('version', { required: 'Version is required' })}\n            >\n              <option value=\"\" disabled>Select a Minecraft version</option>\n              <option value={MINECRAFT_VERSIONS[0]}>{MINECRAFT_VERSIONS[0]} (Latest)</option>\n              {MINECRAFT_VERSIONS.filter(version => version !== MINECRAFT_VERSIONS[0]).map((version) => (\n                <option key={version} value={version}>\n                  {version}\n                </option>\n              ))}\n            </select>\n            {errors.version && (\n              <p className=\"text-red-400 text-sm mt-2 flex items-center\">\n                <svg className=\"h-4 w-4 mr-1\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n                </svg>\n                {errors.version.message}\n              </p>\n            )}\n          </div>\n\n          <div>\n            <label className=\"block text-gray-200 text-sm font-semibold mb-3\" htmlFor=\"type\">\n              Minecraft server software type\n            </label>\n            <select\n              className=\"input-field w-full\"\n              id=\"type\"\n              {...register('type', { required: 'Server type is required' })}\n            >\n              <option value=\"\" disabled>Select server software</option>\n              {TYPE_OPTIONS.map((type) => (\n                <option key={type.value} value={type.value}>\n                  {type.label}\n                </option>\n              ))}\n            </select>\n            {errors.type && (\n              <p className=\"text-red-400 text-sm mt-2 flex items-center\">\n                <svg className=\"h-4 w-4 mr-1\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n                </svg>\n                {errors.type.message}\n              </p>\n            )}\n          </div>\n\n          <div>\n            <label className=\"block text-gray-200 text-sm font-semibold mb-3\" htmlFor=\"memory\">\n              Memory Allocation\n            </label>\n            <select\n              className=\"input-field w-full\"\n              id=\"memory\"\n              {...register('memory', { required: 'Memory is required' })}\n            >\n              <option value=\"\" disabled>Select memory allocation</option>\n              {MEMORY_OPTIONS.map((memory) => (\n                <option key={memory} value={memory}>\n                  {memory}\n                </option>\n              ))}\n            </select>\n            {errors.memory && (\n              <p className=\"text-red-400 text-sm mt-2 flex items-center\">\n                <svg className=\"h-4 w-4 mr-1\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                  <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n                </svg>\n                {errors.memory.message}\n              </p>\n            )}\n          </div>\n\n          <div className=\"pt-4\">\n            <button\n              className=\"btn-primary w-full py-4 text-lg font-semibold disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none\"\n              type=\"submit\"\n              disabled={isLoading}\n            >\n              {isLoading ? (\n                <div className=\"flex items-center justify-center\">\n                  <svg className=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n                    <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                    <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                  </svg>\n                  Creating Server...\n                </div>\n              ) : (\n                <span className=\"flex items-center justify-center\">\n                  <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 mr-2\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                    <path fillRule=\"evenodd\" d=\"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z\" clipRule=\"evenodd\" />\n                  </svg>\n                  Create Server\n                </span>\n              )}\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n    </>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AASA,mCAAmC;AACnC,MAAM,qBAAqB;IACzB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,wBAAwB;AACxB,MAAM,iBAAiB;IACrB;IACA;IACA;CACD;AAED,MAAM,eAAe;IACnB;QAAE,OAAO;QAAW,OAAO;IAAU;IACrC;QAAE,OAAO;QAAS,OAAO;IAAQ;IACjC;QAAE,OAAO;QAAU,OAAO;IAAS;IACnC;QAAE,OAAO;QAAS,OAAO;IAAQ;CAClC;AAMc,SAAS,WAAW,EAAE,eAAe,EAAmB;IACrE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB;IAClF,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IACxD,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAE3B,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE,KAAK,EAAE,WAAW,EAAE,MAAM,EAAE,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAkB;QACvF,eAAe;YACb,SAAS,kBAAkB,CAAC,EAAE;YAC9B,QAAQ;YACR,MAAM,YAAY,CAAC,EAAE,CAAC,KAAK,CAAC,mCAAmC;QACjE;IACF;IAEA,MAAM,WAAW,OAAO;QACtB,SAAS;QACT,0CAA0C;QAC1C,qBAAqB;QACrB,iBAAiB;IACnB;IAEA,MAAM,mBAAmB;QACvB,IAAI,CAAC,mBAAmB;QAExB,aAAa;QACb,iBAAiB;QACjB,SAAS;QAET,IAAI;YACF,MAAM,QAAQ,MAAM;YACpB,yCAAyC;YACzC,MAAM,qBAAqB;gBACzB,GAAG,iBAAiB;gBACpB,cAAc;gBACd,gBAAgB,IAAI,OAAO,WAAW;YACxC;YACA,MAAM,gBAAgB,MAAM,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,oBAAoB;YAC7D;YACA,qBAAqB;YAErB,uDAAuD;YACvD,IAAI,kBAAkB,IAAI,KAAK,WAAW,kBAAkB,IAAI,KAAK,UAAU;gBAC7E,uBAAuB;gBACvB,iBAAiB;YACnB;YAEA;QACF,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,oBAAoB;QACxB,iBAAiB;QACjB,qBAAqB;QACrB,SAAS;IACX;IAEA,qBACE;;0BACE,8OAAC,yIAAA,CAAA,UAAmB;gBAClB,QAAQ;gBACR,UAAU;gBACV,WAAW;gBACX,YAAY,mBAAmB,QAAQ;;;;;;0BAGzC,8OAAC;gBAAI,WAAU;;kCACf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;wCAAI,OAAM;wCAA6B,WAAU;wCAA6B,MAAK;wCAAO,SAAQ;wCAAY,QAAO;kDACpH,cAAA,8OAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;oCACjE;;;;;;;0CAGR,8OAAC;gCAAE,WAAU;0CAAgC;;;;;;;;;;;;kCAG/C,8OAAC;wBAAI,WAAU;;4BACZ,uBACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,OAAM;wCAA6B,WAAU;wCAAoC,SAAQ;wCAAY,MAAK;kDAC7G,cAAA,8OAAC;4CAAK,UAAS;4CAAU,GAAE;4CAAoH,UAAS;;;;;;;;;;;kDAE1J,8OAAC;kDAAM;;;;;;;;;;;;0CAIX,8OAAC;gCAAK,UAAU,aAAa;gCAAW,WAAU;;kDAChD,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;gDAAiD,SAAQ;0DAAO;;;;;;0DAGjF,8OAAC;gDACC,WAAU;gDACV,IAAG;gDACH,MAAK;gDACL,aAAY;gDACX,GAAG,SAAS,QAAQ;oDAAE,UAAU;gDAA0B,EAAE;;;;;;4CAE9D,OAAO,IAAI,kBACV,8OAAC;gDAAE,WAAU;;kEACX,8OAAC;wDAAI,WAAU;wDAAe,MAAK;wDAAe,SAAQ;kEACxD,cAAA,8OAAC;4DAAK,UAAS;4DAAU,GAAE;4DAAoH,UAAS;;;;;;;;;;;oDAEzJ,OAAO,IAAI,CAAC,OAAO;;;;;;;;;;;;;kDAK1B,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;gDAAiD,SAAQ;0DAAO;;;;;;0DAGjF,8OAAC;gDACC,WAAU;gDACV,IAAG;gDACH,MAAK;gDACL,aAAY;gDACX,GAAG,SAAS,QAAQ;oDACnB,UAAU;oDACV,KAAK;wDAAE,OAAO;wDAAO,SAAS;oDAA8B;oDAC5D,KAAK;wDAAE,OAAO;wDAAO,SAAS;oDAA6B;oDAC3D,eAAe;oDACf,UAAU,CAAC,QAAU,CAAC,MAAM,UAAU;gDACxC,EAAE;;;;;;4CAEH,OAAO,IAAI,kBACV,8OAAC;gDAAE,WAAU;;kEACX,8OAAC;wDAAI,WAAU;wDAAe,MAAK;wDAAe,SAAQ;kEACxD,cAAA,8OAAC;4DAAK,UAAS;4DAAU,GAAE;4DAAoH,UAAS;;;;;;;;;;;oDAEzJ,OAAO,IAAI,CAAC,OAAO;;;;;;;;;;;;;kDAK1B,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;gDAAiD,SAAQ;0DAAU;;;;;;0DAGpF,8OAAC;gDACC,WAAU;gDACV,IAAG;gDACF,GAAG,SAAS,WAAW;oDAAE,UAAU;gDAAsB,EAAE;;kEAE5D,8OAAC;wDAAO,OAAM;wDAAG,QAAQ;kEAAC;;;;;;kEAC1B,8OAAC;wDAAO,OAAO,kBAAkB,CAAC,EAAE;;4DAAG,kBAAkB,CAAC,EAAE;4DAAC;;;;;;;oDAC5D,mBAAmB,MAAM,CAAC,CAAA,UAAW,YAAY,kBAAkB,CAAC,EAAE,EAAE,GAAG,CAAC,CAAC,wBAC5E,8OAAC;4DAAqB,OAAO;sEAC1B;2DADU;;;;;;;;;;;4CAKhB,OAAO,OAAO,kBACb,8OAAC;gDAAE,WAAU;;kEACX,8OAAC;wDAAI,WAAU;wDAAe,MAAK;wDAAe,SAAQ;kEACxD,cAAA,8OAAC;4DAAK,UAAS;4DAAU,GAAE;4DAAoH,UAAS;;;;;;;;;;;oDAEzJ,OAAO,OAAO,CAAC,OAAO;;;;;;;;;;;;;kDAK7B,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;gDAAiD,SAAQ;0DAAO;;;;;;0DAGjF,8OAAC;gDACC,WAAU;gDACV,IAAG;gDACF,GAAG,SAAS,QAAQ;oDAAE,UAAU;gDAA0B,EAAE;;kEAE7D,8OAAC;wDAAO,OAAM;wDAAG,QAAQ;kEAAC;;;;;;oDACzB,aAAa,GAAG,CAAC,CAAC,qBACjB,8OAAC;4DAAwB,OAAO,KAAK,KAAK;sEACvC,KAAK,KAAK;2DADA,KAAK,KAAK;;;;;;;;;;;4CAK1B,OAAO,IAAI,kBACV,8OAAC;gDAAE,WAAU;;kEACX,8OAAC;wDAAI,WAAU;wDAAe,MAAK;wDAAe,SAAQ;kEACxD,cAAA,8OAAC;4DAAK,UAAS;4DAAU,GAAE;4DAAoH,UAAS;;;;;;;;;;;oDAEzJ,OAAO,IAAI,CAAC,OAAO;;;;;;;;;;;;;kDAK1B,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;gDAAiD,SAAQ;0DAAS;;;;;;0DAGnF,8OAAC;gDACC,WAAU;gDACV,IAAG;gDACF,GAAG,SAAS,UAAU;oDAAE,UAAU;gDAAqB,EAAE;;kEAE1D,8OAAC;wDAAO,OAAM;wDAAG,QAAQ;kEAAC;;;;;;oDACzB,eAAe,GAAG,CAAC,CAAC,uBACnB,8OAAC;4DAAoB,OAAO;sEACzB;2DADU;;;;;;;;;;;4CAKhB,OAAO,MAAM,kBACZ,8OAAC;gDAAE,WAAU;;kEACX,8OAAC;wDAAI,WAAU;wDAAe,MAAK;wDAAe,SAAQ;kEACxD,cAAA,8OAAC;4DAAK,UAAS;4DAAU,GAAE;4DAAoH,UAAS;;;;;;;;;;;oDAEzJ,OAAO,MAAM,CAAC,OAAO;;;;;;;;;;;;;kDAK5B,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CACC,WAAU;4CACV,MAAK;4CACL,UAAU;sDAET,0BACC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;wDAA6C,OAAM;wDAA6B,MAAK;wDAAO,SAAQ;;0EACjH,8OAAC;gEAAO,WAAU;gEAAa,IAAG;gEAAK,IAAG;gEAAK,GAAE;gEAAK,QAAO;gEAAe,aAAY;;;;;;0EACxF,8OAAC;gEAAK,WAAU;gEAAa,MAAK;gEAAe,GAAE;;;;;;;;;;;;oDAC/C;;;;;;qEAIR,8OAAC;gDAAK,WAAU;;kEACd,8OAAC;wDAAI,OAAM;wDAA6B,WAAU;wDAAe,SAAQ;wDAAY,MAAK;kEACxF,cAAA,8OAAC;4DAAK,UAAS;4DAAU,GAAE;4DAAwF,UAAS;;;;;;;;;;;oDACxH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWxB", "debugId": null}}, {"offset": {"line": 2313, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/BlocksConnect/web-panel-blocksconnect/src/components/ConfirmModal.tsx"], "sourcesContent": ["import React from 'react';\r\n\r\ninterface ConfirmModalProps {\r\n  open: boolean;\r\n  title?: string;\r\n  message: string;\r\n  confirmText?: string;\r\n  cancelText?: string;\r\n  onConfirm: () => void;\r\n  onCancel: () => void;\r\n}\r\n\r\nexport default function ConfirmModal({\r\n  open,\r\n  title = 'Confirm',\r\n  message,\r\n  confirmText = 'Delete',\r\n  cancelText = 'Cancel',\r\n  onConfirm,\r\n  onCancel,\r\n}: ConfirmModalProps) {\r\n  if (!open) return null;\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 z-50 flex items-center justify-center bg-black/60 backdrop-blur-sm\">\r\n      <div className=\"card p-8 max-w-sm w-full shadow-xl animate-fade-in-up\">\r\n        {title && <h3 className=\"text-xl font-bold mb-4 gradient-text\">{title}</h3>}\r\n        <p className=\"text-gray-200 mb-6\">{message}</p>\r\n        <div className=\"flex justify-end gap-3\">\r\n          <button\r\n            className=\"btn-secondary px-6 py-2\"\r\n            type=\"button\"\r\n            onClick={onCancel}\r\n          >\r\n            {cancelText}\r\n          </button>\r\n          <button\r\n            className=\"btn-primary px-6 py-2\"\r\n            type=\"button\"\r\n            onClick={onConfirm}\r\n          >\r\n            {confirmText}\r\n          </button>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAYe,SAAS,aAAa,EACnC,IAAI,EACJ,QAAQ,SAAS,EACjB,OAAO,EACP,cAAc,QAAQ,EACtB,aAAa,QAAQ,EACrB,SAAS,EACT,QAAQ,EACU;IAClB,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;gBACZ,uBAAS,8OAAC;oBAAG,WAAU;8BAAwC;;;;;;8BAChE,8OAAC;oBAAE,WAAU;8BAAsB;;;;;;8BACnC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,WAAU;4BACV,MAAK;4BACL,SAAS;sCAER;;;;;;sCAEH,8OAAC;4BACC,WAAU;4BACV,MAAK;4BACL,SAAS;sCAER;;;;;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}, {"offset": {"line": 2388, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/BlocksConnect/web-panel-blocksconnect/src/components/ServerList.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport ConfirmModal from './ConfirmModal';\nimport { getServers, startServer, stopServer, deleteServer, toggleBackup, downloadBackup, Server } from '../services/api';\nimport { useAuth } from '../contexts/AuthContext';\nimport Link from 'next/link';\n\ninterface ServerListProps {\n  refreshTrigger: number;\n}\n\nexport default function ServerList({ refreshTrigger }: ServerListProps) {\n  const [servers, setServers] = useState<Server[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const { user, isLoading: authLoading, getToken } = useAuth();\n\n  useEffect(() => {\n    const fetchServers = async () => {\n      if (authLoading || !user) return;\n      setIsLoading(true);\n      setError(null);\n      try {\n        const token = await getToken();\n        const data = await getServers(token);\n        setServers(data);\n      } catch (err) {\n        setError(err instanceof Error ? err.message : 'An unknown error occurred');\n      } finally {\n        setIsLoading(false);\n      }\n    };\n    fetchServers();\n  }, [refreshTrigger, user, authLoading, getToken]);\n\n  const handleStartServer = async (serverId: string) => {\n    try {\n      const token = await getToken();\n      await startServer(serverId, token);\n      setServers(servers.map(server =>\n        server.id === serverId ? { ...server, status: 'running' } : server\n      ));\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'An unknown error occurred');\n    }\n  };\n\n  const handleStopServer = async (serverId: string) => {\n    try {\n      const token = await getToken();\n      await stopServer(serverId, token);\n      setServers(servers.map(server =>\n        server.id === serverId ? { ...server, status: 'stopped' } : server\n      ));\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'An unknown error occurred');\n    }\n  };\n\n  const [deleteTarget, setDeleteTarget] = useState<Server | null>(null);\n  const [isDeleting, setIsDeleting] = useState(false);\n\n  const handleDeleteServer = (server: Server) => {\n    setDeleteTarget(server);\n  };\n\n  const confirmDeleteServer = async () => {\n    if (!deleteTarget) return;\n    setIsDeleting(true);\n    try {\n      const token = await getToken();\n      await deleteServer(deleteTarget.id, token);\n      setServers(servers.filter(server => server.id !== deleteTarget.id));\n      setDeleteTarget(null);\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'An unknown error occurred');\n    } finally {\n      setIsDeleting(false);\n    }\n  };\n\n  const handleToggleBackup = async (serverId: string) => {\n    try {\n      const token = await getToken();\n      await toggleBackup(serverId, token);\n      setServers(servers.map(server =>\n        server.id === serverId ? { ...server, backup: !server.backup } : server\n      ));\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'An unknown error occurred');\n    }\n  };\n\n  const handleDownloadBackup = async (serverId: string) => {\n    try {\n      const token = await getToken();\n      await downloadBackup(serverId, token);\n    } catch (err) {\n      setError(err instanceof Error ? err.message : 'An unknown error occurred');\n    }\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'running':\n        return 'text-green-400 bg-green-500/20 border-green-500/30';\n      case 'stopped':\n        return 'text-red-400 bg-red-500/20 border-red-500/30';\n      case 'starting':\n        return 'text-yellow-400 bg-yellow-500/20 border-yellow-500/30';\n      default:\n        return 'text-gray-400 bg-gray-500/20 border-gray-500/30';\n    }\n  };\n\n  if (authLoading || isLoading) {\n    return (\n      <div className=\"flex items-center justify-center py-16\">\n        <div className=\"relative\">\n          <div className=\"animate-spin h-12 w-12 border-4 border-blue-500/30 border-t-blue-500 rounded-full\"></div>\n          <div className=\"absolute inset-0 animate-ping h-12 w-12 border-4 border-blue-500/20 rounded-full\"></div>\n        </div>\n        <span className=\"ml-4 text-gray-300 font-medium\">\n          {authLoading ? 'Authenticating...' : 'Loading servers...'}\n        </span>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"bg-red-500/10 border border-red-500/30 text-red-400 px-6 py-4 rounded-lg backdrop-blur-sm\">\n        <div className=\"flex items-center\">\n          <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-6 w-6 mr-3\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n            <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n          </svg>\n          <div>\n            <h3 className=\"font-semibold\">Error Loading Servers</h3>\n            <p className=\"text-sm text-red-300\">{error}</p>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  if (servers.length === 0) {\n    return (\n      <div className=\"text-center py-16\">\n        <div className=\"w-20 h-20 mx-auto mb-6 rounded-full bg-gradient-to-br from-blue-500/20 to-purple-500/20 flex items-center justify-center\">\n          <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-10 w-10 text-blue-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1.5} d=\"M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01\" />\n          </svg>\n        </div>\n        <h3 className=\"text-xl font-semibold text-white mb-2\">No servers found</h3>\n        <p className=\"text-gray-400 font-light\">Create your first Minecraft server to get started.</p>\n      </div>\n    );\n  }\n\n  return (\n    <>\n      <div className=\"space-y-6\">\n        {servers.map((server) => (\n          <div key={server.id} className=\"card p-6 transition-all duration-300\">\n            {/* Server Header */}\n            <div className=\"flex items-center justify-between mb-6\">\n              <div className=\"flex items-center\">\n                <h3 className=\"text-xl font-bold text-white mr-4\">{server.name}</h3>\n                <span className={`px-4 py-2 rounded-full text-sm font-semibold border backdrop-blur-sm ${getStatusColor(server.status)}`}>\n                  {server.status.charAt(0).toUpperCase() + server.status.slice(1)}\n                </span>\n              </div>\n            </div>\n\n            {/* Server Info Grid */}\n            <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm mb-6\">\n              <div className=\"bg-blue-500/10 rounded-lg p-3 border border-blue-500/20\">\n                <span className=\"text-blue-300 font-medium block\">Port</span>\n                <span className=\"text-white font-semibold\">{server.port}</span>\n              </div>\n              <div className=\"bg-purple-500/10 rounded-lg p-3 border border-purple-500/20\">\n                <span className=\"text-purple-300 font-medium block\">Version</span>\n                <span className=\"text-white font-semibold\">{server.version}</span>\n              </div>\n              <div className=\"bg-indigo-500/10 rounded-lg p-3 border border-indigo-500/20\">\n                <span className=\"text-indigo-300 font-medium block\">Memory</span>\n                <span className=\"text-white font-semibold\">{server.memory}</span>\n              </div>\n              <div className=\"bg-emerald-500/10 rounded-lg p-3 border border-emerald-500/20\">\n                <span className=\"text-emerald-300 font-medium block\">Backup</span>\n                <span className=\"text-white font-semibold\">{server.backup ? 'Enabled' : 'Disabled'}</span>\n              </div>\n            </div>\n\n            {/* Action Buttons */}\n            <div className=\"flex flex-wrap items-center gap-2 justify-end border-t border-white/10 pt-4\">\n              {server.status === 'stopped' ? (\n                <button\n                  onClick={() => handleStartServer(server.id)}\n                  className=\"inline-flex items-center px-3 py-2 text-sm font-medium rounded-lg border transition-all duration-200 hover:scale-105 bg-green-500/20 hover:bg-green-500/30 border-green-500/30 hover:border-green-500/50 text-green-400 hover:text-green-300 hover:shadow-lg hover:shadow-green-500/20\"\n                >\n                  <svg className=\"h-4 w-4 mr-1.5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z\" clipRule=\"evenodd\" />\n                  </svg>\n                  Start\n                </button>\n              ) : (\n                <button\n                  onClick={() => handleStopServer(server.id)}\n                  className=\"inline-flex items-center px-3 py-2 text-sm font-medium rounded-lg border transition-all duration-200 hover:scale-105 bg-red-500/20 hover:bg-red-500/30 border-red-500/30 hover:border-red-500/50 text-red-400 hover:text-red-300 hover:shadow-lg hover:shadow-red-500/20\"\n                >\n                  <svg className=\"h-4 w-4 mr-1.5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8 7a1 1 0 00-1 1v4a1 1 0 001 1h4a1 1 0 001-1V8a1 1 0 00-1-1H8z\" clipRule=\"evenodd\" />\n                  </svg>\n                  Stop\n                </button>\n              )}\n\n              <button\n                onClick={() => handleToggleBackup(server.id)}\n                className={`inline-flex items-center px-3 py-2 text-sm font-medium rounded-lg border transition-all duration-200 hover:scale-105 ${\n                  server.backup\n                    ? 'bg-blue-500/20 hover:bg-blue-500/30 border-blue-500/30 hover:border-blue-500/50 text-blue-400 hover:text-blue-300 hover:shadow-lg hover:shadow-blue-500/20'\n                    : 'bg-gray-500/20 hover:bg-gray-500/30 border-gray-500/30 hover:border-gray-500/50 text-gray-400 hover:text-gray-300 hover:shadow-lg hover:shadow-gray-500/20'\n                }`}\n              >\n                <svg className=\"h-4 w-4 mr-1.5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10\" />\n                </svg>\n                {server.backup ? 'Backup On' : 'Backup Off'}\n              </button>\n\n              {server.backup && (\n                <button\n                  onClick={() => handleDownloadBackup(server.id)}\n                  className=\"inline-flex items-center px-3 py-2 text-sm font-medium rounded-lg border transition-all duration-200 hover:scale-105 bg-purple-500/20 hover:bg-purple-500/30 border-purple-500/30 hover:border-purple-500/50 text-purple-400 hover:text-purple-300 hover:shadow-lg hover:shadow-purple-500/20\"\n                >\n                  <svg className=\"h-4 w-4 mr-1.5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n                  </svg>\n                  Download\n                </button>\n              )}\n\n              <Link\n                href={`/minecraft/server/${server.id}`}\n                className=\"inline-flex items-center px-3 py-2 text-sm font-medium rounded-lg border transition-all duration-200 hover:scale-105 bg-indigo-500/20 hover:bg-indigo-500/30 border-indigo-500/30 hover:border-indigo-500/50 text-indigo-400 hover:text-indigo-300 hover:shadow-lg hover:shadow-indigo-500/20\"\n              >\n                <svg className=\"h-4 w-4 mr-1.5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                </svg>\n                Details\n              </Link>\n\n              <button\n                onClick={() => handleDeleteServer(server)}\n                className=\"inline-flex items-center px-3 py-2 text-sm font-medium rounded-lg border transition-all duration-200 hover:scale-105 bg-red-500/20 hover:bg-red-500/30 border-red-500/30 hover:border-red-500/50 text-red-400 hover:text-red-300 hover:shadow-lg hover:shadow-red-500/20\"\n                disabled={isDeleting && deleteTarget?.id === server.id}\n              >\n                <svg className=\"h-4 w-4 mr-1.5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1-1H8a1 1 0 00-1 1v3M4 7h16\" />\n                </svg>\n                {isDeleting && deleteTarget?.id === server.id ? 'Deleting...' : 'Delete'}\n              </button>\n            </div>\n          </div>\n        ))}\n      </div>\n      <ConfirmModal\n        open={!!deleteTarget}\n        title=\"Delete Server\"\n        message={deleteTarget ? `Are you sure you want to delete the server \"${deleteTarget.name}\"? This action cannot be undone.` : ''}\n        confirmText={isDeleting ? 'Deleting...' : 'Delete'}\n        cancelText=\"Cancel\"\n        onConfirm={confirmDeleteServer}\n        onCancel={() => setDeleteTarget(null)}\n      />\n    </>\n  )}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAYe,SAAS,WAAW,EAAE,cAAc,EAAmB;IACpE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACnD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,EAAE,IAAI,EAAE,WAAW,WAAW,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAEzD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,IAAI,eAAe,CAAC,MAAM;YAC1B,aAAa;YACb,SAAS;YACT,IAAI;gBACF,MAAM,QAAQ,MAAM;gBACpB,MAAM,OAAO,MAAM,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE;gBAC9B,WAAW;YACb,EAAE,OAAO,KAAK;gBACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;YAChD,SAAU;gBACR,aAAa;YACf;QACF;QACA;IACF,GAAG;QAAC;QAAgB;QAAM;QAAa;KAAS;IAEhD,MAAM,oBAAoB,OAAO;QAC/B,IAAI;YACF,MAAM,QAAQ,MAAM;YACpB,MAAM,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE,UAAU;YAC5B,WAAW,QAAQ,GAAG,CAAC,CAAA,SACrB,OAAO,EAAE,KAAK,WAAW;oBAAE,GAAG,MAAM;oBAAE,QAAQ;gBAAU,IAAI;QAEhE,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,MAAM,QAAQ,MAAM;YACpB,MAAM,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,UAAU;YAC3B,WAAW,QAAQ,GAAG,CAAC,CAAA,SACrB,OAAO,EAAE,KAAK,WAAW;oBAAE,GAAG,MAAM;oBAAE,QAAQ;gBAAU,IAAI;QAEhE,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD;IACF;IAEA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAChE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,qBAAqB,CAAC;QAC1B,gBAAgB;IAClB;IAEA,MAAM,sBAAsB;QAC1B,IAAI,CAAC,cAAc;QACnB,cAAc;QACd,IAAI;YACF,MAAM,QAAQ,MAAM;YACpB,MAAM,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,aAAa,EAAE,EAAE;YACpC,WAAW,QAAQ,MAAM,CAAC,CAAA,SAAU,OAAO,EAAE,KAAK,aAAa,EAAE;YACjE,gBAAgB;QAClB,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,qBAAqB,OAAO;QAChC,IAAI;YACF,MAAM,QAAQ,MAAM;YACpB,MAAM,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,UAAU;YAC7B,WAAW,QAAQ,GAAG,CAAC,CAAA,SACrB,OAAO,EAAE,KAAK,WAAW;oBAAE,GAAG,MAAM;oBAAE,QAAQ,CAAC,OAAO,MAAM;gBAAC,IAAI;QAErE,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD;IACF;IAEA,MAAM,uBAAuB,OAAO;QAClC,IAAI;YACF,MAAM,QAAQ,MAAM;YACpB,MAAM,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,UAAU;QACjC,EAAE,OAAO,KAAK;YACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;QAChD;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,IAAI,eAAe,WAAW;QAC5B,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;;;;;;;;;;;;8BAEjB,8OAAC;oBAAK,WAAU;8BACb,cAAc,sBAAsB;;;;;;;;;;;;IAI7C;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,OAAM;wBAA6B,WAAU;wBAAe,SAAQ;wBAAY,MAAK;kCACxF,cAAA,8OAAC;4BAAK,UAAS;4BAAU,GAAE;4BAAoH,UAAS;;;;;;;;;;;kCAE1J,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAgB;;;;;;0CAC9B,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;;;;;;;;;;;;IAK/C;IAEA,IAAI,QAAQ,MAAM,KAAK,GAAG;QACxB,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,OAAM;wBAA6B,WAAU;wBAA0B,MAAK;wBAAO,SAAQ;wBAAY,QAAO;kCACjH,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAK,GAAE;;;;;;;;;;;;;;;;8BAG3E,8OAAC;oBAAG,WAAU;8BAAwC;;;;;;8BACtD,8OAAC;oBAAE,WAAU;8BAA2B;;;;;;;;;;;;IAG9C;IAEA,qBACE;;0BACE,8OAAC;gBAAI,WAAU;0BACZ,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC;wBAAoB,WAAU;;0CAE7B,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAqC,OAAO,IAAI;;;;;;sDAC9D,8OAAC;4CAAK,WAAW,CAAC,qEAAqE,EAAE,eAAe,OAAO,MAAM,GAAG;sDACrH,OAAO,MAAM,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,OAAO,MAAM,CAAC,KAAK,CAAC;;;;;;;;;;;;;;;;;0CAMnE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAkC;;;;;;0DAClD,8OAAC;gDAAK,WAAU;0DAA4B,OAAO,IAAI;;;;;;;;;;;;kDAEzD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAoC;;;;;;0DACpD,8OAAC;gDAAK,WAAU;0DAA4B,OAAO,OAAO;;;;;;;;;;;;kDAE5D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAoC;;;;;;0DACpD,8OAAC;gDAAK,WAAU;0DAA4B,OAAO,MAAM;;;;;;;;;;;;kDAE3D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DAAqC;;;;;;0DACrD,8OAAC;gDAAK,WAAU;0DAA4B,OAAO,MAAM,GAAG,YAAY;;;;;;;;;;;;;;;;;;0CAK5E,8OAAC;gCAAI,WAAU;;oCACZ,OAAO,MAAM,KAAK,0BACjB,8OAAC;wCACC,SAAS,IAAM,kBAAkB,OAAO,EAAE;wCAC1C,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;gDAAiB,MAAK;gDAAe,SAAQ;0DAC1D,cAAA,8OAAC;oDAAK,UAAS;oDAAU,GAAE;oDAA0G,UAAS;;;;;;;;;;;4CAC1I;;;;;;6DAIR,8OAAC;wCACC,SAAS,IAAM,iBAAiB,OAAO,EAAE;wCACzC,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;gDAAiB,MAAK;gDAAe,SAAQ;0DAC1D,cAAA,8OAAC;oDAAK,UAAS;oDAAU,GAAE;oDAAmG,UAAS;;;;;;;;;;;4CACnI;;;;;;;kDAKV,8OAAC;wCACC,SAAS,IAAM,mBAAmB,OAAO,EAAE;wCAC3C,WAAW,CAAC,qHAAqH,EAC/H,OAAO,MAAM,GACT,+JACA,8JACJ;;0DAEF,8OAAC;gDAAI,WAAU;gDAAiB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACxE,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;4CAEtE,OAAO,MAAM,GAAG,cAAc;;;;;;;oCAGhC,OAAO,MAAM,kBACZ,8OAAC;wCACC,SAAS,IAAM,qBAAqB,OAAO,EAAE;wCAC7C,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;gDAAiB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACxE,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;4CACjE;;;;;;;kDAKV,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAM,CAAC,kBAAkB,EAAE,OAAO,EAAE,EAAE;wCACtC,WAAU;;0DAEV,8OAAC;gDAAI,WAAU;gDAAiB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACxE,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;4CACjE;;;;;;;kDAIR,8OAAC;wCACC,SAAS,IAAM,mBAAmB;wCAClC,WAAU;wCACV,UAAU,cAAc,cAAc,OAAO,OAAO,EAAE;;0DAEtD,8OAAC;gDAAI,WAAU;gDAAiB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DACxE,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;4CAEtE,cAAc,cAAc,OAAO,OAAO,EAAE,GAAG,gBAAgB;;;;;;;;;;;;;;uBAnG5D,OAAO,EAAE;;;;;;;;;;0BAyGvB,8OAAC,kIAAA,CAAA,UAAY;gBACX,MAAM,CAAC,CAAC;gBACR,OAAM;gBACN,SAAS,eAAe,CAAC,4CAA4C,EAAE,aAAa,IAAI,CAAC,gCAAgC,CAAC,GAAG;gBAC7H,aAAa,aAAa,gBAAgB;gBAC1C,YAAW;gBACX,WAAW;gBACX,UAAU,IAAM,gBAAgB;;;;;;;;AAGrC", "debugId": null}}, {"offset": {"line": 3036, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Documents/BlocksConnect/web-panel-blocksconnect/src/app/minecraft/dashboard/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport ProtectedRoute from '../../../components/ProtectedRoute';\nimport AdminHeader from '../../../components/Header';\nimport ServerForm from '../../../components/ServerForm';\nimport ServerList from '../../../components/ServerList';\n\nexport default function DashboardPage() {\n  const [refreshTrigger, setRefreshTrigger] = useState(0);\n\n  const handleServerCreated = () => {\n    // Increment the refresh trigger to cause the ServerList to refresh\n    setRefreshTrigger(prev => prev + 1);\n  };\n\n  return (\n    <ProtectedRoute>\n      <div className=\"min-h-screen text-white\">\n        <AdminHeader />\n\n        <main className=\"container mx-auto py-12 px-4 max-w-7xl\">\n          <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8 lg:gap-10\">\n            {/* Server Creation Form */}\n            <div className=\"lg:col-span-1 fade-in-fast\">\n              <ServerForm onServerCreated={handleServerCreated} />\n            </div>\n\n            {/* Server List */}\n            <div className=\"lg:col-span-2 fade-in-fast\">\n              <div className=\"card overflow-hidden\">\n                <div className=\"px-6 lg:px-8 py-6 border-b border-white/10 bg-gradient-to-r from-blue-500/10 to-purple-500/10\">\n                  <h2 className=\"text-xl lg:text-2xl font-bold flex items-center gradient-text\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 lg:h-6 lg:w-6 mr-3 text-blue-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2m-2-4h.01M17 16h.01\" />\n                    </svg>\n                    Your Minecraft Servers\n                  </h2>\n                  <p className=\"text-gray-300 mt-2 font-light text-sm lg:text-base\">Manage and monitor your server instances</p>\n                </div>\n                <div className=\"p-6 lg:p-8\">\n                  <ServerList refreshTrigger={refreshTrigger} />\n                </div>\n              </div>\n            </div>\n          </div>\n        </main>\n\n        <footer className=\"relative mt-20\">\n          <div className=\"absolute inset-0 bg-gradient-to-r from-blue-900/20 to-purple-900/20 backdrop-blur-sm\"></div>\n          <div className=\"relative z-10 container mx-auto text-center py-8 px-4\">\n            <div className=\"border-t border-white/10 pt-8\">\n              <p className=\"text-sm text-gray-300 font-medium\">\n               &copy; {new Date().getFullYear()} BlocksConnect\n              </p>\n            </div>\n          </div>\n        </footer>\n      </div>\n    </ProtectedRoute>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAQe,SAAS;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,MAAM,sBAAsB;QAC1B,mEAAmE;QACnE,kBAAkB,CAAA,OAAQ,OAAO;IACnC;IAEA,qBACE,8OAAC,oIAAA,CAAA,UAAc;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,4HAAA,CAAA,UAAW;;;;;8BAEZ,8OAAC;oBAAK,WAAU;8BACd,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,gIAAA,CAAA,UAAU;oCAAC,iBAAiB;;;;;;;;;;;0CAI/B,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;;sEACZ,8OAAC;4DAAI,OAAM;4DAA6B,WAAU;4DAA2C,MAAK;4DAAO,SAAQ;4DAAY,QAAO;sEAClI,cAAA,8OAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;wDACjE;;;;;;;8DAGR,8OAAC;oDAAE,WAAU;8DAAqD;;;;;;;;;;;;sDAEpE,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,gIAAA,CAAA,UAAU;gDAAC,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAOtC,8OAAC;oBAAO,WAAU;;sCAChB,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;;wCAAoC;wCACxC,IAAI,OAAO,WAAW;wCAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhD", "debugId": null}}]}