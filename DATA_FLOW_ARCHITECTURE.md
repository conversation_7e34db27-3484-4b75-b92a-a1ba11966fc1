# BlocksConnect Data Flow Architecture

This document explains how BlocksConnect collects and displays server data, with and without mod integration.

## 📊 Data Collection Methods

### Method 1: Docker + File System (Default)
**Used when**: No mod/plugin is installed
**Data Sources**:
- Docker container statistics (CPU, memory, network)
- Server file system (world size, player files)
- RCON commands (online players, server commands)
- Server log files (basic events)

**Limitations**:
- ❌ No real-time player events
- ❌ Limited player statistics
- ❌ No detailed game state tracking
- ❌ Polling-based updates (slower)
- ❌ Basic server performance metrics only

### Method 2: Mod/Plugin Integration (Enhanced)
**Used when**: BlocksConnect Forge/Fabric mod is installed
**Data Sources**:
- Real-time HTTP API calls from mod to backend
- Direct game event hooks (player join/leave, deaths, etc.)
- Live player data (position, health, experience, etc.)
- Server performance metrics from within the game

**Benefits**:
- ✅ Real-time player events
- ✅ Detailed player statistics and positions
- ✅ Live server performance metrics
- ✅ Advanced player management tools
- ✅ Event-driven updates (instant)
- ✅ Comprehensive game state tracking

## 🔄 Data Flow Diagrams

### Without Mod (Basic Flow)
```
┌─────────────────┐    ┌──────────────┐    ┌─────────────┐    ┌─────────────┐
│ Minecraft       │    │ Docker       │    │ API         │    │ Web Panel   │
│ Server          │    │ Container    │    │ Backend     │    │             │
│                 │    │              │    │             │    │             │
│ • Server files  │───▶│ • Stats API  │───▶│ • File      │───▶│ • Basic     │
│ • Logs          │    │ • Logs       │    │   scanning  │    │   stats     │
│ • RCON          │    │ • RCON       │    │ • Polling   │    │ • Limited   │
│                 │    │              │    │             │    │   features  │
└─────────────────┘    └──────────────┘    └─────────────┘    └─────────────┘
```

### With Mod (Enhanced Flow)
```
┌─────────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│ Minecraft       │    │ BlocksConnect│    │ API         │    │ Web Panel   │
│ Server          │    │ Mod/Plugin  │    │ Backend     │    │             │
│                 │    │             │    │             │    │             │
│ • Game events   │───▶│ • Event     │───▶│ • Real-time │───▶│ • Enhanced  │
│ • Player data   │    │   capture   │    │   endpoints │    │   stats     │
│ • Server state  │    │ • HTTP API  │    │ • Database  │    │ • Live      │
│                 │    │   calls     │    │   storage   │    │   monitoring│
└─────────────────┘    └─────────────┘    └─────────────┘    └─────────────┘
```

## 🛠 Implementation Details

### API Endpoints

#### Standard Endpoints (Always Available)
- `GET /api/servers` - List servers
- `GET /api/servers/{id}` - Server details
- `GET /api/servers/{id}/stats` - Server statistics
- `POST /api/servers/{id}/start` - Start server
- `POST /api/servers/{id}/stop` - Stop server

#### Mod Integration Endpoints (Enhanced Data)
- `POST /api/servers/{id}/events` - Receive real-time events from mod
- `PUT /api/servers/{id}/players/{uuid}` - Update player data from mod
- `GET /api/servers/{id}` - Returns `has_mod_integration: true` when mod is active

### Database Schema

#### minecraft_servers table
```sql
CREATE TABLE minecraft_servers (
    id UUID PRIMARY KEY,
    owner_id VARCHAR(255) NOT NULL,
    name VARCHAR(255) NOT NULL,
    port INTEGER NOT NULL,
    version VARCHAR(50) NOT NULL,
    server_type VARCHAR(50) DEFAULT 'vanilla',
    memory VARCHAR(10) DEFAULT '2G',
    status VARCHAR(20) DEFAULT 'stopped',
    container_id VARCHAR(255),
    server_config JSONB,
    backup_enabled BOOLEAN DEFAULT FALSE,
    has_mod_integration BOOLEAN DEFAULT FALSE,  -- NEW
    mod_last_seen TIMESTAMP,                    -- NEW
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Mod Integration Detection

The system automatically detects when a mod is installed:

1. **Mod sends first API call** → `has_mod_integration = true`
2. **Each API call updates** → `mod_last_seen = CURRENT_TIMESTAMP`
3. **Web panel shows enhanced features** when `has_mod_integration = true`
4. **Statistics prioritize mod data** over file system data

### User Experience Flow

#### Server Creation
1. User creates Forge/Fabric server
2. **Popup appears**: "Install BlocksConnect mod for enhanced statistics"
3. Popup provides:
   - Download link for correct mod version
   - Installation instructions
   - Configuration guide with pre-filled server ID

#### Server Management
- **Without mod**: Basic stats, file-based player list, limited monitoring
- **With mod**: Real-time events, detailed player data, live performance metrics

## 🔧 Configuration

### Mod Configuration (blocksconnect.json)
```json
{
  "apiBaseUrl": "https://your-panel.com/api",
  "serverToken": "generated-server-token",
  "serverId": "auto-filled-server-id",
  "enablePlayerSync": true,
  "enablePermissionSync": true,
  "enableEventLogging": true,
  "syncIntervalSeconds": 30,
  "debugMode": false
}
```

### API Response Format

#### Server Details (Enhanced)
```json
{
  "id": "server-uuid",
  "name": "My Server",
  "status": "running",
  "has_mod_integration": true,
  "mod_last_seen": "2024-01-15T10:30:00Z",
  "stats": {
    "data_source": "mod_integration",
    "players": {
      "online": 5,
      "list": [
        {
          "uuid": "player-uuid",
          "username": "PlayerName",
          "position": { "x": 100, "y": 64, "z": 200 },
          "health": 20,
          "gameMode": 0
        }
      ]
    }
  }
}
```

## 🚀 Benefits Summary

| Feature | Without Mod | With Mod |
|---------|-------------|----------|
| Player Join/Leave Events | ❌ | ✅ Real-time |
| Player Positions | ❌ | ✅ Live tracking |
| Player Health/Stats | ❌ | ✅ Detailed |
| Server Performance | ⚠️ Basic | ✅ Comprehensive |
| Event History | ⚠️ Limited | ✅ Complete |
| Player Management | ⚠️ Basic | ✅ Advanced |
| Update Frequency | 🐌 Polling | ⚡ Real-time |
| Data Accuracy | ⚠️ Approximate | ✅ Precise |

## 🔄 Migration Path

### For Existing Servers
1. Install appropriate mod (Forge/Fabric)
2. Configure with server credentials
3. Restart server
4. System automatically detects mod and enables enhanced features

### For New Servers
1. Create server with Forge/Fabric type
2. Popup suggests mod installation
3. Follow guided setup process
4. Enhanced features available immediately

This architecture ensures backward compatibility while providing significant enhancements for users who install the mod.
